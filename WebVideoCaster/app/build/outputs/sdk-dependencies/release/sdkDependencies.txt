# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.20"
  }
  digests {
    sha256: "(\243[\315\377F\330d\370\0174ja~Hb\204\262\b\321sx\304\031\000\337\261\336\225\251\016l"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.20"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\247\3379\274\3012\177\245\247\352]\312)v\341\033\207\376\352L\250\235\031h\313\273i\024\371\203\315L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.5.4"
  }
  digests {
    sha256: "^\203L\022\327\322&\241\005~np\263A\314\'AJ\372.\235\000\af\v\235\377\354kvs\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\023\363\232\217\v\372\366!\253W\367z\320\"\324\202\265\362\261H\003\245b<\304\375\312.\371\005\312&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.5.4"
  }
  digests {
    sha256: ".\315\005\320\30032S\004\203J6\035>_,\0019[\374]\242@&A\317\316\344\230\032\001\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\002\315Y\253\274\317\225\376\362\202[\244\300\207\022U8\fo[\243\352\352\205\261\211-\342KNkP"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\217x\021\323O\027\216-\346c\231\350\260\375t}\263P*\233\204JN-\224jq\036ue\\\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\254u:\261)\267\v\253y\317\352i\036\326\274\216\302\017\336\'\215\267&\233\224\365\002\'\247\0220."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\0263\335T\024!i\245\006e\200\371\245\200\322f\fy\017\301\266\214.S<6\251\300\370\261\n\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.4.0"
  }
  digests {
    sha256: "C?\353\323CJEfqv\307jd\363\362\005\312c5\246\265D\305\265\325\177%\243\2127RB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.4.0"
  }
  digests {
    sha256: "\355]>\327r\245\373\360\325p\367RoX\\\326\032\030\016`\3717%\204\303(\246\216,\3773u"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\022c(\204\2007-\277\265Mc#N\234\253\372\177\224\261:[\241\b\320lJ\241\227m\273\252\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.5.4"
  }
  digests {
    sha256: "t\a[\177]\340\202\367|\342{\200\270x|Y\254>\310m\nf\277WT7r\021\362\211@r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.5.4"
  }
  digests {
    sha256: "B\2058w^\245U)\271S\373\267\246\000\f\317\335\310\227\"\306\234z0C\032c\017\242\376t\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\327\350_\264\3736\333\025\353\025\023O\376\3106?\332\205\027\356\224J\361D\030\025\023\020)\371`\310"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.5.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.5.4"
  }
  digests {
    sha256: "c\326\263\203 4\346\361\207!9p\364-\202\320\316\023ml`at\2174\360\\\362\272\035Cd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.11.0"
  }
  digests {
    sha256: "\216\257\1779\236\340\224\334\023I\\6\347\324\357G\036\247\357\2129\236My\311kP\375O\202E\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.1.2"
  }
  digests {
    sha256: "\375\237\217\351\035fa\257\312\360\351\311\316\363\vZ\031kM\357[(\243p\361?,%\234&\344\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.4.1"
  }
  digests {
    sha256: "\214\177\335\324*6\261\020\215#\275\236\346\036\227J\260XS-\b\310\302\362\365_\336\255\220OJ\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.4.1"
  }
  digests {
    sha256: "\036\375\357\310\332\317\220<\274\320j3\324\034o\243\242\035\311\261\271\327\230y\205G\337\003V\343\263\252"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cast-framework"
    version: "21.4.0"
  }
  digests {
    sha256: "\234p\370=\036\022\347\354Z\361 t;\0027\314\300t\351\221\302-Az\350\a}e\360\224Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.6.0"
  }
  digests {
    sha256: "\376b\210G\330\371\t\312\217#\326\205\006\037\232v\204\252\f\233\311\301\262\001\304\377n\267\2637(("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.mediarouter"
    artifactId: "mediarouter"
    version: "1.6.0"
  }
  digests {
    sha256: "?\314\257\310X\221\334S\213\203\360_W\327\246\300\034G\234=\200\341\356\265\277u~\023N\235\261I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.palette"
    artifactId: "palette"
    version: "1.0.0"
  }
  digests {
    sha256: "`T\356\001\204\272\017\375f\334\202^\006\016\214\233\362\366\302\352T3l\363\276\361\\WP7\247C"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.0.0"
  }
  digests {
    sha256: "Ni\203\300p;5}\366\361\306\316\254\261\265\337\302\305\000jx\234y\237\354\"\230\262\2653tf"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.3"
  }
  digests {
    sha256: "j\232\016y\033\030\344p\346+U\251\325\236\243\210\346\037\022\\\304\022d\267\253\316\227v\340\271\321\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.3"
  }
  digests {
    sha256: "\3012\321\'\323\312\343\v\2553\354X\256\232\0069Ck/\177\2627\344B\277\372\006\222\267-\205\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.0.0"
  }
  digests {
    sha256: "U\301wtg\220\032-9\2372R8LIv(J\243_\335\375Y\225Fm\276\254\264\237\235\326"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.1"
  }
  digests {
    sha256: "\361\006\333H\306\314\372\216\023\025\247\255\304J\354\320/\3675^\263\372}\315\313\250\302\203\250\353\026\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cast"
    version: "21.4.0"
  }
  digests {
    sha256: "\031l\365@N\312c]\330\367\023\264\254?T\267E\347\250l\327)i\243\366\325}\246.rA\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-flags"
    version: "18.0.1"
  }
  digests {
    sha256: "\221I\216\006\305 \263\221,$~.\354%\306\'f>>\346\305\345\253\261]\322\275\256yA\035\326"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jupnp"
    artifactId: "org.jupnp"
    version: "2.7.1"
  }
  digests {
    sha256: "\002\371\363Q\226\217X\021:\241\230{>\357\256\244\352\021\031\005\\\004N\021\265\211?\373\215\032\027\036"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jupnp"
    artifactId: "org.jupnp.support"
    version: "2.7.1"
  }
  digests {
    sha256: "@\\x\361\033r\177gk\336\201\307\2060\247\232\032>\"\201\364\213B<F\336\253z\312\370-\024"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.nanohttpd"
    artifactId: "nanohttpd"
    version: "2.3.1"
  }
  digests {
    sha256: "\336\206LG\201\201W\024\032$\311\254\263m\360\304}{\361[\177\364\214\220a\017>\264\345\337\016X"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 6
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 12
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
}
library_dependencies {
  library_index: 14
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 15
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 3
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 17
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 23
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 24
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 32
}
library_dependencies {
  library_index: 25
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 32
}
library_dependencies {
  library_index: 26
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 22
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 29
}
library_dependencies {
  library_index: 31
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 33
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 12
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 71
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 80
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 71
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 35
  library_dep_index: 29
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 38
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 38
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 41
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 40
  library_dep_index: 6
  library_dep_index: 47
  library_dep_index: 10
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 54
  library_dep_index: 50
  library_dep_index: 8
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 35
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 50
  library_dep_index: 63
}
library_dependencies {
  library_index: 47
  library_dep_index: 8
}
library_dependencies {
  library_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 45
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 54
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 54
  library_dep_index: 50
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 48
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 50
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 48
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 50
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 50
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 19
  library_dep_index: 45
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 50
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 58
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 54
  library_dep_index: 50
}
library_dependencies {
  library_index: 62
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 65
  library_dep_index: 69
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 50
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 2
  library_dep_index: 69
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 6
  library_dep_index: 67
  library_dep_index: 69
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 48
  library_dep_index: 50
  library_dep_index: 2
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 54
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 19
  library_dep_index: 65
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
  library_dep_index: 67
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 50
  library_dep_index: 8
  library_dep_index: 2
  library_dep_index: 63
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 37
}
library_dependencies {
  library_index: 72
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 73
  library_dep_index: 72
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 74
  library_dep_index: 6
}
library_dependencies {
  library_index: 75
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 77
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 38
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 9
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 76
}
library_dependencies {
  library_index: 79
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 27
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 37
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 8
  library_dep_index: 75
  library_dep_index: 88
  library_dep_index: 9
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 93
  library_dep_index: 80
  library_dep_index: 94
  library_dep_index: 72
  library_dep_index: 95
}
library_dependencies {
  library_index: 82
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
}
library_dependencies {
  library_index: 85
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 76
  library_dep_index: 10
}
library_dependencies {
  library_index: 86
  library_dep_index: 37
  library_dep_index: 8
  library_dep_index: 87
}
library_dependencies {
  library_index: 88
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 89
}
library_dependencies {
  library_index: 89
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 90
  library_dep_index: 79
  library_dep_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 90
  library_dep_index: 6
}
library_dependencies {
  library_index: 91
  library_dep_index: 6
}
library_dependencies {
  library_index: 92
  library_dep_index: 6
}
library_dependencies {
  library_index: 93
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 76
  library_dep_index: 10
}
library_dependencies {
  library_index: 94
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 95
  library_dep_index: 6
  library_dep_index: 77
  library_dep_index: 93
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 96
  library_dep_index: 39
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 50
  library_dep_index: 22
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 2
}
library_dependencies {
  library_index: 97
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 65
  library_dep_index: 63
  library_dep_index: 41
  library_dep_index: 50
  library_dep_index: 2
  library_dep_index: 97
}
library_dependencies {
  library_index: 99
  library_dep_index: 37
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 8
  library_dep_index: 77
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 93
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 113
  library_dep_index: 112
  library_dep_index: 12
}
library_dependencies {
  library_index: 100
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
}
library_dependencies {
  library_index: 101
  library_dep_index: 9
  library_dep_index: 37
  library_dep_index: 71
  library_dep_index: 8
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 93
  library_dep_index: 12
}
library_dependencies {
  library_index: 102
  library_dep_index: 8
  library_dep_index: 89
}
library_dependencies {
  library_index: 103
  library_dep_index: 6
}
library_dependencies {
  library_index: 104
  library_dep_index: 6
  library_dep_index: 103
  library_dep_index: 105
  library_dep_index: 106
  library_dep_index: 109
}
library_dependencies {
  library_index: 105
  library_dep_index: 6
  library_dep_index: 103
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 106
  library_dep_index: 6
}
library_dependencies {
  library_index: 107
  library_dep_index: 6
  library_dep_index: 106
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
  library_dep_index: 106
}
library_dependencies {
  library_index: 110
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 77
  library_dep_index: 111
  library_dep_index: 112
}
library_dependencies {
  library_index: 111
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 77
}
library_dependencies {
  library_index: 112
  library_dep_index: 111
}
library_dependencies {
  library_index: 113
  library_dep_index: 8
  library_dep_index: 101
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 114
  library_dep_index: 112
}
library_dependencies {
  library_index: 114
  library_dep_index: 110
  library_dep_index: 111
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 37
  dependency_index: 81
  dependency_index: 45
  dependency_index: 96
  dependency_index: 60
  dependency_index: 26
  dependency_index: 39
  dependency_index: 99
  dependency_index: 101
  dependency_index: 115
  dependency_index: 116
  dependency_index: 117
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
