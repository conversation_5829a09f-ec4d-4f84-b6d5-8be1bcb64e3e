-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:2:1-38:12
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:2:1-38:12
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:2:1-38:12
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:2:1-38:12
MERGED from [com.google.android.material:material:1.11.0] /Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:2:1-28:12
MERGED from [com.google.android.gms:play-services-cast:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.mediarouter:mediarouter:1.6.0] /Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.9/transforms/da7a8ba5b6cf7beb26c2ce521ee4b417/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e8201db57eb291eace6fd6b21d50e77d/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-flags:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/33b6447230393c3ff0aa742f0d5b466d/transformed/jetified-play-services-flags-18.0.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.9/transforms/ee222b22060dc5f571f40e86de14708c/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/b81511212cfc9f48175ec95256712d0c/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/672faa678a9f1e8cd9802c4af4d6cbf2/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/944a51849acf6b8a33e4c46439086f48/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.9/transforms/ebfcf924deaba69ee263b8cb9247b341/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/96e80ff796fca669540e59e2b777bfeb/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/4a3c24bd55ae2fab52c04b7fd3bf197b/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/a390b088c20f6f3307060a2ecee33876/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/8a49a4e061d95551802fa45cfd8e969f/transformed/jetified-activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/47eb938e0da75f94241bf45d90144e98/transformed/jetified-activity-compose-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple:1.4.1] /Users/<USER>/.gradle/caches/8.9/transforms/aa4244aebcdf839b597c598a4ca3cd99/transformed/jetified-material-ripple-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.1] /Users/<USER>/.gradle/caches/8.9/transforms/331372e0f38533ce2cfabf9e1d9e1d84/transformed/jetified-material-icons-core-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/dc325bc5472c38e8522b454fce0cf047/transformed/jetified-animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/73124378ade76c11d2afb77656920763/transformed/jetified-animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/afdb439afded8443d9ae353dfb774cab/transformed/jetified-foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/64608746c3ff9624fa99ecb6cb1429ed/transformed/jetified-foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/75d97b3dc8f545dbbc97571e1ca8e2f9/transformed/jetified-ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/d533644e31a146bc7e6c213c86da85ec/transformed/jetified-ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/293bb6b66f46252c40050a333d68edea/transformed/jetified-ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/ff2ac332600eeb8104dea25c838af606/transformed/jetified-ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/f95af31094110e4b212b44f472b6902f/transformed/jetified-ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/3dfaed9901eb1f2d4a71c401555caaf7/transformed/jetified-ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.6.0] /Users/<USER>/.gradle/caches/8.9/transforms/696d7ec42970f7c47e825a10ccca99ca/transformed/media-1.6.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/636156b63378b8385d6295ddbcd917d0/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.palette:palette:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/c6c8a149bec494813ab0a6933800fe3b/transformed/palette-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/c4271e0011cd633a4cfed4aefcbe8da9/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/6e373910180ecc499c4cba2cc7958c36/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/379e10c6a2ab255f09c739262032f7f3/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/b49c4ce08ce2b8d95f30ec8ab1d1b1bb/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e20b22f26a336ba1aade959c4c7258be/transformed/jetified-autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/5d2ab0f604ac0e2d0cdecc2f6e5534e0/transformed/jetified-emoji2-views-helper-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.9/transforms/e4deb428621c5224ae59132906665ef7/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.9/transforms/c3d51f584632b9a7666f8b6ed78a6331/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/f55d00b0d0d0c1786d8af86fdf7ad602/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/d79e2440f0340e360148a94b90906480/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/4b95a194827e7f73a23c8574950f6b3e/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/23138fc38fa20c8c93e58f9de952ab88/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/b1c55e20baaf2b63e5935b6f6e461b1c/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/275eb027f39dfb89e7ad38cb505cb864/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/099eff3b50696528943bd87848ddf446/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/b82337f599ebf0a0b3361e83396cc6e1/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/a3f1d2edbe65934dfc46afc0cea315bd/transformed/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/c4d635458670a9b028ffcf3be9bff09d/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/9ae346bbf2a17bbcdc3895522114d875/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/c65d2c4606ef016bc8e3e56ad27eb943/transformed/jetified-runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1671d930e985436090841b427d11d32f/transformed/jetified-runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/b0bc7b3992b71085dfaa8e12e93ea0c6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/33e6c0dc262202b399de8d22d4841987/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/9e704535f3897deba48f602c32a5b71b/transformed/jetified-transport-api-3.0.0/AndroidManifest.xml:15:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e58b6f65eff4ddf626c2737ff41a39bd/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.9/transforms/d180a73b7464ea9b85cb84019998d00f/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/a0b64b9874b1daa4c457675b104702b8/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/5ca5fc8016562829660423478fa6f454/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e65010cfb503f4435d66ab95983ad30c/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/68146a0a5c17126926b9da7c81135d7f/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/4c31d476d55a656da9a23569fdd589c1/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:26:5-67
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:5-76
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:22-73
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:5-79
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:10:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:22:5-79
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:5-80
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:22-77
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:5-77
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:12:5-77
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:12:5-77
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:22-74
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:5-80
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:22-77
application
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:5-36:19
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:5-36:19
MERGED from [com.google.android.material:material:1.11.0] /Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] /Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:18:5-26:19
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:18:5-26:19
MERGED from [com.google.android.gms:play-services-cast:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-cast:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:14:9-35
	android:label
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:13:9-41
	android:allowBackup
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:9-35
	android:theme
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:15:9-52
meta-data#com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:17:9-19:75
	android:value
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:19:13-72
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:18:13-93
activity#com.webvideocaster.ui.MainActivity
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:21:9-24:20
	android:exported
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:23:13-37
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:22:13-44
activity#com.webvideocaster.ui.BrowserActivity
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:9-31:20
	android:exported
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:26:13-36
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:19-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:27:13-30:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:17-69
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:17-77
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:27-74
activity#com.webvideocaster.ui.LocalVideoActivity
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:9-59
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:19-56
service#com.webvideocaster.cast.UpnpService
ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:34:9-35:39
	android:exported
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:13-37
	android:name
		ADDED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:34:18-50
uses-sdk
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
MERGED from [com.google.android.material:material:1.11.0] /Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] /Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-cast:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cast:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.mediarouter:mediarouter:1.6.0] /Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.mediarouter:mediarouter:1.6.0] /Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.9/transforms/da7a8ba5b6cf7beb26c2ce521ee4b417/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.9/transforms/da7a8ba5b6cf7beb26c2ce521ee4b417/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e8201db57eb291eace6fd6b21d50e77d/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e8201db57eb291eace6fd6b21d50e77d/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-flags:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/33b6447230393c3ff0aa742f0d5b466d/transformed/jetified-play-services-flags-18.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/33b6447230393c3ff0aa742f0d5b466d/transformed/jetified-play-services-flags-18.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.9/transforms/ee222b22060dc5f571f40e86de14708c/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.9/transforms/ee222b22060dc5f571f40e86de14708c/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/b81511212cfc9f48175ec95256712d0c/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/b81511212cfc9f48175ec95256712d0c/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/672faa678a9f1e8cd9802c4af4d6cbf2/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/672faa678a9f1e8cd9802c4af4d6cbf2/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/944a51849acf6b8a33e4c46439086f48/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/944a51849acf6b8a33e4c46439086f48/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.9/transforms/ebfcf924deaba69ee263b8cb9247b341/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.9/transforms/ebfcf924deaba69ee263b8cb9247b341/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/96e80ff796fca669540e59e2b777bfeb/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/96e80ff796fca669540e59e2b777bfeb/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/4a3c24bd55ae2fab52c04b7fd3bf197b/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/4a3c24bd55ae2fab52c04b7fd3bf197b/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/a390b088c20f6f3307060a2ecee33876/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/a390b088c20f6f3307060a2ecee33876/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/8a49a4e061d95551802fa45cfd8e969f/transformed/jetified-activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/8a49a4e061d95551802fa45cfd8e969f/transformed/jetified-activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/47eb938e0da75f94241bf45d90144e98/transformed/jetified-activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.9/transforms/47eb938e0da75f94241bf45d90144e98/transformed/jetified-activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.1] /Users/<USER>/.gradle/caches/8.9/transforms/aa4244aebcdf839b597c598a4ca3cd99/transformed/jetified-material-ripple-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.1] /Users/<USER>/.gradle/caches/8.9/transforms/aa4244aebcdf839b597c598a4ca3cd99/transformed/jetified-material-ripple-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.1] /Users/<USER>/.gradle/caches/8.9/transforms/331372e0f38533ce2cfabf9e1d9e1d84/transformed/jetified-material-icons-core-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.1] /Users/<USER>/.gradle/caches/8.9/transforms/331372e0f38533ce2cfabf9e1d9e1d84/transformed/jetified-material-icons-core-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/dc325bc5472c38e8522b454fce0cf047/transformed/jetified-animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/dc325bc5472c38e8522b454fce0cf047/transformed/jetified-animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/73124378ade76c11d2afb77656920763/transformed/jetified-animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/73124378ade76c11d2afb77656920763/transformed/jetified-animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/afdb439afded8443d9ae353dfb774cab/transformed/jetified-foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/afdb439afded8443d9ae353dfb774cab/transformed/jetified-foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/64608746c3ff9624fa99ecb6cb1429ed/transformed/jetified-foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/64608746c3ff9624fa99ecb6cb1429ed/transformed/jetified-foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/75d97b3dc8f545dbbc97571e1ca8e2f9/transformed/jetified-ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/75d97b3dc8f545dbbc97571e1ca8e2f9/transformed/jetified-ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/d533644e31a146bc7e6c213c86da85ec/transformed/jetified-ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/d533644e31a146bc7e6c213c86da85ec/transformed/jetified-ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/293bb6b66f46252c40050a333d68edea/transformed/jetified-ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/293bb6b66f46252c40050a333d68edea/transformed/jetified-ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/ff2ac332600eeb8104dea25c838af606/transformed/jetified-ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/ff2ac332600eeb8104dea25c838af606/transformed/jetified-ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/f95af31094110e4b212b44f472b6902f/transformed/jetified-ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/f95af31094110e4b212b44f472b6902f/transformed/jetified-ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/3dfaed9901eb1f2d4a71c401555caaf7/transformed/jetified-ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/3dfaed9901eb1f2d4a71c401555caaf7/transformed/jetified-ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.6.0] /Users/<USER>/.gradle/caches/8.9/transforms/696d7ec42970f7c47e825a10ccca99ca/transformed/media-1.6.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] /Users/<USER>/.gradle/caches/8.9/transforms/696d7ec42970f7c47e825a10ccca99ca/transformed/media-1.6.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/636156b63378b8385d6295ddbcd917d0/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/636156b63378b8385d6295ddbcd917d0/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.palette:palette:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/c6c8a149bec494813ab0a6933800fe3b/transformed/palette-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/c6c8a149bec494813ab0a6933800fe3b/transformed/palette-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/c4271e0011cd633a4cfed4aefcbe8da9/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/c4271e0011cd633a4cfed4aefcbe8da9/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/6e373910180ecc499c4cba2cc7958c36/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/6e373910180ecc499c4cba2cc7958c36/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/379e10c6a2ab255f09c739262032f7f3/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/379e10c6a2ab255f09c739262032f7f3/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/b49c4ce08ce2b8d95f30ec8ab1d1b1bb/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.9/transforms/b49c4ce08ce2b8d95f30ec8ab1d1b1bb/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e20b22f26a336ba1aade959c4c7258be/transformed/jetified-autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e20b22f26a336ba1aade959c4c7258be/transformed/jetified-autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/5d2ab0f604ac0e2d0cdecc2f6e5534e0/transformed/jetified-emoji2-views-helper-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/5d2ab0f604ac0e2d0cdecc2f6e5534e0/transformed/jetified-emoji2-views-helper-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.9/transforms/e4deb428621c5224ae59132906665ef7/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.9/transforms/e4deb428621c5224ae59132906665ef7/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.9/transforms/c3d51f584632b9a7666f8b6ed78a6331/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.9/transforms/c3d51f584632b9a7666f8b6ed78a6331/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/f55d00b0d0d0c1786d8af86fdf7ad602/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/f55d00b0d0d0c1786d8af86fdf7ad602/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/d79e2440f0340e360148a94b90906480/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/d79e2440f0340e360148a94b90906480/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/4b95a194827e7f73a23c8574950f6b3e/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/4b95a194827e7f73a23c8574950f6b3e/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/23138fc38fa20c8c93e58f9de952ab88/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/23138fc38fa20c8c93e58f9de952ab88/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/b1c55e20baaf2b63e5935b6f6e461b1c/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/b1c55e20baaf2b63e5935b6f6e461b1c/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/275eb027f39dfb89e7ad38cb505cb864/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/275eb027f39dfb89e7ad38cb505cb864/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/099eff3b50696528943bd87848ddf446/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/099eff3b50696528943bd87848ddf446/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/b82337f599ebf0a0b3361e83396cc6e1/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/b82337f599ebf0a0b3361e83396cc6e1/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/a3f1d2edbe65934dfc46afc0cea315bd/transformed/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/a3f1d2edbe65934dfc46afc0cea315bd/transformed/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/c4d635458670a9b028ffcf3be9bff09d/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/c4d635458670a9b028ffcf3be9bff09d/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/9ae346bbf2a17bbcdc3895522114d875/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/9ae346bbf2a17bbcdc3895522114d875/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/c65d2c4606ef016bc8e3e56ad27eb943/transformed/jetified-runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/c65d2c4606ef016bc8e3e56ad27eb943/transformed/jetified-runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1671d930e985436090841b427d11d32f/transformed/jetified-runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1671d930e985436090841b427d11d32f/transformed/jetified-runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/b0bc7b3992b71085dfaa8e12e93ea0c6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/b0bc7b3992b71085dfaa8e12e93ea0c6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/33e6c0dc262202b399de8d22d4841987/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/33e6c0dc262202b399de8d22d4841987/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/9e704535f3897deba48f602c32a5b71b/transformed/jetified-transport-api-3.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/9e704535f3897deba48f602c32a5b71b/transformed/jetified-transport-api-3.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e58b6f65eff4ddf626c2737ff41a39bd/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e58b6f65eff4ddf626c2737ff41a39bd/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.9/transforms/d180a73b7464ea9b85cb84019998d00f/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.9/transforms/d180a73b7464ea9b85cb84019998d00f/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/a0b64b9874b1daa4c457675b104702b8/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/a0b64b9874b1daa4c457675b104702b8/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/5ca5fc8016562829660423478fa6f454/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/5ca5fc8016562829660423478fa6f454/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e65010cfb503f4435d66ab95983ad30c/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/e65010cfb503f4435d66ab95983ad30c/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/68146a0a5c17126926b9da7c81135d7f/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/68146a0a5c17126926b9da7c81135d7f/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/4c31d476d55a656da9a23569fdd589c1/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/4c31d476d55a656da9a23569fdd589c1/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml
queries
ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:14:5-16:15
package#com.google.android.gms.policy_cast_dynamite
ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:9-79
	android:name
		ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:18-76
receiver#com.google.android.gms.cast.framework.media.MediaIntentReceiver
ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:20:13-91
service#com.google.android.gms.cast.framework.ReconnectionService
ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:23:9-25:40
	android:exported
		ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:24:13-85
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:33:13-132
