/ Header Record For PersistentHashMapValueStorage9 8app/src/main/java/com/webvideocaster/cast/CastManager.ktA @app/src/main/java/com/webvideocaster/cast/CastOptionsProvider.kt? >app/src/main/java/com/webvideocaster/cast/ChromecastManager.kt9 8app/src/main/java/com/webvideocaster/cast/DLNAManager.kt9 8app/src/main/java/com/webvideocaster/cast/UpnpService.kt: 9app/src/main/java/com/webvideocaster/media/VideoParser.kt; :app/src/main/java/com/webvideocaster/media/VideoScanner.kt5 4app/src/main/java/com/webvideocaster/model/Device.kt@ ?app/src/main/java/com/webvideocaster/network/LocalHttpServer.kt; :app/src/main/java/com/webvideocaster/ui/BrowserActivity.kt8 7app/src/main/java/com/webvideocaster/ui/ComposeViews.kt> =app/src/main/java/com/webvideocaster/ui/LocalVideoActivity.kt8 7app/src/main/java/com/webvideocaster/ui/MainActivity.kt@ ?app/src/main/java/com/webvideocaster/utils/PermissionsHelper.kt9 8app/src/main/java/com/webvideocaster/cast/CastManager.kt? >app/src/main/java/com/webvideocaster/cast/ChromecastManager.kt: 9app/src/main/java/com/webvideocaster/media/VideoParser.kt; :app/src/main/java/com/webvideocaster/media/VideoScanner.kt@ ?app/src/main/java/com/webvideocaster/network/LocalHttpServer.kt; :app/src/main/java/com/webvideocaster/ui/BrowserActivity.ktA @app/src/main/java/com/webvideocaster/cast/CastOptionsProvider.kt; :app/src/main/java/com/webvideocaster/ui/BrowserActivity.kt> =app/src/main/java/com/webvideocaster/ui/LocalVideoActivity.kt8 7app/src/main/java/com/webvideocaster/ui/MainActivity.kt8 7app/src/main/java/com/webvideocaster/ui/VideoAdapter.kt