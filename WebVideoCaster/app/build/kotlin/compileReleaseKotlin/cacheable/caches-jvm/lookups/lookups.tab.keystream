  SuppressLint android.annotation  Activity android.app  Service android.app  AlertDialog android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  Button android.app.Activity  CastManager android.app.Activity  ChromecastManager android.app.Activity  Device android.app.Activity  
DeviceType android.app.Activity  DialogInterface android.app.Activity  EditText android.app.Activity  ImageButton android.app.Activity  List android.app.Activity  R android.app.Activity  String android.app.Activity  SuppressLint android.app.Activity  Text android.app.Activity  WebResourceRequest android.app.Activity  WebResourceResponse android.app.Activity  WebView android.app.Activity  
WebViewClient android.app.Activity  detectedVideoUrls android.app.Activity  endsWith android.app.Activity  findViewById android.app.Activity  
isVideoUrl android.app.Activity  listOf android.app.Activity  map android.app.Activity  mutableSetOf android.app.Activity  onCreate android.app.Activity  onPause android.app.Activity  onResume android.app.Activity  
setContent android.app.Activity  setContentView android.app.Activity  showDeviceSelectionDialog android.app.Activity  showVideoSelectionDialog android.app.Activity  
startsWith android.app.Activity  toString android.app.Activity  toTypedArray android.app.Activity  Binder android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  START_STICKY android.app.Service  UpnpService android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  Context android.content  DialogInterface android.content  Intent android.content  AlertDialog android.content.Context  Binder android.content.Context  Boolean android.content.Context  Bundle android.content.Context  Button android.content.Context  CastManager android.content.Context  ChromecastManager android.content.Context  Device android.content.Context  
DeviceType android.content.Context  DialogInterface android.content.Context  EditText android.content.Context  IBinder android.content.Context  ImageButton android.content.Context  Int android.content.Context  Intent android.content.Context  List android.content.Context  R android.content.Context  START_STICKY android.content.Context  String android.content.Context  SuppressLint android.content.Context  Text android.content.Context  UpnpService android.content.Context  WebResourceRequest android.content.Context  WebResourceResponse android.content.Context  WebView android.content.Context  
WebViewClient android.content.Context  detectedVideoUrls android.content.Context  endsWith android.content.Context  findViewById android.content.Context  
isVideoUrl android.content.Context  listOf android.content.Context  map android.content.Context  mutableSetOf android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  onPause android.content.Context  onResume android.content.Context  
setContent android.content.Context  setContentView android.content.Context  showDeviceSelectionDialog android.content.Context  showVideoSelectionDialog android.content.Context  
startsWith android.content.Context  toString android.content.Context  toTypedArray android.content.Context  AlertDialog android.content.ContextWrapper  Binder android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  Button android.content.ContextWrapper  CastManager android.content.ContextWrapper  ChromecastManager android.content.ContextWrapper  Device android.content.ContextWrapper  
DeviceType android.content.ContextWrapper  DialogInterface android.content.ContextWrapper  EditText android.content.ContextWrapper  IBinder android.content.ContextWrapper  ImageButton android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  List android.content.ContextWrapper  R android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  String android.content.ContextWrapper  SuppressLint android.content.ContextWrapper  Text android.content.ContextWrapper  UpnpService android.content.ContextWrapper  WebResourceRequest android.content.ContextWrapper  WebResourceResponse android.content.ContextWrapper  WebView android.content.ContextWrapper  
WebViewClient android.content.ContextWrapper  detectedVideoUrls android.content.ContextWrapper  endsWith android.content.ContextWrapper  findViewById android.content.ContextWrapper  
isVideoUrl android.content.ContextWrapper  listOf android.content.ContextWrapper  map android.content.ContextWrapper  mutableSetOf android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  onPause android.content.ContextWrapper  onResume android.content.ContextWrapper  
setContent android.content.ContextWrapper  setContentView android.content.ContextWrapper  showDeviceSelectionDialog android.content.ContextWrapper  showVideoSelectionDialog android.content.ContextWrapper  
startsWith android.content.ContextWrapper  toString android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  OnClickListener android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  getTOString android.net.Uri  getToString android.net.Uri  toString android.net.Uri  Binder 
android.os  Bundle 
android.os  IBinder 
android.os  UpnpService android.os.Binder  toString android.text.Editable  Log android.util  d android.util.Log  e android.util.Log  View android.view  AlertDialog  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  CastManager  android.view.ContextThemeWrapper  ChromecastManager  android.view.ContextThemeWrapper  Device  android.view.ContextThemeWrapper  
DeviceType  android.view.ContextThemeWrapper  DialogInterface  android.view.ContextThemeWrapper  EditText  android.view.ContextThemeWrapper  ImageButton  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  SuppressLint  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  WebResourceRequest  android.view.ContextThemeWrapper  WebResourceResponse  android.view.ContextThemeWrapper  WebView  android.view.ContextThemeWrapper  
WebViewClient  android.view.ContextThemeWrapper  detectedVideoUrls  android.view.ContextThemeWrapper  endsWith  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  
isVideoUrl  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  mutableSetOf  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  onPause  android.view.ContextThemeWrapper  onResume  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  showDeviceSelectionDialog  android.view.ContextThemeWrapper  showVideoSelectionDialog  android.view.ContextThemeWrapper  
startsWith  android.view.ContextThemeWrapper  toString  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  loadUrl android.view.View  setOnClickListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  loadUrl android.view.ViewGroup  WebResourceRequest android.webkit  WebResourceResponse android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  getURL !android.webkit.WebResourceRequest  getUrl !android.webkit.WebResourceRequest  setUrl !android.webkit.WebResourceRequest  url !android.webkit.WebResourceRequest  databaseEnabled android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  getDATABASEEnabled android.webkit.WebSettings  getDOMStorageEnabled android.webkit.WebSettings  getDatabaseEnabled android.webkit.WebSettings  getDomStorageEnabled android.webkit.WebSettings  getJAVAScriptEnabled android.webkit.WebSettings  getJavaScriptEnabled android.webkit.WebSettings  getLOADWithOverviewMode android.webkit.WebSettings  getLoadWithOverviewMode android.webkit.WebSettings  getUSERAgentString android.webkit.WebSettings  getUSEWideViewPort android.webkit.WebSettings  getUseWideViewPort android.webkit.WebSettings  getUserAgentString android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  setDatabaseEnabled android.webkit.WebSettings  setDomStorageEnabled android.webkit.WebSettings  setJavaScriptEnabled android.webkit.WebSettings  setLoadWithOverviewMode android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  setUseWideViewPort android.webkit.WebSettings  setUserAgentString android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  getSETTINGS android.webkit.WebView  getSettings android.webkit.WebView  getWEBViewClient android.webkit.WebView  getWebViewClient android.webkit.WebView  loadUrl android.webkit.WebView  setSettings android.webkit.WebView  setWebViewClient android.webkit.WebView  settings android.webkit.WebView  
webViewClient android.webkit.WebView  WebResourceRequest android.webkit.WebViewClient  WebResourceResponse android.webkit.WebViewClient  WebView android.webkit.WebViewClient  detectedVideoUrls android.webkit.WebViewClient  
isVideoUrl android.webkit.WebViewClient  shouldInterceptRequest android.webkit.WebViewClient  toString android.webkit.WebViewClient  Button android.widget  EditText android.widget  ImageButton android.widget  loadUrl android.widget.AbsoluteLayout  setOnClickListener android.widget.Button  getTEXT android.widget.EditText  getText android.widget.EditText  setText android.widget.EditText  text android.widget.EditText  setOnClickListener android.widget.ImageButton  setOnClickListener android.widget.ImageView  setOnClickListener android.widget.TextView  ComponentActivity androidx.activity  AlertDialog #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  CastManager #androidx.activity.ComponentActivity  ChromecastManager #androidx.activity.ComponentActivity  Device #androidx.activity.ComponentActivity  
DeviceType #androidx.activity.ComponentActivity  DialogInterface #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  ImageButton #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  SuppressLint #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  WebResourceRequest #androidx.activity.ComponentActivity  WebResourceResponse #androidx.activity.ComponentActivity  WebView #androidx.activity.ComponentActivity  
WebViewClient #androidx.activity.ComponentActivity  detectedVideoUrls #androidx.activity.ComponentActivity  endsWith #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  
isVideoUrl #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  mutableSetOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  onPause #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  showDeviceSelectionDialog #androidx.activity.ComponentActivity  showVideoSelectionDialog #androidx.activity.ComponentActivity  
startsWith #androidx.activity.ComponentActivity  toString #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  setItems *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  AlertDialog (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Button (androidx.appcompat.app.AppCompatActivity  CastManager (androidx.appcompat.app.AppCompatActivity  ChromecastManager (androidx.appcompat.app.AppCompatActivity  Device (androidx.appcompat.app.AppCompatActivity  
DeviceType (androidx.appcompat.app.AppCompatActivity  DialogInterface (androidx.appcompat.app.AppCompatActivity  EditText (androidx.appcompat.app.AppCompatActivity  ImageButton (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  SuppressLint (androidx.appcompat.app.AppCompatActivity  WebResourceRequest (androidx.appcompat.app.AppCompatActivity  WebResourceResponse (androidx.appcompat.app.AppCompatActivity  WebView (androidx.appcompat.app.AppCompatActivity  
WebViewClient (androidx.appcompat.app.AppCompatActivity  detectedVideoUrls (androidx.appcompat.app.AppCompatActivity  endsWith (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  
isVideoUrl (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  map (androidx.appcompat.app.AppCompatActivity  mutableSetOf (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  showDeviceSelectionDialog (androidx.appcompat.app.AppCompatActivity  showVideoSelectionDialog (androidx.appcompat.app.AppCompatActivity  
startsWith (androidx.appcompat.app.AppCompatActivity  toString (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  Text androidx.compose.material3  
Composable androidx.compose.runtime  Preview #androidx.compose.ui.tooling.preview  AlertDialog #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  CastManager #androidx.core.app.ComponentActivity  ChromecastManager #androidx.core.app.ComponentActivity  Device #androidx.core.app.ComponentActivity  
DeviceType #androidx.core.app.ComponentActivity  DialogInterface #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  ImageButton #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  WebResourceRequest #androidx.core.app.ComponentActivity  WebResourceResponse #androidx.core.app.ComponentActivity  WebView #androidx.core.app.ComponentActivity  
WebViewClient #androidx.core.app.ComponentActivity  detectedVideoUrls #androidx.core.app.ComponentActivity  endsWith #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  
isVideoUrl #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  mutableSetOf #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  onPause #androidx.core.app.ComponentActivity  onResume #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  showDeviceSelectionDialog #androidx.core.app.ComponentActivity  showVideoSelectionDialog #androidx.core.app.ComponentActivity  
startsWith #androidx.core.app.ComponentActivity  toString #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  Button &androidx.fragment.app.FragmentActivity  CastManager &androidx.fragment.app.FragmentActivity  ChromecastManager &androidx.fragment.app.FragmentActivity  Device &androidx.fragment.app.FragmentActivity  
DeviceType &androidx.fragment.app.FragmentActivity  DialogInterface &androidx.fragment.app.FragmentActivity  EditText &androidx.fragment.app.FragmentActivity  ImageButton &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  SuppressLint &androidx.fragment.app.FragmentActivity  WebResourceRequest &androidx.fragment.app.FragmentActivity  WebResourceResponse &androidx.fragment.app.FragmentActivity  WebView &androidx.fragment.app.FragmentActivity  
WebViewClient &androidx.fragment.app.FragmentActivity  detectedVideoUrls &androidx.fragment.app.FragmentActivity  endsWith &androidx.fragment.app.FragmentActivity  findViewById &androidx.fragment.app.FragmentActivity  
isVideoUrl &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  map &androidx.fragment.app.FragmentActivity  mutableSetOf &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  showDeviceSelectionDialog &androidx.fragment.app.FragmentActivity  showVideoSelectionDialog &androidx.fragment.app.FragmentActivity  
startsWith &androidx.fragment.app.FragmentActivity  toString &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  MediaRouteSelector androidx.mediarouter.media  MediaRouter androidx.mediarouter.media  Builder -androidx.mediarouter.media.MediaRouteSelector  addControlCategory 5androidx.mediarouter.media.MediaRouteSelector.Builder  build 5androidx.mediarouter.media.MediaRouteSelector.Builder  !CALLBACK_FLAG_PERFORM_ACTIVE_SCAN &androidx.mediarouter.media.MediaRouter  Callback &androidx.mediarouter.media.MediaRouter  	RouteInfo &androidx.mediarouter.media.MediaRouter  addCallback &androidx.mediarouter.media.MediaRouter  getInstance &androidx.mediarouter.media.MediaRouter  	getROUTES &androidx.mediarouter.media.MediaRouter  	getRoutes &androidx.mediarouter.media.MediaRouter  removeCallback &androidx.mediarouter.media.MediaRouter  routes &androidx.mediarouter.media.MediaRouter  selectRoute &androidx.mediarouter.media.MediaRouter  	setRoutes &androidx.mediarouter.media.MediaRouter  Device /androidx.mediarouter.media.MediaRouter.Callback  
DeviceType /androidx.mediarouter.media.MediaRouter.Callback  MediaRouter /androidx.mediarouter.media.MediaRouter.Callback  any /androidx.mediarouter.media.MediaRouter.Callback  availableDevices /androidx.mediarouter.media.MediaRouter.Callback  onDevicesUpdatedListener /androidx.mediarouter.media.MediaRouter.Callback  	removeAll /androidx.mediarouter.media.MediaRouter.Callback  toList /androidx.mediarouter.media.MediaRouter.Callback  equals 0androidx.mediarouter.media.MediaRouter.RouteInfo  getID 0androidx.mediarouter.media.MediaRouter.RouteInfo  getId 0androidx.mediarouter.media.MediaRouter.RouteInfo  getNAME 0androidx.mediarouter.media.MediaRouter.RouteInfo  getName 0androidx.mediarouter.media.MediaRouter.RouteInfo  id 0androidx.mediarouter.media.MediaRouter.RouteInfo  name 0androidx.mediarouter.media.MediaRouter.RouteInfo  setId 0androidx.mediarouter.media.MediaRouter.RouteInfo  setName 0androidx.mediarouter.media.MediaRouter.RouteInfo  CastMediaControlIntent com.google.android.gms.cast  	MediaInfo com.google.android.gms.cast  MediaLoadRequestData com.google.android.gms.cast  
MediaMetadata com.google.android.gms.cast  categoryForCast 2com.google.android.gms.cast.CastMediaControlIntent  Builder %com.google.android.gms.cast.MediaInfo  STREAM_TYPE_BUFFERED %com.google.android.gms.cast.MediaInfo  build -com.google.android.gms.cast.MediaInfo.Builder  setContentType -com.google.android.gms.cast.MediaInfo.Builder  setMetadata -com.google.android.gms.cast.MediaInfo.Builder  
setStreamType -com.google.android.gms.cast.MediaInfo.Builder  Builder 0com.google.android.gms.cast.MediaLoadRequestData  build 8com.google.android.gms.cast.MediaLoadRequestData.Builder  setAutoplay 8com.google.android.gms.cast.MediaLoadRequestData.Builder  setMediaInfo 8com.google.android.gms.cast.MediaLoadRequestData.Builder  	KEY_TITLE )com.google.android.gms.cast.MediaMetadata  MEDIA_TYPE_MOVIE )com.google.android.gms.cast.MediaMetadata  	putString )com.google.android.gms.cast.MediaMetadata  CastContext %com.google.android.gms.cast.framework  CastOptions %com.google.android.gms.cast.framework  CastSession %com.google.android.gms.cast.framework  OptionsProvider %com.google.android.gms.cast.framework  SessionManagerListener %com.google.android.gms.cast.framework  SessionProvider %com.google.android.gms.cast.framework  getSESSIONManager 1com.google.android.gms.cast.framework.CastContext  getSessionManager 1com.google.android.gms.cast.framework.CastContext  getSharedInstance 1com.google.android.gms.cast.framework.CastContext  sessionManager 1com.google.android.gms.cast.framework.CastContext  setSessionManager 1com.google.android.gms.cast.framework.CastContext  Builder 1com.google.android.gms.cast.framework.CastOptions  build 9com.google.android.gms.cast.framework.CastOptions.Builder  setReceiverApplicationId 9com.google.android.gms.cast.framework.CastOptions.Builder  getREMOTEMediaClient 1com.google.android.gms.cast.framework.CastSession  getRemoteMediaClient 1com.google.android.gms.cast.framework.CastSession  remoteMediaClient 1com.google.android.gms.cast.framework.CastSession  setRemoteMediaClient 1com.google.android.gms.cast.framework.CastSession  addSessionManagerListener 4com.google.android.gms.cast.framework.SessionManager  removeSessionManagerListener 4com.google.android.gms.cast.framework.SessionManager  RemoteMediaClient +com.google.android.gms.cast.framework.media  MediaChannelResult =com.google.android.gms.cast.framework.media.RemoteMediaClient  load =com.google.android.gms.cast.framework.media.RemoteMediaClient  
PendingResult !com.google.android.gms.common.api  	putString Hcom.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable  R com.webvideocaster  id com.webvideocaster.R  layout com.webvideocaster.R  cast_button com.webvideocaster.R.id  	go_button com.webvideocaster.R.id  
url_edit_text com.webvideocaster.R.id  web_view com.webvideocaster.R.id  activity_browser com.webvideocaster.R.layout  Boolean com.webvideocaster.cast  CastContext com.webvideocaster.cast  CastManager com.webvideocaster.cast  CastMediaControlIntent com.webvideocaster.cast  CastOptions com.webvideocaster.cast  CastOptionsProvider com.webvideocaster.cast  CastSession com.webvideocaster.cast  ChromecastManager com.webvideocaster.cast  DLNAManager com.webvideocaster.cast  Device com.webvideocaster.cast  
DeviceType com.webvideocaster.cast  Int com.webvideocaster.cast  List com.webvideocaster.cast  Log com.webvideocaster.cast  	MediaInfo com.webvideocaster.cast  MediaLoadRequestData com.webvideocaster.cast  
MediaMetadata com.webvideocaster.cast  MediaRouteSelector com.webvideocaster.cast  MediaRouter com.webvideocaster.cast  START_STICKY com.webvideocaster.cast  String com.webvideocaster.cast  TAG com.webvideocaster.cast  Unit com.webvideocaster.cast  UpnpService com.webvideocaster.cast  any com.webvideocaster.cast  availableDevices com.webvideocaster.cast  castSession com.webvideocaster.cast  castUrlAfterSessionStart com.webvideocaster.cast  find com.webvideocaster.cast  getValue com.webvideocaster.cast  java com.webvideocaster.cast  lazy com.webvideocaster.cast  let com.webvideocaster.cast  	loadMedia com.webvideocaster.cast  
mutableListOf com.webvideocaster.cast  onDevicesUpdatedListener com.webvideocaster.cast  provideDelegate com.webvideocaster.cast  	removeAll com.webvideocaster.cast  run com.webvideocaster.cast  toList com.webvideocaster.cast  Boolean #com.webvideocaster.cast.CastManager  String #com.webvideocaster.cast.CastManager  CastOptions +com.webvideocaster.cast.CastOptionsProvider  Context +com.webvideocaster.cast.CastOptionsProvider  List +com.webvideocaster.cast.CastOptionsProvider  SessionProvider +com.webvideocaster.cast.CastOptionsProvider  Boolean )com.webvideocaster.cast.ChromecastManager  CastContext )com.webvideocaster.cast.ChromecastManager  CastMediaControlIntent )com.webvideocaster.cast.ChromecastManager  CastSession )com.webvideocaster.cast.ChromecastManager  Context )com.webvideocaster.cast.ChromecastManager  Device )com.webvideocaster.cast.ChromecastManager  
DeviceType )com.webvideocaster.cast.ChromecastManager  Int )com.webvideocaster.cast.ChromecastManager  List )com.webvideocaster.cast.ChromecastManager  Log )com.webvideocaster.cast.ChromecastManager  	MediaInfo )com.webvideocaster.cast.ChromecastManager  MediaLoadRequestData )com.webvideocaster.cast.ChromecastManager  
MediaMetadata )com.webvideocaster.cast.ChromecastManager  MediaRouteSelector )com.webvideocaster.cast.ChromecastManager  MediaRouter )com.webvideocaster.cast.ChromecastManager  MySessionManagerListener )com.webvideocaster.cast.ChromecastManager  SessionManagerListener )com.webvideocaster.cast.ChromecastManager  String )com.webvideocaster.cast.ChromecastManager  Unit )com.webvideocaster.cast.ChromecastManager  any )com.webvideocaster.cast.ChromecastManager  availableDevices )com.webvideocaster.cast.ChromecastManager  cast )com.webvideocaster.cast.ChromecastManager  castContext )com.webvideocaster.cast.ChromecastManager  castSession )com.webvideocaster.cast.ChromecastManager  castUrlAfterSessionStart )com.webvideocaster.cast.ChromecastManager  context )com.webvideocaster.cast.ChromecastManager  find )com.webvideocaster.cast.ChromecastManager  getANY )com.webvideocaster.cast.ChromecastManager  getAny )com.webvideocaster.cast.ChromecastManager  getFIND )com.webvideocaster.cast.ChromecastManager  getFind )com.webvideocaster.cast.ChromecastManager  getGETValue )com.webvideocaster.cast.ChromecastManager  getGetValue )com.webvideocaster.cast.ChromecastManager  getLAZY )com.webvideocaster.cast.ChromecastManager  getLET )com.webvideocaster.cast.ChromecastManager  getLazy )com.webvideocaster.cast.ChromecastManager  getLet )com.webvideocaster.cast.ChromecastManager  getMUTABLEListOf )com.webvideocaster.cast.ChromecastManager  getMutableListOf )com.webvideocaster.cast.ChromecastManager  getPROVIDEDelegate )com.webvideocaster.cast.ChromecastManager  getProvideDelegate )com.webvideocaster.cast.ChromecastManager  getREMOVEAll )com.webvideocaster.cast.ChromecastManager  getRUN )com.webvideocaster.cast.ChromecastManager  getRemoveAll )com.webvideocaster.cast.ChromecastManager  getRun )com.webvideocaster.cast.ChromecastManager  	getTOList )com.webvideocaster.cast.ChromecastManager  	getToList )com.webvideocaster.cast.ChromecastManager  getValue )com.webvideocaster.cast.ChromecastManager  java )com.webvideocaster.cast.ChromecastManager  lazy )com.webvideocaster.cast.ChromecastManager  let )com.webvideocaster.cast.ChromecastManager  	loadMedia )com.webvideocaster.cast.ChromecastManager  mediaRouteSelector )com.webvideocaster.cast.ChromecastManager  mediaRouter )com.webvideocaster.cast.ChromecastManager  mediaRouterCallback )com.webvideocaster.cast.ChromecastManager  
mutableListOf )com.webvideocaster.cast.ChromecastManager  onDevicesUpdatedListener )com.webvideocaster.cast.ChromecastManager  provideDelegate )com.webvideocaster.cast.ChromecastManager  	removeAll )com.webvideocaster.cast.ChromecastManager  run )com.webvideocaster.cast.ChromecastManager  sessionManagerListener )com.webvideocaster.cast.ChromecastManager  setOnDevicesUpdatedListener )com.webvideocaster.cast.ChromecastManager  startDeviceDiscovery )com.webvideocaster.cast.ChromecastManager  stopDeviceDiscovery )com.webvideocaster.cast.ChromecastManager  toList )com.webvideocaster.cast.ChromecastManager  Boolean Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  CastSession Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  Int Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  Log Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  String Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  castSession Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  castUrlAfterSessionStart Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getCASTSession Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getCASTUrlAfterSessionStart Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getCastSession Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getCastUrlAfterSessionStart Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getLET Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getLOADMedia Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getLet Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getLoadMedia Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  let Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  	loadMedia Bcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener  getANY Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getAVAILABLEDevices Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getAny Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getAvailableDevices Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getONDevicesUpdatedListener Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getOnDevicesUpdatedListener Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getREMOVEAll Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  getRemoveAll Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  	getTOList Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  	getToList Pcom.webvideocaster.cast.ChromecastManager.mediaRouterCallback.<no name provided>  Context #com.webvideocaster.cast.DLNAManager  Device #com.webvideocaster.cast.DLNAManager  
DeviceType #com.webvideocaster.cast.DLNAManager  List #com.webvideocaster.cast.DLNAManager  Log #com.webvideocaster.cast.DLNAManager  String #com.webvideocaster.cast.DLNAManager  TAG #com.webvideocaster.cast.DLNAManager  Unit #com.webvideocaster.cast.DLNAManager  availableDevices #com.webvideocaster.cast.DLNAManager  getMUTABLEListOf #com.webvideocaster.cast.DLNAManager  getMutableListOf #com.webvideocaster.cast.DLNAManager  	getTOList #com.webvideocaster.cast.DLNAManager  	getToList #com.webvideocaster.cast.DLNAManager  isServiceBound #com.webvideocaster.cast.DLNAManager  
mutableListOf #com.webvideocaster.cast.DLNAManager  onDevicesUpdatedListener #com.webvideocaster.cast.DLNAManager  toList #com.webvideocaster.cast.DLNAManager  Context -com.webvideocaster.cast.DLNAManager.Companion  Device -com.webvideocaster.cast.DLNAManager.Companion  
DeviceType -com.webvideocaster.cast.DLNAManager.Companion  List -com.webvideocaster.cast.DLNAManager.Companion  Log -com.webvideocaster.cast.DLNAManager.Companion  String -com.webvideocaster.cast.DLNAManager.Companion  TAG -com.webvideocaster.cast.DLNAManager.Companion  Unit -com.webvideocaster.cast.DLNAManager.Companion  getMUTABLEListOf -com.webvideocaster.cast.DLNAManager.Companion  getMutableListOf -com.webvideocaster.cast.DLNAManager.Companion  	getTOList -com.webvideocaster.cast.DLNAManager.Companion  	getToList -com.webvideocaster.cast.DLNAManager.Companion  
mutableListOf -com.webvideocaster.cast.DLNAManager.Companion  toList -com.webvideocaster.cast.DLNAManager.Companion  Binder #com.webvideocaster.cast.UpnpService  IBinder #com.webvideocaster.cast.UpnpService  Int #com.webvideocaster.cast.UpnpService  Intent #com.webvideocaster.cast.UpnpService  LocalBinder #com.webvideocaster.cast.UpnpService  START_STICKY #com.webvideocaster.cast.UpnpService  UpnpService #com.webvideocaster.cast.UpnpService  binder #com.webvideocaster.cast.UpnpService  UpnpService /com.webvideocaster.cast.UpnpService.LocalBinder  Any com.webvideocaster.media  List com.webvideocaster.media  String com.webvideocaster.media  Unit com.webvideocaster.media  VideoParser com.webvideocaster.media  VideoScanner com.webvideocaster.media  	emptyList com.webvideocaster.media  String $com.webvideocaster.media.VideoParser  Unit $com.webvideocaster.media.VideoParser  Any %com.webvideocaster.media.VideoScanner  List %com.webvideocaster.media.VideoScanner  	emptyList %com.webvideocaster.media.VideoScanner  getEMPTYList %com.webvideocaster.media.VideoScanner  getEmptyList %com.webvideocaster.media.VideoScanner  Device com.webvideocaster.model  
DeviceType com.webvideocaster.model  String com.webvideocaster.model  
DeviceType com.webvideocaster.model.Device  String com.webvideocaster.model.Device  id com.webvideocaster.model.Device  name com.webvideocaster.model.Device  type com.webvideocaster.model.Device  
CHROMECAST #com.webvideocaster.model.DeviceType  DLNA #com.webvideocaster.model.DeviceType  equals #com.webvideocaster.model.DeviceType  LocalHttpServer com.webvideocaster.network  String com.webvideocaster.network  String *com.webvideocaster.network.LocalHttpServer  AlertDialog com.webvideocaster.ui  Boolean com.webvideocaster.ui  BrowserActivity com.webvideocaster.ui  CastManager com.webvideocaster.ui  ChromecastManager com.webvideocaster.ui  DefaultPreview com.webvideocaster.ui  
DeviceType com.webvideocaster.ui  DialogInterface com.webvideocaster.ui  List com.webvideocaster.ui  LocalVideoActivity com.webvideocaster.ui  MainActivity com.webvideocaster.ui  
MainScreen com.webvideocaster.ui  R com.webvideocaster.ui  String com.webvideocaster.ui  Text com.webvideocaster.ui  detectedVideoUrls com.webvideocaster.ui  endsWith com.webvideocaster.ui  
isVideoUrl com.webvideocaster.ui  listOf com.webvideocaster.ui  map com.webvideocaster.ui  mutableSetOf com.webvideocaster.ui  
setContent com.webvideocaster.ui  
startsWith com.webvideocaster.ui  toString com.webvideocaster.ui  toTypedArray com.webvideocaster.ui  AlertDialog %com.webvideocaster.ui.BrowserActivity  Boolean %com.webvideocaster.ui.BrowserActivity  Bundle %com.webvideocaster.ui.BrowserActivity  Button %com.webvideocaster.ui.BrowserActivity  CastManager %com.webvideocaster.ui.BrowserActivity  ChromecastManager %com.webvideocaster.ui.BrowserActivity  Device %com.webvideocaster.ui.BrowserActivity  
DeviceType %com.webvideocaster.ui.BrowserActivity  DialogInterface %com.webvideocaster.ui.BrowserActivity  EditText %com.webvideocaster.ui.BrowserActivity  ImageButton %com.webvideocaster.ui.BrowserActivity  List %com.webvideocaster.ui.BrowserActivity  R %com.webvideocaster.ui.BrowserActivity  String %com.webvideocaster.ui.BrowserActivity  SuppressLint %com.webvideocaster.ui.BrowserActivity  WebResourceRequest %com.webvideocaster.ui.BrowserActivity  WebResourceResponse %com.webvideocaster.ui.BrowserActivity  WebView %com.webvideocaster.ui.BrowserActivity  
WebViewClient %com.webvideocaster.ui.BrowserActivity  availableDevices %com.webvideocaster.ui.BrowserActivity  
castButton %com.webvideocaster.ui.BrowserActivity  castManager %com.webvideocaster.ui.BrowserActivity  chromecastManager %com.webvideocaster.ui.BrowserActivity  detectedVideoUrls %com.webvideocaster.ui.BrowserActivity  endsWith %com.webvideocaster.ui.BrowserActivity  findViewById %com.webvideocaster.ui.BrowserActivity  getENDSWith %com.webvideocaster.ui.BrowserActivity  getEndsWith %com.webvideocaster.ui.BrowserActivity  	getLISTOf %com.webvideocaster.ui.BrowserActivity  	getListOf %com.webvideocaster.ui.BrowserActivity  getMAP %com.webvideocaster.ui.BrowserActivity  getMUTABLESetOf %com.webvideocaster.ui.BrowserActivity  getMap %com.webvideocaster.ui.BrowserActivity  getMutableSetOf %com.webvideocaster.ui.BrowserActivity  
getSTARTSWith %com.webvideocaster.ui.BrowserActivity  
getStartsWith %com.webvideocaster.ui.BrowserActivity  getTOString %com.webvideocaster.ui.BrowserActivity  getTOTypedArray %com.webvideocaster.ui.BrowserActivity  getToString %com.webvideocaster.ui.BrowserActivity  getToTypedArray %com.webvideocaster.ui.BrowserActivity  goButton %com.webvideocaster.ui.BrowserActivity  
isVideoUrl %com.webvideocaster.ui.BrowserActivity  listOf %com.webvideocaster.ui.BrowserActivity  map %com.webvideocaster.ui.BrowserActivity  mutableSetOf %com.webvideocaster.ui.BrowserActivity  setContentView %com.webvideocaster.ui.BrowserActivity  showDeviceSelectionDialog %com.webvideocaster.ui.BrowserActivity  showVideoSelectionDialog %com.webvideocaster.ui.BrowserActivity  
startsWith %com.webvideocaster.ui.BrowserActivity  toString %com.webvideocaster.ui.BrowserActivity  toTypedArray %com.webvideocaster.ui.BrowserActivity  urlEditText %com.webvideocaster.ui.BrowserActivity  webView %com.webvideocaster.ui.BrowserActivity  getDETECTEDVideoUrls Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  getDetectedVideoUrls Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  
getISVideoUrl Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  
getIsVideoUrl Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  getTOString Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  getToString Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  
isVideoUrl Acom.webvideocaster.ui.BrowserActivity.onCreate.<no name provided>  Bundle (com.webvideocaster.ui.LocalVideoActivity  Bundle "com.webvideocaster.ui.MainActivity  Text "com.webvideocaster.ui.MainActivity  
getSETContent "com.webvideocaster.ui.MainActivity  
getSetContent "com.webvideocaster.ui.MainActivity  
setContent "com.webvideocaster.ui.MainActivity  PermissionsHelper com.webvideocaster.utils  Activity *com.webvideocaster.utils.PermissionsHelper  AlertDialog 	java.lang  CastContext 	java.lang  CastManager 	java.lang  CastMediaControlIntent 	java.lang  CastOptions 	java.lang  CastSession 	java.lang  ChromecastManager 	java.lang  Class 	java.lang  Device 	java.lang  
DeviceType 	java.lang  DialogInterface 	java.lang  Log 	java.lang  	MediaInfo 	java.lang  MediaLoadRequestData 	java.lang  
MediaMetadata 	java.lang  MediaRouteSelector 	java.lang  MediaRouter 	java.lang  R 	java.lang  START_STICKY 	java.lang  TAG 	java.lang  Text 	java.lang  any 	java.lang  availableDevices 	java.lang  castSession 	java.lang  castUrlAfterSessionStart 	java.lang  detectedVideoUrls 	java.lang  	emptyList 	java.lang  endsWith 	java.lang  find 	java.lang  getValue 	java.lang  
isVideoUrl 	java.lang  java 	java.lang  lazy 	java.lang  let 	java.lang  listOf 	java.lang  	loadMedia 	java.lang  map 	java.lang  
mutableListOf 	java.lang  mutableSetOf 	java.lang  onDevicesUpdatedListener 	java.lang  provideDelegate 	java.lang  	removeAll 	java.lang  run 	java.lang  
startsWith 	java.lang  toList 	java.lang  toString 	java.lang  toTypedArray 	java.lang  AlertDialog kotlin  Any kotlin  Array kotlin  Boolean kotlin  CastContext kotlin  CastManager kotlin  CastMediaControlIntent kotlin  CastOptions kotlin  CastSession kotlin  ChromecastManager kotlin  Device kotlin  
DeviceType kotlin  DialogInterface kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  Lazy kotlin  Log kotlin  	MediaInfo kotlin  MediaLoadRequestData kotlin  
MediaMetadata kotlin  MediaRouteSelector kotlin  MediaRouter kotlin  Nothing kotlin  R kotlin  START_STICKY kotlin  String kotlin  TAG kotlin  Text kotlin  Unit kotlin  any kotlin  availableDevices kotlin  castSession kotlin  castUrlAfterSessionStart kotlin  detectedVideoUrls kotlin  	emptyList kotlin  endsWith kotlin  find kotlin  getValue kotlin  
isVideoUrl kotlin  java kotlin  lazy kotlin  let kotlin  listOf kotlin  	loadMedia kotlin  map kotlin  
mutableListOf kotlin  mutableSetOf kotlin  onDevicesUpdatedListener kotlin  provideDelegate kotlin  	removeAll kotlin  run kotlin  
startsWith kotlin  toList kotlin  toString kotlin  toTypedArray kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getENDSWith 
kotlin.String  getEndsWith 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  
getSTARTSWith 
kotlin.String  
getStartsWith 
kotlin.String  AlertDialog kotlin.annotation  CastContext kotlin.annotation  CastManager kotlin.annotation  CastMediaControlIntent kotlin.annotation  CastOptions kotlin.annotation  CastSession kotlin.annotation  ChromecastManager kotlin.annotation  Device kotlin.annotation  
DeviceType kotlin.annotation  DialogInterface kotlin.annotation  Log kotlin.annotation  	MediaInfo kotlin.annotation  MediaLoadRequestData kotlin.annotation  
MediaMetadata kotlin.annotation  MediaRouteSelector kotlin.annotation  MediaRouter kotlin.annotation  R kotlin.annotation  START_STICKY kotlin.annotation  TAG kotlin.annotation  Text kotlin.annotation  any kotlin.annotation  availableDevices kotlin.annotation  castSession kotlin.annotation  castUrlAfterSessionStart kotlin.annotation  detectedVideoUrls kotlin.annotation  	emptyList kotlin.annotation  endsWith kotlin.annotation  find kotlin.annotation  getValue kotlin.annotation  
isVideoUrl kotlin.annotation  java kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	loadMedia kotlin.annotation  map kotlin.annotation  
mutableListOf kotlin.annotation  mutableSetOf kotlin.annotation  onDevicesUpdatedListener kotlin.annotation  provideDelegate kotlin.annotation  	removeAll kotlin.annotation  run kotlin.annotation  
startsWith kotlin.annotation  toList kotlin.annotation  toString kotlin.annotation  toTypedArray kotlin.annotation  AlertDialog kotlin.collections  CastContext kotlin.collections  CastManager kotlin.collections  CastMediaControlIntent kotlin.collections  CastOptions kotlin.collections  CastSession kotlin.collections  ChromecastManager kotlin.collections  Device kotlin.collections  
DeviceType kotlin.collections  DialogInterface kotlin.collections  List kotlin.collections  Log kotlin.collections  	MediaInfo kotlin.collections  MediaLoadRequestData kotlin.collections  
MediaMetadata kotlin.collections  MediaRouteSelector kotlin.collections  MediaRouter kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  R kotlin.collections  START_STICKY kotlin.collections  TAG kotlin.collections  Text kotlin.collections  any kotlin.collections  availableDevices kotlin.collections  castSession kotlin.collections  castUrlAfterSessionStart kotlin.collections  detectedVideoUrls kotlin.collections  	emptyList kotlin.collections  endsWith kotlin.collections  find kotlin.collections  getValue kotlin.collections  
isVideoUrl kotlin.collections  java kotlin.collections  lazy kotlin.collections  let kotlin.collections  listOf kotlin.collections  	loadMedia kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  mutableSetOf kotlin.collections  onDevicesUpdatedListener kotlin.collections  provideDelegate kotlin.collections  	removeAll kotlin.collections  run kotlin.collections  
startsWith kotlin.collections  toList kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  getMAP kotlin.collections.List  getMap kotlin.collections.List  getTOTypedArray kotlin.collections.List  getToTypedArray kotlin.collections.List  getANY kotlin.collections.MutableList  getAny kotlin.collections.MutableList  getFIND kotlin.collections.MutableList  getFind kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  	getTOList kotlin.collections.MutableList  	getToList kotlin.collections.MutableList  getTOTypedArray kotlin.collections.MutableSet  getToTypedArray kotlin.collections.MutableSet  AlertDialog kotlin.comparisons  CastContext kotlin.comparisons  CastManager kotlin.comparisons  CastMediaControlIntent kotlin.comparisons  CastOptions kotlin.comparisons  CastSession kotlin.comparisons  ChromecastManager kotlin.comparisons  Device kotlin.comparisons  
DeviceType kotlin.comparisons  DialogInterface kotlin.comparisons  Log kotlin.comparisons  	MediaInfo kotlin.comparisons  MediaLoadRequestData kotlin.comparisons  
MediaMetadata kotlin.comparisons  MediaRouteSelector kotlin.comparisons  MediaRouter kotlin.comparisons  R kotlin.comparisons  START_STICKY kotlin.comparisons  TAG kotlin.comparisons  Text kotlin.comparisons  any kotlin.comparisons  availableDevices kotlin.comparisons  castSession kotlin.comparisons  castUrlAfterSessionStart kotlin.comparisons  detectedVideoUrls kotlin.comparisons  	emptyList kotlin.comparisons  endsWith kotlin.comparisons  find kotlin.comparisons  getValue kotlin.comparisons  
isVideoUrl kotlin.comparisons  java kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	loadMedia kotlin.comparisons  map kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableSetOf kotlin.comparisons  onDevicesUpdatedListener kotlin.comparisons  provideDelegate kotlin.comparisons  	removeAll kotlin.comparisons  run kotlin.comparisons  
startsWith kotlin.comparisons  toList kotlin.comparisons  toString kotlin.comparisons  toTypedArray kotlin.comparisons  AlertDialog 	kotlin.io  CastContext 	kotlin.io  CastManager 	kotlin.io  CastMediaControlIntent 	kotlin.io  CastOptions 	kotlin.io  CastSession 	kotlin.io  ChromecastManager 	kotlin.io  Device 	kotlin.io  
DeviceType 	kotlin.io  DialogInterface 	kotlin.io  Log 	kotlin.io  	MediaInfo 	kotlin.io  MediaLoadRequestData 	kotlin.io  
MediaMetadata 	kotlin.io  MediaRouteSelector 	kotlin.io  MediaRouter 	kotlin.io  R 	kotlin.io  START_STICKY 	kotlin.io  TAG 	kotlin.io  Text 	kotlin.io  any 	kotlin.io  availableDevices 	kotlin.io  castSession 	kotlin.io  castUrlAfterSessionStart 	kotlin.io  detectedVideoUrls 	kotlin.io  	emptyList 	kotlin.io  endsWith 	kotlin.io  find 	kotlin.io  getValue 	kotlin.io  
isVideoUrl 	kotlin.io  java 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	loadMedia 	kotlin.io  map 	kotlin.io  
mutableListOf 	kotlin.io  mutableSetOf 	kotlin.io  onDevicesUpdatedListener 	kotlin.io  provideDelegate 	kotlin.io  	removeAll 	kotlin.io  run 	kotlin.io  
startsWith 	kotlin.io  toList 	kotlin.io  toString 	kotlin.io  toTypedArray 	kotlin.io  AlertDialog 
kotlin.jvm  CastContext 
kotlin.jvm  CastManager 
kotlin.jvm  CastMediaControlIntent 
kotlin.jvm  CastOptions 
kotlin.jvm  CastSession 
kotlin.jvm  ChromecastManager 
kotlin.jvm  Device 
kotlin.jvm  
DeviceType 
kotlin.jvm  DialogInterface 
kotlin.jvm  Log 
kotlin.jvm  	MediaInfo 
kotlin.jvm  MediaLoadRequestData 
kotlin.jvm  
MediaMetadata 
kotlin.jvm  MediaRouteSelector 
kotlin.jvm  MediaRouter 
kotlin.jvm  R 
kotlin.jvm  START_STICKY 
kotlin.jvm  TAG 
kotlin.jvm  Text 
kotlin.jvm  any 
kotlin.jvm  availableDevices 
kotlin.jvm  castSession 
kotlin.jvm  castUrlAfterSessionStart 
kotlin.jvm  detectedVideoUrls 
kotlin.jvm  	emptyList 
kotlin.jvm  endsWith 
kotlin.jvm  find 
kotlin.jvm  getValue 
kotlin.jvm  
isVideoUrl 
kotlin.jvm  java 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	loadMedia 
kotlin.jvm  map 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableSetOf 
kotlin.jvm  onDevicesUpdatedListener 
kotlin.jvm  provideDelegate 
kotlin.jvm  	removeAll 
kotlin.jvm  run 
kotlin.jvm  
startsWith 
kotlin.jvm  toList 
kotlin.jvm  toString 
kotlin.jvm  toTypedArray 
kotlin.jvm  AlertDialog 
kotlin.ranges  CastContext 
kotlin.ranges  CastManager 
kotlin.ranges  CastMediaControlIntent 
kotlin.ranges  CastOptions 
kotlin.ranges  CastSession 
kotlin.ranges  ChromecastManager 
kotlin.ranges  Device 
kotlin.ranges  
DeviceType 
kotlin.ranges  DialogInterface 
kotlin.ranges  Log 
kotlin.ranges  	MediaInfo 
kotlin.ranges  MediaLoadRequestData 
kotlin.ranges  
MediaMetadata 
kotlin.ranges  MediaRouteSelector 
kotlin.ranges  MediaRouter 
kotlin.ranges  R 
kotlin.ranges  START_STICKY 
kotlin.ranges  TAG 
kotlin.ranges  Text 
kotlin.ranges  any 
kotlin.ranges  availableDevices 
kotlin.ranges  castSession 
kotlin.ranges  castUrlAfterSessionStart 
kotlin.ranges  detectedVideoUrls 
kotlin.ranges  	emptyList 
kotlin.ranges  endsWith 
kotlin.ranges  find 
kotlin.ranges  getValue 
kotlin.ranges  
isVideoUrl 
kotlin.ranges  java 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	loadMedia 
kotlin.ranges  map 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableSetOf 
kotlin.ranges  onDevicesUpdatedListener 
kotlin.ranges  provideDelegate 
kotlin.ranges  	removeAll 
kotlin.ranges  run 
kotlin.ranges  
startsWith 
kotlin.ranges  toList 
kotlin.ranges  toString 
kotlin.ranges  toTypedArray 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AlertDialog kotlin.sequences  CastContext kotlin.sequences  CastManager kotlin.sequences  CastMediaControlIntent kotlin.sequences  CastOptions kotlin.sequences  CastSession kotlin.sequences  ChromecastManager kotlin.sequences  Device kotlin.sequences  
DeviceType kotlin.sequences  DialogInterface kotlin.sequences  Log kotlin.sequences  	MediaInfo kotlin.sequences  MediaLoadRequestData kotlin.sequences  
MediaMetadata kotlin.sequences  MediaRouteSelector kotlin.sequences  MediaRouter kotlin.sequences  R kotlin.sequences  START_STICKY kotlin.sequences  TAG kotlin.sequences  Text kotlin.sequences  any kotlin.sequences  availableDevices kotlin.sequences  castSession kotlin.sequences  castUrlAfterSessionStart kotlin.sequences  detectedVideoUrls kotlin.sequences  	emptyList kotlin.sequences  endsWith kotlin.sequences  find kotlin.sequences  getValue kotlin.sequences  
isVideoUrl kotlin.sequences  java kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	loadMedia kotlin.sequences  map kotlin.sequences  
mutableListOf kotlin.sequences  mutableSetOf kotlin.sequences  onDevicesUpdatedListener kotlin.sequences  provideDelegate kotlin.sequences  	removeAll kotlin.sequences  run kotlin.sequences  
startsWith kotlin.sequences  toList kotlin.sequences  toString kotlin.sequences  toTypedArray kotlin.sequences  AlertDialog kotlin.text  CastContext kotlin.text  CastManager kotlin.text  CastMediaControlIntent kotlin.text  CastOptions kotlin.text  CastSession kotlin.text  ChromecastManager kotlin.text  Device kotlin.text  
DeviceType kotlin.text  DialogInterface kotlin.text  Log kotlin.text  	MediaInfo kotlin.text  MediaLoadRequestData kotlin.text  
MediaMetadata kotlin.text  MediaRouteSelector kotlin.text  MediaRouter kotlin.text  R kotlin.text  START_STICKY kotlin.text  TAG kotlin.text  Text kotlin.text  any kotlin.text  availableDevices kotlin.text  castSession kotlin.text  castUrlAfterSessionStart kotlin.text  detectedVideoUrls kotlin.text  	emptyList kotlin.text  endsWith kotlin.text  find kotlin.text  getValue kotlin.text  
isVideoUrl kotlin.text  java kotlin.text  lazy kotlin.text  let kotlin.text  listOf kotlin.text  	loadMedia kotlin.text  map kotlin.text  
mutableListOf kotlin.text  mutableSetOf kotlin.text  onDevicesUpdatedListener kotlin.text  provideDelegate kotlin.text  	removeAll kotlin.text  run kotlin.text  
startsWith kotlin.text  toList kotlin.text  toString kotlin.text  toTypedArray kotlin.text  stopCasting )com.webvideocaster.cast.ChromecastManager  isAvailable )com.webvideocaster.cast.ChromecastManager  cleanup )com.webvideocaster.cast.ChromecastManager  getAvailableDevices )com.webvideocaster.cast.ChromecastManager  	VideoFile com.webvideocaster.media  
initialize )com.webvideocaster.cast.ChromecastManager  	castVideo )com.webvideocaster.cast.ChromecastManager  isConnected )com.webvideocaster.cast.ChromecastManager  invoke android.app.Activity  ContentResolver android.content  query android.content.ContentResolver  MEDIA_ROUTER_SERVICE android.content.Context  contentResolver android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getSystemService android.content.Context  invoke android.content.Context  setContentResolver android.content.Context  
startActivity android.content.Context  invoke android.content.ContextWrapper  FLAG_ACTIVITY_NEW_TASK android.content.Intent  flags android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  setFlags android.content.Intent  Cursor android.database  getColumnIndexOrThrow android.database.Cursor  getLong android.database.Cursor  	getString android.database.Cursor  getUSE android.database.Cursor  getUse android.database.Cursor  
moveToNext android.database.Cursor  use android.database.Cursor  MediaRouter 
android.media  
getRouteCount android.media.MediaRouter  Uri android.net  
MediaStore android.provider  Video android.provider.MediaStore  Media !android.provider.MediaStore.Video  DATA 'android.provider.MediaStore.Video.Media  
DATE_ADDED 'android.provider.MediaStore.Video.Media  
DATE_MODIFIED 'android.provider.MediaStore.Video.Media  DISPLAY_NAME 'android.provider.MediaStore.Video.Media  DURATION 'android.provider.MediaStore.Video.Media  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Video.Media  	MIME_TYPE 'android.provider.MediaStore.Video.Media  SIZE 'android.provider.MediaStore.Video.Media  TITLE 'android.provider.MediaStore.Video.Media  _ID 'android.provider.MediaStore.Video.Media  w android.util.Log  invoke  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_LONG android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  invoke #androidx.activity.ComponentActivity  invoke (androidx.appcompat.app.AppCompatActivity  invoke #androidx.core.app.ComponentActivity  invoke &androidx.fragment.app.FragmentActivity  KEY_SUBTITLE )com.google.android.gms.cast.MediaMetadata  getISConnected 1com.google.android.gms.cast.framework.CastSession  getIsConnected 1com.google.android.gms.cast.framework.CastSession  isConnected 1com.google.android.gms.cast.framework.CastSession  setConnected 1com.google.android.gms.cast.framework.CastSession  endCurrentSession 4com.google.android.gms.cast.framework.SessionManager  equals =com.google.android.gms.cast.framework.media.RemoteMediaClient  stop =com.google.android.gms.cast.framework.media.RemoteMediaClient  Context com.webvideocaster.cast  	Exception com.webvideocaster.cast  Intent com.webvideocaster.cast  Toast com.webvideocaster.cast  first com.webvideocaster.cast  invoke com.webvideocaster.cast  isBlank com.webvideocaster.cast  
isNotEmpty com.webvideocaster.cast  ChromecastManager #com.webvideocaster.cast.CastManager  Context #com.webvideocaster.cast.CastManager  DLNAManager #com.webvideocaster.cast.CastManager  Device #com.webvideocaster.cast.CastManager  
DeviceType #com.webvideocaster.cast.CastManager  	Exception #com.webvideocaster.cast.CastManager  Intent #com.webvideocaster.cast.CastManager  List #com.webvideocaster.cast.CastManager  Log #com.webvideocaster.cast.CastManager  MediaRouter #com.webvideocaster.cast.CastManager  TAG #com.webvideocaster.cast.CastManager  Toast #com.webvideocaster.cast.CastManager  castViaChromecast #com.webvideocaster.cast.CastManager  castViaDLNA #com.webvideocaster.cast.CastManager  chromecastManager #com.webvideocaster.cast.CastManager  context #com.webvideocaster.cast.CastManager  dlnaManager #com.webvideocaster.cast.CastManager  
getISBlank #com.webvideocaster.cast.CastManager  
getIsBlank #com.webvideocaster.cast.CastManager  getLET #com.webvideocaster.cast.CastManager  getLet #com.webvideocaster.cast.CastManager  getMUTABLEListOf #com.webvideocaster.cast.CastManager  getMutableListOf #com.webvideocaster.cast.CastManager  invoke #com.webvideocaster.cast.CastManager  isBlank #com.webvideocaster.cast.CastManager  isChromecastAvailable #com.webvideocaster.cast.CastManager  isDLNAAvailable #com.webvideocaster.cast.CastManager  isMiracastAvailable #com.webvideocaster.cast.CastManager  let #com.webvideocaster.cast.CastManager  
mutableListOf #com.webvideocaster.cast.CastManager  selectedDevice #com.webvideocaster.cast.CastManager  	showError #com.webvideocaster.cast.CastManager  startSystemMiracast #com.webvideocaster.cast.CastManager  Boolean -com.webvideocaster.cast.CastManager.Companion  ChromecastManager -com.webvideocaster.cast.CastManager.Companion  Context -com.webvideocaster.cast.CastManager.Companion  DLNAManager -com.webvideocaster.cast.CastManager.Companion  Device -com.webvideocaster.cast.CastManager.Companion  
DeviceType -com.webvideocaster.cast.CastManager.Companion  	Exception -com.webvideocaster.cast.CastManager.Companion  Intent -com.webvideocaster.cast.CastManager.Companion  List -com.webvideocaster.cast.CastManager.Companion  Log -com.webvideocaster.cast.CastManager.Companion  MediaRouter -com.webvideocaster.cast.CastManager.Companion  String -com.webvideocaster.cast.CastManager.Companion  TAG -com.webvideocaster.cast.CastManager.Companion  Toast -com.webvideocaster.cast.CastManager.Companion  
getISBlank -com.webvideocaster.cast.CastManager.Companion  
getIsBlank -com.webvideocaster.cast.CastManager.Companion  getLET -com.webvideocaster.cast.CastManager.Companion  getLet -com.webvideocaster.cast.CastManager.Companion  getMUTABLEListOf -com.webvideocaster.cast.CastManager.Companion  getMutableListOf -com.webvideocaster.cast.CastManager.Companion  invoke -com.webvideocaster.cast.CastManager.Companion  isBlank -com.webvideocaster.cast.CastManager.Companion  let -com.webvideocaster.cast.CastManager.Companion  
mutableListOf -com.webvideocaster.cast.CastManager.Companion  first )com.webvideocaster.cast.ChromecastManager  getFIRST )com.webvideocaster.cast.ChromecastManager  getFirst )com.webvideocaster.cast.ChromecastManager  
getISNotEmpty )com.webvideocaster.cast.ChromecastManager  
getIsNotEmpty )com.webvideocaster.cast.ChromecastManager  
isNotEmpty )com.webvideocaster.cast.ChromecastManager  loadMediaWithDetails )com.webvideocaster.cast.ChromecastManager  bindUpnpService #com.webvideocaster.cast.DLNAManager  equals #com.webvideocaster.cast.DLNAManager  getLET #com.webvideocaster.cast.DLNAManager  getLet #com.webvideocaster.cast.DLNAManager  let #com.webvideocaster.cast.DLNAManager  unbindUpnpService #com.webvideocaster.cast.DLNAManager  invoke -com.webvideocaster.cast.DLNAManager.Companion  Boolean com.webvideocaster.media  	Exception com.webvideocaster.media  File com.webvideocaster.media  Log com.webvideocaster.media  Long com.webvideocaster.media  
MediaStore com.webvideocaster.media  
MutableSet com.webvideocaster.media  Pattern com.webvideocaster.media  TAG com.webvideocaster.media  VIDEO_EXTENSIONS com.webvideocaster.media  VIDEO_PROJECTION com.webvideocaster.media  VIDEO_URL_PATTERNS com.webvideocaster.media  any com.webvideocaster.media  arrayOf com.webvideocaster.media  contains com.webvideocaster.media  	extension com.webvideocaster.media  forEach com.webvideocaster.media  format com.webvideocaster.media  isBlank com.webvideocaster.media  
isNotEmpty com.webvideocaster.media  joinToString com.webvideocaster.media  	lowercase com.webvideocaster.media  
mutableListOf com.webvideocaster.media  mutableSetOf com.webvideocaster.media  nameWithoutExtension com.webvideocaster.media  
startsWith com.webvideocaster.media  substringAfterLast com.webvideocaster.media  use com.webvideocaster.media  Long "com.webvideocaster.media.VideoFile  String "com.webvideocaster.media.VideoFile  Boolean $com.webvideocaster.media.VideoParser  	Exception $com.webvideocaster.media.VideoParser  Log $com.webvideocaster.media.VideoParser  
MutableSet $com.webvideocaster.media.VideoParser  Pattern $com.webvideocaster.media.VideoParser  TAG $com.webvideocaster.media.VideoParser  VIDEO_EXTENSIONS $com.webvideocaster.media.VideoParser  VIDEO_URL_PATTERNS $com.webvideocaster.media.VideoParser  any $com.webvideocaster.media.VideoParser  arrayOf $com.webvideocaster.media.VideoParser  contains $com.webvideocaster.media.VideoParser  forEach $com.webvideocaster.media.VideoParser  getANY $com.webvideocaster.media.VideoParser  
getARRAYOf $com.webvideocaster.media.VideoParser  getAny $com.webvideocaster.media.VideoParser  
getArrayOf $com.webvideocaster.media.VideoParser  getCONTAINS $com.webvideocaster.media.VideoParser  getContains $com.webvideocaster.media.VideoParser  
getFOREach $com.webvideocaster.media.VideoParser  
getForEach $com.webvideocaster.media.VideoParser  
getISBlank $com.webvideocaster.media.VideoParser  
getIsBlank $com.webvideocaster.media.VideoParser  getJOINToString $com.webvideocaster.media.VideoParser  getJoinToString $com.webvideocaster.media.VideoParser  getLOWERCASE $com.webvideocaster.media.VideoParser  getLowercase $com.webvideocaster.media.VideoParser  getMUTABLESetOf $com.webvideocaster.media.VideoParser  getMutableSetOf $com.webvideocaster.media.VideoParser  
getSTARTSWith $com.webvideocaster.media.VideoParser  
getStartsWith $com.webvideocaster.media.VideoParser  isBlank $com.webvideocaster.media.VideoParser  isValidVideoUrl $com.webvideocaster.media.VideoParser  isVideoEmbedUrl $com.webvideocaster.media.VideoParser  joinToString $com.webvideocaster.media.VideoParser  	lowercase $com.webvideocaster.media.VideoParser  mutableSetOf $com.webvideocaster.media.VideoParser  parseDirectVideoUrls $com.webvideocaster.media.VideoParser  parseIframeEmbeds $com.webvideocaster.media.VideoParser  parseJavaScriptVideoUrls $com.webvideocaster.media.VideoParser  parseSourceTags $com.webvideocaster.media.VideoParser  parseVideoTags $com.webvideocaster.media.VideoParser  
startsWith $com.webvideocaster.media.VideoParser  Boolean .com.webvideocaster.media.VideoParser.Companion  	Exception .com.webvideocaster.media.VideoParser.Companion  Log .com.webvideocaster.media.VideoParser.Companion  
MutableSet .com.webvideocaster.media.VideoParser.Companion  Pattern .com.webvideocaster.media.VideoParser.Companion  String .com.webvideocaster.media.VideoParser.Companion  TAG .com.webvideocaster.media.VideoParser.Companion  Unit .com.webvideocaster.media.VideoParser.Companion  VIDEO_EXTENSIONS .com.webvideocaster.media.VideoParser.Companion  VIDEO_URL_PATTERNS .com.webvideocaster.media.VideoParser.Companion  any .com.webvideocaster.media.VideoParser.Companion  arrayOf .com.webvideocaster.media.VideoParser.Companion  contains .com.webvideocaster.media.VideoParser.Companion  forEach .com.webvideocaster.media.VideoParser.Companion  getANY .com.webvideocaster.media.VideoParser.Companion  
getARRAYOf .com.webvideocaster.media.VideoParser.Companion  getAny .com.webvideocaster.media.VideoParser.Companion  
getArrayOf .com.webvideocaster.media.VideoParser.Companion  getCONTAINS .com.webvideocaster.media.VideoParser.Companion  getContains .com.webvideocaster.media.VideoParser.Companion  
getFOREach .com.webvideocaster.media.VideoParser.Companion  
getForEach .com.webvideocaster.media.VideoParser.Companion  
getISBlank .com.webvideocaster.media.VideoParser.Companion  
getIsBlank .com.webvideocaster.media.VideoParser.Companion  getJOINToString .com.webvideocaster.media.VideoParser.Companion  getJoinToString .com.webvideocaster.media.VideoParser.Companion  getLOWERCASE .com.webvideocaster.media.VideoParser.Companion  getLowercase .com.webvideocaster.media.VideoParser.Companion  getMUTABLESetOf .com.webvideocaster.media.VideoParser.Companion  getMutableSetOf .com.webvideocaster.media.VideoParser.Companion  
getSTARTSWith .com.webvideocaster.media.VideoParser.Companion  
getStartsWith .com.webvideocaster.media.VideoParser.Companion  isBlank .com.webvideocaster.media.VideoParser.Companion  joinToString .com.webvideocaster.media.VideoParser.Companion  	lowercase .com.webvideocaster.media.VideoParser.Companion  mutableSetOf .com.webvideocaster.media.VideoParser.Companion  
startsWith .com.webvideocaster.media.VideoParser.Companion  Boolean %com.webvideocaster.media.VideoScanner  ContentResolver %com.webvideocaster.media.VideoScanner  Context %com.webvideocaster.media.VideoScanner  Cursor %com.webvideocaster.media.VideoScanner  	Exception %com.webvideocaster.media.VideoScanner  File %com.webvideocaster.media.VideoScanner  Log %com.webvideocaster.media.VideoScanner  Long %com.webvideocaster.media.VideoScanner  
MediaStore %com.webvideocaster.media.VideoScanner  String %com.webvideocaster.media.VideoScanner  TAG %com.webvideocaster.media.VideoScanner  VIDEO_PROJECTION %com.webvideocaster.media.VideoScanner  	VideoFile %com.webvideocaster.media.VideoScanner  arrayOf %com.webvideocaster.media.VideoScanner  contains %com.webvideocaster.media.VideoScanner  context %com.webvideocaster.media.VideoScanner  	extension %com.webvideocaster.media.VideoScanner  forEach %com.webvideocaster.media.VideoScanner  format %com.webvideocaster.media.VideoScanner  
getARRAYOf %com.webvideocaster.media.VideoScanner  
getArrayOf %com.webvideocaster.media.VideoScanner  getCONTAINS %com.webvideocaster.media.VideoScanner  getContains %com.webvideocaster.media.VideoScanner  
getFOREach %com.webvideocaster.media.VideoScanner  	getFORMAT %com.webvideocaster.media.VideoScanner  
getForEach %com.webvideocaster.media.VideoScanner  	getFormat %com.webvideocaster.media.VideoScanner  
getISNotEmpty %com.webvideocaster.media.VideoScanner  
getIsNotEmpty %com.webvideocaster.media.VideoScanner  getLOWERCASE %com.webvideocaster.media.VideoScanner  getLowercase %com.webvideocaster.media.VideoScanner  getMUTABLEListOf %com.webvideocaster.media.VideoScanner  getMimeTypeFromExtension %com.webvideocaster.media.VideoScanner  getMutableListOf %com.webvideocaster.media.VideoScanner  getSUBSTRINGAfterLast %com.webvideocaster.media.VideoScanner  getSubstringAfterLast %com.webvideocaster.media.VideoScanner  getUSE %com.webvideocaster.media.VideoScanner  getUse %com.webvideocaster.media.VideoScanner  
isNotEmpty %com.webvideocaster.media.VideoScanner  isVideoFile %com.webvideocaster.media.VideoScanner  	lowercase %com.webvideocaster.media.VideoScanner  
mutableListOf %com.webvideocaster.media.VideoScanner  nameWithoutExtension %com.webvideocaster.media.VideoScanner  substringAfterLast %com.webvideocaster.media.VideoScanner  use %com.webvideocaster.media.VideoScanner  Boolean /com.webvideocaster.media.VideoScanner.Companion  ContentResolver /com.webvideocaster.media.VideoScanner.Companion  Context /com.webvideocaster.media.VideoScanner.Companion  Cursor /com.webvideocaster.media.VideoScanner.Companion  	Exception /com.webvideocaster.media.VideoScanner.Companion  File /com.webvideocaster.media.VideoScanner.Companion  List /com.webvideocaster.media.VideoScanner.Companion  Log /com.webvideocaster.media.VideoScanner.Companion  Long /com.webvideocaster.media.VideoScanner.Companion  
MediaStore /com.webvideocaster.media.VideoScanner.Companion  String /com.webvideocaster.media.VideoScanner.Companion  TAG /com.webvideocaster.media.VideoScanner.Companion  VIDEO_PROJECTION /com.webvideocaster.media.VideoScanner.Companion  	VideoFile /com.webvideocaster.media.VideoScanner.Companion  arrayOf /com.webvideocaster.media.VideoScanner.Companion  contains /com.webvideocaster.media.VideoScanner.Companion  	extension /com.webvideocaster.media.VideoScanner.Companion  forEach /com.webvideocaster.media.VideoScanner.Companion  format /com.webvideocaster.media.VideoScanner.Companion  
getARRAYOf /com.webvideocaster.media.VideoScanner.Companion  
getArrayOf /com.webvideocaster.media.VideoScanner.Companion  getCONTAINS /com.webvideocaster.media.VideoScanner.Companion  getContains /com.webvideocaster.media.VideoScanner.Companion  
getFOREach /com.webvideocaster.media.VideoScanner.Companion  	getFORMAT /com.webvideocaster.media.VideoScanner.Companion  
getForEach /com.webvideocaster.media.VideoScanner.Companion  	getFormat /com.webvideocaster.media.VideoScanner.Companion  
getISNotEmpty /com.webvideocaster.media.VideoScanner.Companion  
getIsNotEmpty /com.webvideocaster.media.VideoScanner.Companion  getLOWERCASE /com.webvideocaster.media.VideoScanner.Companion  getLowercase /com.webvideocaster.media.VideoScanner.Companion  getMUTABLEListOf /com.webvideocaster.media.VideoScanner.Companion  getMutableListOf /com.webvideocaster.media.VideoScanner.Companion  getSUBSTRINGAfterLast /com.webvideocaster.media.VideoScanner.Companion  getSubstringAfterLast /com.webvideocaster.media.VideoScanner.Companion  getUSE /com.webvideocaster.media.VideoScanner.Companion  getUse /com.webvideocaster.media.VideoScanner.Companion  
isNotEmpty /com.webvideocaster.media.VideoScanner.Companion  	lowercase /com.webvideocaster.media.VideoScanner.Companion  
mutableListOf /com.webvideocaster.media.VideoScanner.Companion  nameWithoutExtension /com.webvideocaster.media.VideoScanner.Companion  substringAfterLast /com.webvideocaster.media.VideoScanner.Companion  use /com.webvideocaster.media.VideoScanner.Companion  equals com.webvideocaster.model.Device  MIRACAST #com.webvideocaster.model.DeviceType  Boolean com.webvideocaster.network  	Exception com.webvideocaster.network  File com.webvideocaster.network  FileInputStream com.webvideocaster.network  Int com.webvideocaster.network  Log com.webvideocaster.network  MIME_PLAINTEXT com.webvideocaster.network  NetworkInterface com.webvideocaster.network  Response com.webvideocaster.network  TAG com.webvideocaster.network  UUID com.webvideocaster.network  contains com.webvideocaster.network  endsWith com.webvideocaster.network  	lowercase com.webvideocaster.network  mutableMapOf com.webvideocaster.network  newFixedLengthResponse com.webvideocaster.network  set com.webvideocaster.network  
startsWith com.webvideocaster.network  
trimIndent com.webvideocaster.network  Boolean *com.webvideocaster.network.LocalHttpServer  	Exception *com.webvideocaster.network.LocalHttpServer  File *com.webvideocaster.network.LocalHttpServer  FileInputStream *com.webvideocaster.network.LocalHttpServer  IHTTPSession *com.webvideocaster.network.LocalHttpServer  IOException *com.webvideocaster.network.LocalHttpServer  Int *com.webvideocaster.network.LocalHttpServer  Log *com.webvideocaster.network.LocalHttpServer  MIME_PLAINTEXT *com.webvideocaster.network.LocalHttpServer  NetworkInterface *com.webvideocaster.network.LocalHttpServer  Response *com.webvideocaster.network.LocalHttpServer  TAG *com.webvideocaster.network.LocalHttpServer  UUID *com.webvideocaster.network.LocalHttpServer  contains *com.webvideocaster.network.LocalHttpServer  endsWith *com.webvideocaster.network.LocalHttpServer  getCONTAINS *com.webvideocaster.network.LocalHttpServer  getContains *com.webvideocaster.network.LocalHttpServer  getENDSWith *com.webvideocaster.network.LocalHttpServer  getEndsWith *com.webvideocaster.network.LocalHttpServer  getLOWERCASE *com.webvideocaster.network.LocalHttpServer  getLocalIpAddress *com.webvideocaster.network.LocalHttpServer  getLowercase *com.webvideocaster.network.LocalHttpServer  getMUTABLEMapOf *com.webvideocaster.network.LocalHttpServer  getMimeTypeForFile *com.webvideocaster.network.LocalHttpServer  getMutableMapOf *com.webvideocaster.network.LocalHttpServer  getNEWFixedLengthResponse *com.webvideocaster.network.LocalHttpServer  getNewFixedLengthResponse *com.webvideocaster.network.LocalHttpServer  getSET *com.webvideocaster.network.LocalHttpServer  
getSTARTSWith *com.webvideocaster.network.LocalHttpServer  getSet *com.webvideocaster.network.LocalHttpServer  
getStartsWith *com.webvideocaster.network.LocalHttpServer  
getTRIMIndent *com.webvideocaster.network.LocalHttpServer  
getTrimIndent *com.webvideocaster.network.LocalHttpServer  isServerRunning *com.webvideocaster.network.LocalHttpServer  	lowercase *com.webvideocaster.network.LocalHttpServer  mutableMapOf *com.webvideocaster.network.LocalHttpServer  newFixedLengthResponse *com.webvideocaster.network.LocalHttpServer  port *com.webvideocaster.network.LocalHttpServer  	serveFile *com.webvideocaster.network.LocalHttpServer  serveIndexPage *com.webvideocaster.network.LocalHttpServer  servedFiles *com.webvideocaster.network.LocalHttpServer  set *com.webvideocaster.network.LocalHttpServer  
startsWith *com.webvideocaster.network.LocalHttpServer  
trimIndent *com.webvideocaster.network.LocalHttpServer  Boolean 4com.webvideocaster.network.LocalHttpServer.Companion  	Exception 4com.webvideocaster.network.LocalHttpServer.Companion  File 4com.webvideocaster.network.LocalHttpServer.Companion  FileInputStream 4com.webvideocaster.network.LocalHttpServer.Companion  IHTTPSession 4com.webvideocaster.network.LocalHttpServer.Companion  IOException 4com.webvideocaster.network.LocalHttpServer.Companion  Int 4com.webvideocaster.network.LocalHttpServer.Companion  Log 4com.webvideocaster.network.LocalHttpServer.Companion  MIME_PLAINTEXT 4com.webvideocaster.network.LocalHttpServer.Companion  NetworkInterface 4com.webvideocaster.network.LocalHttpServer.Companion  Response 4com.webvideocaster.network.LocalHttpServer.Companion  String 4com.webvideocaster.network.LocalHttpServer.Companion  TAG 4com.webvideocaster.network.LocalHttpServer.Companion  UUID 4com.webvideocaster.network.LocalHttpServer.Companion  contains 4com.webvideocaster.network.LocalHttpServer.Companion  endsWith 4com.webvideocaster.network.LocalHttpServer.Companion  getCONTAINS 4com.webvideocaster.network.LocalHttpServer.Companion  getContains 4com.webvideocaster.network.LocalHttpServer.Companion  getENDSWith 4com.webvideocaster.network.LocalHttpServer.Companion  getEndsWith 4com.webvideocaster.network.LocalHttpServer.Companion  getLOWERCASE 4com.webvideocaster.network.LocalHttpServer.Companion  getLowercase 4com.webvideocaster.network.LocalHttpServer.Companion  getMUTABLEMapOf 4com.webvideocaster.network.LocalHttpServer.Companion  getMutableMapOf 4com.webvideocaster.network.LocalHttpServer.Companion  getNEWFixedLengthResponse 4com.webvideocaster.network.LocalHttpServer.Companion  getNewFixedLengthResponse 4com.webvideocaster.network.LocalHttpServer.Companion  getSET 4com.webvideocaster.network.LocalHttpServer.Companion  
getSTARTSWith 4com.webvideocaster.network.LocalHttpServer.Companion  getSet 4com.webvideocaster.network.LocalHttpServer.Companion  
getStartsWith 4com.webvideocaster.network.LocalHttpServer.Companion  
getTRIMIndent 4com.webvideocaster.network.LocalHttpServer.Companion  
getTrimIndent 4com.webvideocaster.network.LocalHttpServer.Companion  	lowercase 4com.webvideocaster.network.LocalHttpServer.Companion  mutableMapOf 4com.webvideocaster.network.LocalHttpServer.Companion  newFixedLengthResponse 4com.webvideocaster.network.LocalHttpServer.Companion  set 4com.webvideocaster.network.LocalHttpServer.Companion  
startsWith 4com.webvideocaster.network.LocalHttpServer.Companion  
trimIndent 4com.webvideocaster.network.LocalHttpServer.Companion  invoke %com.webvideocaster.ui.BrowserActivity  	NanoHTTPD 
fi.iki.elonen  Boolean fi.iki.elonen.NanoHTTPD  	Exception fi.iki.elonen.NanoHTTPD  File fi.iki.elonen.NanoHTTPD  FileInputStream fi.iki.elonen.NanoHTTPD  IHTTPSession fi.iki.elonen.NanoHTTPD  IOException fi.iki.elonen.NanoHTTPD  Int fi.iki.elonen.NanoHTTPD  Log fi.iki.elonen.NanoHTTPD  MIME_PLAINTEXT fi.iki.elonen.NanoHTTPD  NetworkInterface fi.iki.elonen.NanoHTTPD  Response fi.iki.elonen.NanoHTTPD  String fi.iki.elonen.NanoHTTPD  TAG fi.iki.elonen.NanoHTTPD  UUID fi.iki.elonen.NanoHTTPD  contains fi.iki.elonen.NanoHTTPD  endsWith fi.iki.elonen.NanoHTTPD  getLocalIpAddress fi.iki.elonen.NanoHTTPD  getMimeTypeForFile fi.iki.elonen.NanoHTTPD  	lowercase fi.iki.elonen.NanoHTTPD  mutableMapOf fi.iki.elonen.NanoHTTPD  newFixedLengthResponse fi.iki.elonen.NanoHTTPD  	serveFile fi.iki.elonen.NanoHTTPD  serveIndexPage fi.iki.elonen.NanoHTTPD  set fi.iki.elonen.NanoHTTPD  start fi.iki.elonen.NanoHTTPD  
startsWith fi.iki.elonen.NanoHTTPD  stop fi.iki.elonen.NanoHTTPD  
trimIndent fi.iki.elonen.NanoHTTPD  getURI $fi.iki.elonen.NanoHTTPD.IHTTPSession  getUri $fi.iki.elonen.NanoHTTPD.IHTTPSession  setUri $fi.iki.elonen.NanoHTTPD.IHTTPSession  uri $fi.iki.elonen.NanoHTTPD.IHTTPSession  Status  fi.iki.elonen.NanoHTTPD.Response  	addHeader  fi.iki.elonen.NanoHTTPD.Response  INTERNAL_ERROR 'fi.iki.elonen.NanoHTTPD.Response.Status  	NOT_FOUND 'fi.iki.elonen.NanoHTTPD.Response.Status  OK 'fi.iki.elonen.NanoHTTPD.Response.Status  File java.io  FileInputStream java.io  IOException java.io  absolutePath java.io.File  exists java.io.File  	extension java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getEXTENSION java.io.File  getExtension java.io.File  getISDirectory java.io.File  	getISFile java.io.File  getIsDirectory java.io.File  	getIsFile java.io.File  getNAME java.io.File  getNAMEWithoutExtension java.io.File  getName java.io.File  getNameWithoutExtension java.io.File  hashCode java.io.File  isDirectory java.io.File  isFile java.io.File  lastModified java.io.File  length java.io.File  	listFiles java.io.File  name java.io.File  nameWithoutExtension java.io.File  setAbsolutePath java.io.File  setDirectory java.io.File  setFile java.io.File  setName java.io.File  <SAM-CONSTRUCTOR> java.io.FileFilter  <SAM-CONSTRUCTOR> java.io.FilenameFilter  Context 	java.lang  DLNAManager 	java.lang  	Exception 	java.lang  File 	java.lang  FileInputStream 	java.lang  Intent 	java.lang  MIME_PLAINTEXT 	java.lang  
MediaStore 	java.lang  NetworkInterface 	java.lang  Pattern 	java.lang  Response 	java.lang  String 	java.lang  Toast 	java.lang  UUID 	java.lang  VIDEO_EXTENSIONS 	java.lang  VIDEO_PROJECTION 	java.lang  VIDEO_URL_PATTERNS 	java.lang  	VideoFile 	java.lang  arrayOf 	java.lang  contains 	java.lang  	extension 	java.lang  first 	java.lang  forEach 	java.lang  format 	java.lang  isBlank 	java.lang  
isNotEmpty 	java.lang  joinToString 	java.lang  	lowercase 	java.lang  mutableMapOf 	java.lang  nameWithoutExtension 	java.lang  newFixedLengthResponse 	java.lang  set 	java.lang  substringAfterLast 	java.lang  
trimIndent 	java.lang  use 	java.lang  message java.lang.Exception  InetAddress java.net  NetworkInterface java.net  getHOSTAddress java.net.InetAddress  getHostAddress java.net.InetAddress  getISLoopbackAddress java.net.InetAddress  getIsLoopbackAddress java.net.InetAddress  hostAddress java.net.InetAddress  isLoopbackAddress java.net.InetAddress  setHostAddress java.net.InetAddress  setLoopbackAddress java.net.InetAddress  getINETAddresses java.net.NetworkInterface  
getISLoopback java.net.NetworkInterface  getISUp java.net.NetworkInterface  getInetAddresses java.net.NetworkInterface  
getIsLoopback java.net.NetworkInterface  getIsUp java.net.NetworkInterface  getNetworkInterfaces java.net.NetworkInterface  
inetAddresses java.net.NetworkInterface  
isLoopback java.net.NetworkInterface  isUp java.net.NetworkInterface  setInetAddresses java.net.NetworkInterface  setLoopback java.net.NetworkInterface  setUp java.net.NetworkInterface  Enumeration 	java.util  	Exception 	java.util  File 	java.util  FileInputStream 	java.util  Log 	java.util  MIME_PLAINTEXT 	java.util  NetworkInterface 	java.util  Response 	java.util  TAG 	java.util  UUID 	java.util  contains 	java.util  endsWith 	java.util  	lowercase 	java.util  mutableMapOf 	java.util  newFixedLengthResponse 	java.util  set 	java.util  
startsWith 	java.util  
trimIndent 	java.util  hasMoreElements java.util.Enumeration  nextElement java.util.Enumeration  
randomUUID java.util.UUID  Matcher java.util.regex  Pattern java.util.regex  find java.util.regex.Matcher  group java.util.regex.Matcher  CASE_INSENSITIVE java.util.regex.Pattern  DOTALL java.util.regex.Pattern  compile java.util.regex.Pattern  matcher java.util.regex.Pattern  Char kotlin  Context kotlin  DLNAManager kotlin  Double kotlin  	Exception kotlin  File kotlin  FileInputStream kotlin  Intent kotlin  Long kotlin  MIME_PLAINTEXT kotlin  
MediaStore kotlin  NetworkInterface kotlin  Pattern kotlin  Response kotlin  Toast kotlin  UUID kotlin  VIDEO_EXTENSIONS kotlin  VIDEO_PROJECTION kotlin  VIDEO_URL_PATTERNS kotlin  	VideoFile kotlin  arrayOf kotlin  contains kotlin  	extension kotlin  first kotlin  forEach kotlin  format kotlin  isBlank kotlin  
isNotEmpty kotlin  joinToString kotlin  	lowercase kotlin  mutableMapOf kotlin  nameWithoutExtension kotlin  newFixedLengthResponse kotlin  set kotlin  substringAfterLast kotlin  
trimIndent kotlin  use kotlin  getANY kotlin.Array  getAny kotlin.Array  getCONTAINS kotlin.Array  getContains kotlin.Array  
getFOREach kotlin.Array  
getForEach kotlin.Array  getJOINToString kotlin.Array  getJoinToString kotlin.Array  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  getLOWERCASE 
kotlin.String  getLowercase 
kotlin.String  getSUBSTRINGAfterLast 
kotlin.String  getSubstringAfterLast 
kotlin.String  
getTRIMIndent 
kotlin.String  
getTrimIndent 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  Context kotlin.annotation  DLNAManager kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileInputStream kotlin.annotation  Intent kotlin.annotation  MIME_PLAINTEXT kotlin.annotation  
MediaStore kotlin.annotation  NetworkInterface kotlin.annotation  Pattern kotlin.annotation  Response kotlin.annotation  String kotlin.annotation  Toast kotlin.annotation  UUID kotlin.annotation  VIDEO_EXTENSIONS kotlin.annotation  VIDEO_PROJECTION kotlin.annotation  VIDEO_URL_PATTERNS kotlin.annotation  	VideoFile kotlin.annotation  arrayOf kotlin.annotation  contains kotlin.annotation  	extension kotlin.annotation  first kotlin.annotation  forEach kotlin.annotation  format kotlin.annotation  isBlank kotlin.annotation  
isNotEmpty kotlin.annotation  joinToString kotlin.annotation  	lowercase kotlin.annotation  mutableMapOf kotlin.annotation  nameWithoutExtension kotlin.annotation  newFixedLengthResponse kotlin.annotation  set kotlin.annotation  substringAfterLast kotlin.annotation  
trimIndent kotlin.annotation  use kotlin.annotation  Context kotlin.collections  DLNAManager kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileInputStream kotlin.collections  Intent kotlin.collections  MIME_PLAINTEXT kotlin.collections  
MediaStore kotlin.collections  
MutableMap kotlin.collections  NetworkInterface kotlin.collections  Pattern kotlin.collections  Response kotlin.collections  String kotlin.collections  Toast kotlin.collections  UUID kotlin.collections  VIDEO_EXTENSIONS kotlin.collections  VIDEO_PROJECTION kotlin.collections  VIDEO_URL_PATTERNS kotlin.collections  	VideoFile kotlin.collections  arrayOf kotlin.collections  contains kotlin.collections  	extension kotlin.collections  first kotlin.collections  forEach kotlin.collections  format kotlin.collections  isBlank kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  	lowercase kotlin.collections  mutableMapOf kotlin.collections  nameWithoutExtension kotlin.collections  newFixedLengthResponse kotlin.collections  set kotlin.collections  substringAfterLast kotlin.collections  
trimIndent kotlin.collections  use kotlin.collections  getLET kotlin.collections.List  getLet kotlin.collections.List  getFIRST kotlin.collections.MutableList  getFirst kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  Context kotlin.comparisons  DLNAManager kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileInputStream kotlin.comparisons  Intent kotlin.comparisons  MIME_PLAINTEXT kotlin.comparisons  
MediaStore kotlin.comparisons  NetworkInterface kotlin.comparisons  Pattern kotlin.comparisons  Response kotlin.comparisons  String kotlin.comparisons  Toast kotlin.comparisons  UUID kotlin.comparisons  VIDEO_EXTENSIONS kotlin.comparisons  VIDEO_PROJECTION kotlin.comparisons  VIDEO_URL_PATTERNS kotlin.comparisons  	VideoFile kotlin.comparisons  arrayOf kotlin.comparisons  contains kotlin.comparisons  	extension kotlin.comparisons  first kotlin.comparisons  forEach kotlin.comparisons  format kotlin.comparisons  isBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  joinToString kotlin.comparisons  	lowercase kotlin.comparisons  mutableMapOf kotlin.comparisons  nameWithoutExtension kotlin.comparisons  newFixedLengthResponse kotlin.comparisons  set kotlin.comparisons  substringAfterLast kotlin.comparisons  
trimIndent kotlin.comparisons  use kotlin.comparisons  Context 	kotlin.io  DLNAManager 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileInputStream 	kotlin.io  Intent 	kotlin.io  MIME_PLAINTEXT 	kotlin.io  
MediaStore 	kotlin.io  NetworkInterface 	kotlin.io  Pattern 	kotlin.io  Response 	kotlin.io  String 	kotlin.io  Toast 	kotlin.io  UUID 	kotlin.io  VIDEO_EXTENSIONS 	kotlin.io  VIDEO_PROJECTION 	kotlin.io  VIDEO_URL_PATTERNS 	kotlin.io  	VideoFile 	kotlin.io  arrayOf 	kotlin.io  contains 	kotlin.io  	extension 	kotlin.io  first 	kotlin.io  forEach 	kotlin.io  format 	kotlin.io  isBlank 	kotlin.io  
isNotEmpty 	kotlin.io  joinToString 	kotlin.io  	lowercase 	kotlin.io  mutableMapOf 	kotlin.io  nameWithoutExtension 	kotlin.io  newFixedLengthResponse 	kotlin.io  set 	kotlin.io  substringAfterLast 	kotlin.io  
trimIndent 	kotlin.io  use 	kotlin.io  Context 
kotlin.jvm  DLNAManager 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileInputStream 
kotlin.jvm  Intent 
kotlin.jvm  MIME_PLAINTEXT 
kotlin.jvm  
MediaStore 
kotlin.jvm  NetworkInterface 
kotlin.jvm  Pattern 
kotlin.jvm  Response 
kotlin.jvm  String 
kotlin.jvm  Toast 
kotlin.jvm  UUID 
kotlin.jvm  VIDEO_EXTENSIONS 
kotlin.jvm  VIDEO_PROJECTION 
kotlin.jvm  VIDEO_URL_PATTERNS 
kotlin.jvm  	VideoFile 
kotlin.jvm  arrayOf 
kotlin.jvm  contains 
kotlin.jvm  	extension 
kotlin.jvm  first 
kotlin.jvm  forEach 
kotlin.jvm  format 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  joinToString 
kotlin.jvm  	lowercase 
kotlin.jvm  mutableMapOf 
kotlin.jvm  nameWithoutExtension 
kotlin.jvm  newFixedLengthResponse 
kotlin.jvm  set 
kotlin.jvm  substringAfterLast 
kotlin.jvm  
trimIndent 
kotlin.jvm  use 
kotlin.jvm  Context 
kotlin.ranges  DLNAManager 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileInputStream 
kotlin.ranges  Intent 
kotlin.ranges  MIME_PLAINTEXT 
kotlin.ranges  
MediaStore 
kotlin.ranges  NetworkInterface 
kotlin.ranges  Pattern 
kotlin.ranges  Response 
kotlin.ranges  String 
kotlin.ranges  Toast 
kotlin.ranges  UUID 
kotlin.ranges  VIDEO_EXTENSIONS 
kotlin.ranges  VIDEO_PROJECTION 
kotlin.ranges  VIDEO_URL_PATTERNS 
kotlin.ranges  	VideoFile 
kotlin.ranges  arrayOf 
kotlin.ranges  contains 
kotlin.ranges  	extension 
kotlin.ranges  first 
kotlin.ranges  forEach 
kotlin.ranges  format 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  joinToString 
kotlin.ranges  	lowercase 
kotlin.ranges  mutableMapOf 
kotlin.ranges  nameWithoutExtension 
kotlin.ranges  newFixedLengthResponse 
kotlin.ranges  set 
kotlin.ranges  substringAfterLast 
kotlin.ranges  
trimIndent 
kotlin.ranges  use 
kotlin.ranges  Context kotlin.sequences  DLNAManager kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileInputStream kotlin.sequences  Intent kotlin.sequences  MIME_PLAINTEXT kotlin.sequences  
MediaStore kotlin.sequences  NetworkInterface kotlin.sequences  Pattern kotlin.sequences  Response kotlin.sequences  String kotlin.sequences  Toast kotlin.sequences  UUID kotlin.sequences  VIDEO_EXTENSIONS kotlin.sequences  VIDEO_PROJECTION kotlin.sequences  VIDEO_URL_PATTERNS kotlin.sequences  	VideoFile kotlin.sequences  arrayOf kotlin.sequences  contains kotlin.sequences  	extension kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  format kotlin.sequences  isBlank kotlin.sequences  
isNotEmpty kotlin.sequences  joinToString kotlin.sequences  	lowercase kotlin.sequences  mutableMapOf kotlin.sequences  nameWithoutExtension kotlin.sequences  newFixedLengthResponse kotlin.sequences  set kotlin.sequences  substringAfterLast kotlin.sequences  
trimIndent kotlin.sequences  use kotlin.sequences  Context kotlin.text  DLNAManager kotlin.text  	Exception kotlin.text  File kotlin.text  FileInputStream kotlin.text  Intent kotlin.text  MIME_PLAINTEXT kotlin.text  
MediaStore kotlin.text  NetworkInterface kotlin.text  Pattern kotlin.text  Response kotlin.text  String kotlin.text  Toast kotlin.text  UUID kotlin.text  VIDEO_EXTENSIONS kotlin.text  VIDEO_PROJECTION kotlin.text  VIDEO_URL_PATTERNS kotlin.text  	VideoFile kotlin.text  arrayOf kotlin.text  contains kotlin.text  	extension kotlin.text  first kotlin.text  forEach kotlin.text  format kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  joinToString kotlin.text  	lowercase kotlin.text  mutableMapOf kotlin.text  nameWithoutExtension kotlin.text  newFixedLengthResponse kotlin.text  set kotlin.text  substringAfterLast kotlin.text  
trimIndent kotlin.text  use kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             