+com.webvideocaster.cast.CastOptionsProviderBcom.webvideocaster.cast.ChromecastManager.MySessionManagerListener#com.webvideocaster.cast.UpnpService/com.webvideocaster.cast.UpnpService.LocalBinder#com.webvideocaster.model.DeviceType*com.webvideocaster.network.LocalHttpServer%com.webvideocaster.ui.BrowserActivity(com.webvideocaster.ui.LocalVideoActivity"com.webvideocaster.ui.MainActivity"com.webvideocaster.ui.VideoAdapter2com.webvideocaster.ui.VideoAdapter.VideoViewHolder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      