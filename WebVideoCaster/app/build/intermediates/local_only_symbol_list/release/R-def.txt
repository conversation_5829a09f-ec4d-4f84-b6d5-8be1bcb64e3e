R_DEF: Internal format may change without notice
local
color background
color black
color cast_connected
color cast_disconnected
color error
color primary_color
color primary_variant
color purple_200
color purple_500
color purple_700
color secondary_color
color secondary_variant
color success
color surface
color surface_variant
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color warning
color white
drawable bg_duration_badge
drawable ic_arrow_back
drawable ic_cast
drawable ic_cast_connected
drawable ic_folder
drawable ic_search
drawable ic_settings
drawable ic_video_library
drawable ic_video_placeholder
drawable ic_web
id btn_browse_web
id btn_cast
id btn_cast_settings
id btn_disconnect
id btn_local_videos
id cast_button
id cast_status_card
id chip_all
id chip_group_filters
id chip_large
id chip_recent
id content_container
id empty_container
id et_search
id fab_cast
id go_button
id iv_thumbnail
id loading_container
id rv_videos
id toolbar
id tv_cast_device_name
id tv_cast_status
id tv_duration
id tv_format
id tv_path
id tv_size
id tv_title
id tv_video_count
id url_edit_text
id web_view
layout activity_browser
layout activity_local_video
layout activity_main
layout item_video
string all_videos
string app_description
string app_name
string back
string browse_web_videos
string cancel
string cast
string cast_connected
string cast_failed
string cast_settings
string cast_video
string casting_to
string connected_to_device
string disconnect
string enter_url
string error_cast_failed
string error_file_not_found
string error_invalid_url
string error_network
string go
string large_files
string loading_videos
string local_videos
string no_cast_devices
string no_video_found
string no_videos_description
string no_videos_found
string ok
string permission_denied
string permission_granted
string permission_storage_message
string permission_storage_title
string ready_to_cast
string recent
string retry
string scanning_videos
string search_videos
string searching_devices
string select_cast_device
string settings
string video_count
string video_found
string video_thumbnail
style Theme.WebVideoCaster
