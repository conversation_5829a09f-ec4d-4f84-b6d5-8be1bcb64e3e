[{"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/layout_activity_browser.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/layout/activity_browser.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/layout_item_video.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/layout/item_video.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/layout_activity_local_video.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/layout/activity_local_video.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/layout_activity_main.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/layout/activity_main.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_arrow_back.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_arrow_back.xml"}, {"merged": "com.webvideocaster.app-merged_res-60:/layout_item_video.xml.flat", "source": "com.webvideocaster.app-main-61:/layout/item_video.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_bg_duration_badge.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/bg_duration_badge.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_video_library.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_video_library.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_web.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_web.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_search.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_search.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_cast_connected.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_cast_connected.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_settings.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_settings.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_folder.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_folder.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_cast.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_cast.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-merged_res-60:/drawable_ic_video_placeholder.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.9/com.webvideocaster.app-main-61:/drawable/ic_video_placeholder.xml"}]