{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-be/values-be.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-be/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "9491", "endColumns": "145", "endOffsets": "9632"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-be/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,320,374,452,530,580,733,818,901,1000,1088,1155,1237,1335,1380,1443,1507,1581,1635,1683,1724,1768,1820,1893,1967,2026,2096,2167,2209,2295,2358,2426,2502,2582,2643,2700,2770,2819", "endColumns": "45,65,53,77,77,49,152,84,82,98,87,66,81,97,44,62,63,73,53,47,40,43,51,72,73,58,69,70,41,85,62,67,75,79,60,56,69,48,66", "endOffsets": "253,319,373,451,529,579,732,817,900,999,1087,1154,1236,1334,1379,1442,1506,1580,1634,1682,1723,1767,1819,1892,1966,2025,2095,2166,2208,2294,2357,2425,2501,2581,2642,2699,2769,2818,2885"}, "to": {"startLines": "51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4770,4820,4890,4948,5030,5112,5237,5394,5483,5570,5673,5765,5836,5922,6024,6073,6140,6208,6286,6344,6713,6758,6806,6862,6939,7017,7080,7154,7229,7275,7365,7432,7504,7584,7668,7733,7794,7868,16337", "endColumns": "49,69,57,81,81,53,156,88,86,102,91,70,85,101,48,66,67,77,57,51,44,47,55,76,77,62,73,74,45,89,66,71,79,83,64,60,73,52,70", "endOffsets": "4815,4885,4943,5025,5107,5161,5389,5478,5565,5668,5760,5831,5917,6019,6068,6135,6203,6281,6339,6391,6753,6801,6857,6934,7012,7075,7149,7224,7270,7360,7427,7499,7579,7663,7728,7789,7863,7916,16403"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,394,507,584,677,788,907,1018,1159,1239,1346,1435,1528,1638,1757,1863,2006,2148,2282,2455,2583,2704,2830,2949,3039,3133,3255,3384,3479,3589,3694,3839,3988,4092,4187,4268,4346,4428,4511,4607,4690,4773,4869,4971,5063,5157,5242,5346,5438,5533,5675,5761,5876", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "163,274,389,502,579,672,783,902,1013,1154,1234,1341,1430,1523,1633,1752,1858,2001,2143,2277,2450,2578,2699,2825,2944,3034,3128,3250,3379,3474,3584,3689,3834,3983,4087,4182,4263,4341,4423,4506,4602,4685,4768,4864,4966,5058,5152,5237,5341,5433,5528,5670,5756,5871,5963"}, "to": {"startLines": "35,36,37,38,99,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151,154,258,261,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3162,3275,3386,3501,8410,10719,10812,10923,11042,11153,11294,11374,11481,11570,11663,11773,11892,11998,12141,12283,12417,12590,12718,12839,12965,13084,13174,13268,13390,13519,13614,13724,13829,13974,14123,14227,14519,14753,24474,24710,24894,25268,25351,25434,25530,25632,25724,25818,25903,26007,26099,26194,26336,26422,26537", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "3270,3381,3496,3609,8482,10807,10918,11037,11148,11289,11369,11476,11565,11658,11768,11887,11993,12136,12278,12412,12585,12713,12834,12960,13079,13169,13263,13385,13514,13609,13719,13824,13969,14118,14222,14317,14595,14826,24551,24788,24985,25346,25429,25525,25627,25719,25813,25898,26002,26094,26189,26331,26417,26532,26624"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-be/values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,3033,3124,3207,3264,3315,3381,3453,3530,3614,3697,3772,3849,3931,4007,4115,4204,4286,4377,4473,4547,4628,4723,4777,4859,4925,5012,5098,5160,5224,5287,5356,5466,5579,5682,5789,5850,5905", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,3028,3119,3202,3259,3310,3376,3448,3525,3609,3692,3767,3844,3926,4002,4110,4199,4281,4372,4468,4542,4623,4718,4772,4854,4920,5007,5093,5155,5219,5282,5351,5461,5574,5677,5784,5845,5900,5980"}, "to": {"startLines": "2,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3614,3691,3768,3850,3947,7921,8018,8150,14686,14831,15043,15289,15352,15468,15531,15600,15659,15730,15789,15843,15964,16025,16088,16142,16215,20412,20500,20583,20735,20821,20908,21041,21132,21215,21272,21323,21389,21461,21538,21622,21705,21780,21857,21939,22015,22123,22212,22294,22385,22481,22555,22636,22731,22785,22867,22933,23020,23106,23168,23232,23295,23364,23474,23587,23690,23797,23858,24224", "endLines": "7,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "427,3686,3763,3845,3942,4034,8013,8145,8228,14748,14919,15115,15347,15463,15526,15595,15654,15725,15784,15838,15959,16020,16083,16137,16210,16332,20495,20578,20730,20816,20903,21036,21127,21210,21267,21318,21384,21456,21533,21617,21700,21775,21852,21934,22010,22118,22207,22289,22380,22476,22550,22631,22726,22780,22862,22928,23015,23101,23163,23227,23290,23359,23469,23582,23685,23792,23853,23908,24299"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "97,98,149,150,152,158,159,251,252,253,254,256,257,260,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8233,8326,14322,14416,14600,15120,15200,23913,24001,24083,24154,24304,24387,24638,24990,25075,25145", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "8321,8405,14411,14514,14681,15195,15284,23996,24078,24149,24219,24382,24469,24705,25070,25140,25263"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,276,385,501,612,730,838,930,1030,1210,1390,1569,1749,1927,2105,2286,2401,2500,2617,2715,2811,2903,2999,3110,3223,3310,3397,3482,3585,3690,3783,3883,3997,4084", "endColumns": "118,101,108,115,110,117,107,91,99,179,179,178,179,177,177,180,114,98,116,97,95,91,95,110,112,86,86,84,102,104,92,99,113,86,93", "endOffsets": "169,271,380,496,607,725,833,925,1025,1205,1385,1564,1744,1922,2100,2281,2396,2495,2612,2710,2806,2898,2994,3105,3218,3305,3392,3477,3580,3685,3778,3878,3992,4079,4173"}, "to": {"startLines": "156,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14924,16408,16510,16619,16735,16846,16964,17072,17164,17264,17444,17624,17803,17983,18161,18339,18520,18635,18734,18851,18949,19045,19137,19233,19344,19457,19544,19631,19716,19819,19924,20017,20117,20231,20318", "endColumns": "118,101,108,115,110,117,107,91,99,179,179,178,179,177,177,180,114,98,116,97,95,91,95,110,112,86,86,84,102,104,92,99,113,86,93", "endOffsets": "15038,16505,16614,16730,16841,16959,17067,17159,17259,17439,17619,17798,17978,18156,18334,18515,18630,18729,18846,18944,19040,19132,19228,19339,19452,19539,19626,19711,19814,19919,20012,20112,20226,20313,20407"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-be/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8487,8594,8756,8881,8991,9146,9272,9387,9637,9799,9906,10069,10197,10350,10509,10578,10640", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "8589,8751,8876,8986,9141,9267,9382,9486,9794,9901,10064,10192,10345,10504,10573,10635,10714"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,24556", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,24633"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-be/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,354,435,503", "endColumns": "66,88,80,67,62", "endOffsets": "264,353,434,502,565"}, "to": {"startLines": "57,72,73,74,75", "startColumns": "4,4,4,4,4", "startOffsets": "5166,6396,6489,6574,6646", "endColumns": "70,92,84,71,66", "endOffsets": "5232,6484,6569,6641,6708"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "44,45,46,47,48,49,50,262", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4039,4137,4239,4339,4440,4546,4649,24793", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "4132,4234,4334,4435,4541,4644,4765,24889"}}]}]}