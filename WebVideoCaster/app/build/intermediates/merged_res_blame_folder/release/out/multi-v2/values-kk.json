{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-kk/values-kk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3880,3975,4077,4179,4282,4386,4483,24294", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3970,4072,4174,4277,4381,4478,4589,24390"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-kk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,329,385,457,531,579,718,803,883,1001,1091,1159,1247,1369,1411,1466,1523,1597,1650,1698,1741,1782,1834,1899,1966,2021,2081,2142,2184,2253,2316,2384,2462,2542,2607,2669,2741,2790", "endColumns": "45,74,55,71,73,47,138,84,79,117,89,67,87,121,41,54,56,73,52,47,42,40,51,64,66,54,59,60,41,68,62,67,77,79,64,61,71,48,69", "endOffsets": "253,328,384,456,530,578,717,802,882,1000,1090,1158,1246,1368,1410,1465,1522,1596,1649,1697,1740,1781,1833,1898,1965,2020,2080,2141,2183,2252,2315,2383,2461,2541,2606,2668,2740,2789,2859"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4594,4644,4723,4783,4859,4937,5060,5203,5292,5376,5498,5592,5664,5756,5882,5928,5987,6048,6126,6183,6546,6593,6638,6694,6763,6834,6893,6957,7022,7068,7141,7208,7280,7362,7446,7515,7581,7657,15980", "endColumns": "49,78,59,75,77,51,142,88,83,121,93,71,91,125,45,58,60,77,56,51,46,44,55,68,70,58,63,64,45,72,66,71,81,83,68,65,75,52,73", "endOffsets": "4639,4718,4778,4854,4932,4984,5198,5287,5371,5493,5587,5659,5751,5877,5923,5982,6043,6121,6178,6230,6588,6633,6689,6758,6829,6888,6952,7017,7063,7136,7203,7275,7357,7441,7510,7576,7652,7705,16049"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-kk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9233", "endColumns": "163", "endOffsets": "9392"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,394,506,581,678,793,927,1048,1169,1249,1343,1433,1526,1638,1758,1862,2000,2136,2264,2419,2538,2649,2765,2877,2964,3058,3177,3308,3408,3515,3617,3751,3892,3996,4091,4173,4248,4330,4410,4510,4595,4676,4773,4873,4966,5058,5141,5241,5336,5429,5562,5648,5755", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "164,278,389,501,576,673,788,922,1043,1164,1244,1338,1428,1521,1633,1753,1857,1995,2131,2259,2414,2533,2644,2760,2872,2959,3053,3172,3303,3403,3510,3612,3746,3887,3991,4086,4168,4243,4325,4405,4505,4590,4671,4768,4868,4961,5053,5136,5236,5331,5424,5557,5643,5750,5847"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3014,3128,3242,3353,8172,10464,10561,10676,10810,10931,11052,11132,11226,11316,11409,11521,11641,11745,11883,12019,12147,12302,12421,12532,12648,12760,12847,12941,13060,13191,13291,13398,13500,13634,13775,13879,14181,14414,23977,24214,24395,24759,24844,24925,25022,25122,25215,25307,25390,25490,25585,25678,25811,25897,26004", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "3123,3237,3348,3460,8242,10556,10671,10805,10926,11047,11127,11221,11311,11404,11516,11636,11740,11878,12014,12142,12297,12416,12527,12643,12755,12842,12936,13055,13186,13286,13393,13495,13629,13770,13874,13969,14258,14484,24054,24289,24490,24839,24920,25017,25117,25210,25302,25385,25485,25580,25673,25806,25892,25999,26096"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-kk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8247,8350,8503,8629,8735,8875,9001,9124,9397,9562,9668,9825,9954,10107,10264,10327,10386", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "8345,8498,8624,8730,8870,8996,9119,9228,9557,9663,9820,9949,10102,10259,10322,10381,10459"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,24059", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,24136"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-kk/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,347,431,499", "endColumns": "66,81,83,67,60", "endOffsets": "264,346,430,498,559"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4989,6235,6321,6409,6481", "endColumns": "70,85,87,71,64", "endOffsets": "5055,6316,6404,6476,6541"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,274,381,489,602,719,828,932,1031,1209,1381,1557,1734,1909,2087,2261,2381,2482,2592,2687,2778,2869,2958,3068,3189,3274,3358,3443,3548,3662,3753,3853,3954,4038", "endColumns": "116,101,106,107,112,116,108,103,98,177,171,175,176,174,177,173,119,100,109,94,90,90,88,109,120,84,83,84,104,113,90,99,100,83,96", "endOffsets": "167,269,376,484,597,714,823,927,1026,1204,1376,1552,1729,1904,2082,2256,2376,2477,2587,2682,2773,2864,2953,3063,3184,3269,3353,3438,3543,3657,3748,3848,3949,4033,4130"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14584,16054,16156,16263,16371,16484,16601,16710,16814,16913,17091,17263,17439,17616,17791,17969,18143,18263,18364,18474,18569,18660,18751,18840,18950,19071,19156,19240,19325,19430,19544,19635,19735,19836,19920", "endColumns": "116,101,106,107,112,116,108,103,98,177,171,175,176,174,177,173,119,100,109,94,90,90,88,109,120,84,83,84,104,113,90,99,100,83,96", "endOffsets": "14696,16151,16258,16366,16479,16596,16705,16809,16908,17086,17258,17434,17611,17786,17964,18138,18258,18359,18469,18564,18655,18746,18835,18945,19066,19151,19235,19320,19425,19539,19630,19730,19831,19915,20012"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,990,1058,1141,1226,1299,1375,1445", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,985,1053,1136,1221,1294,1370,1440,1558"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7996,8089,13974,14079,14263,14771,14852,23416,23506,23588,23657,23809,23892,24141,24495,24571,24641", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "8084,8167,14074,14176,14345,14847,14940,23501,23583,23652,23720,23887,23972,24209,24566,24636,24754"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1038,1133,1203,1266,1373,1438,1505,1566,1633,1695,1749,1863,1922,1983,2037,2112,2238,2326,2416,2558,2630,2703,2840,2929,3010,3067,3123,3189,3260,3337,3423,3503,3575,3651,3732,3802,3902,3989,4061,4152,4245,4319,4394,4486,4538,4620,4686,4770,4856,4918,4982,5045,5114,5218,5322,5416,5516,5577,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "268,346,422,501,595,683,775,887,969,1033,1128,1198,1261,1368,1433,1500,1561,1628,1690,1744,1858,1917,1978,2032,2107,2233,2321,2411,2553,2625,2698,2835,2924,3005,3062,3118,3184,3255,3332,3418,3498,3570,3646,3727,3797,3897,3984,4056,4147,4240,4314,4389,4481,4533,4615,4681,4765,4851,4913,4977,5040,5109,5213,5317,5411,5511,5572,5632,5716"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3465,3543,3619,3698,3792,7710,7802,7914,14350,14489,14701,14945,15008,15115,15180,15247,15308,15375,15437,15491,15605,15664,15725,15779,15854,20017,20105,20195,20337,20409,20482,20619,20708,20789,20846,20902,20968,21039,21116,21202,21282,21354,21430,21511,21581,21681,21768,21840,21931,22024,22098,22173,22265,22317,22399,22465,22549,22635,22697,22761,22824,22893,22997,23101,23195,23295,23356,23725", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "318,3538,3614,3693,3787,3875,7797,7909,7991,14409,14579,14766,15003,15110,15175,15242,15303,15370,15432,15486,15600,15659,15720,15774,15849,15975,20100,20190,20332,20404,20477,20614,20703,20784,20841,20897,20963,21034,21111,21197,21277,21349,21425,21506,21576,21676,21763,21835,21926,22019,22093,22168,22260,22312,22394,22460,22544,22630,22692,22756,22819,22888,22992,23096,23190,23290,23351,23411,23804"}}]}]}