{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-lt/values-lt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-lt/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,256,323,378,458,534,583,707,794,876,997,1092,1159,1241,1340,1384,1448,1516,1590,1641,1685,1732,1773,1823,1893,1967,2033,2099,2171,2216,2291,2355,2422,2499,2582,2643,2703,2774,2824", "endColumns": "47,66,54,79,75,48,123,86,81,120,94,66,81,98,43,63,67,73,50,43,46,40,49,69,73,65,65,71,44,74,63,66,76,82,60,59,70,49,60", "endOffsets": "255,322,377,457,533,582,706,793,875,996,1091,1158,1240,1339,1383,1447,1515,1589,1640,1684,1731,1772,1822,1892,1966,2032,2098,2170,2215,2290,2354,2421,2498,2581,2642,2702,2773,2823,2884"}, "to": {"startLines": "51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4827,4879,4950,5009,5093,5173,5297,5425,5516,5602,5727,5826,5897,5983,6086,6134,6202,6274,6352,6407,6774,6825,6870,6924,6998,7076,7146,7216,7292,7341,7420,7488,7559,7640,7727,7792,7856,7931,16431", "endColumns": "51,70,58,83,79,52,127,90,85,124,98,70,85,102,47,67,71,77,54,47,50,44,53,73,77,69,69,75,48,78,67,70,80,86,64,63,74,53,64", "endOffsets": "4874,4945,5004,5088,5168,5221,5420,5511,5597,5722,5821,5892,5978,6081,6129,6197,6269,6347,6402,6450,6820,6865,6919,6993,7071,7141,7211,7287,7336,7415,7483,7554,7635,7722,7787,7851,7926,7980,16491"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "35,36,37,38,99,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151,154,258,261,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3194,3309,3425,3538,8460,10804,10894,11002,11130,11242,11382,11462,11557,11649,11744,11865,11979,12079,12214,12345,12480,12672,12798,12912,13035,13159,13252,13349,13467,13592,13686,13785,13888,14021,14165,14270,14572,14813,24646,24889,25076,25468,25551,25634,25730,25835,25927,26022,26106,26213,26305,26400,26534,26614,26713", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "3304,3420,3533,3647,8533,10889,10997,11125,11237,11377,11457,11552,11644,11739,11860,11974,12074,12209,12340,12475,12667,12793,12907,13030,13154,13247,13344,13462,13587,13681,13780,13883,14016,14160,14265,14364,14647,14886,24725,24970,25178,25546,25629,25725,25830,25922,26017,26101,26208,26300,26395,26529,26609,26708,26801"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "44,45,46,47,48,49,50,262", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4076,4174,4284,4383,4486,4597,4707,24975", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "4169,4279,4378,4481,4592,4702,4822,25071"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-lt/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "9544", "endColumns": "158", "endOffsets": "9698"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-lt/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8538,8645,8797,8929,9036,9189,9319,9438,9703,9869,9978,10143,10277,10430,10580,10648,10714", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "8640,8792,8924,9031,9184,9314,9433,9539,9864,9973,10138,10272,10425,10575,10643,10709,10799"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,3030,3127,3204,3260,3314,3380,3455,3534,3622,3702,3778,3856,3929,4006,4113,4200,4281,4371,4463,4535,4616,4708,4763,4845,4911,4996,5083,5145,5209,5272,5344,5455,5571,5672,5781,5841,5899", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,3025,3122,3199,3255,3309,3375,3450,3529,3617,3697,3773,3851,3924,4001,4108,4195,4276,4366,4458,4530,4611,4703,4758,4840,4906,4991,5078,5140,5204,5267,5339,5450,5566,5667,5776,5836,5894,5976"}, "to": {"startLines": "2,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3652,3731,3809,3892,3986,7985,8081,8199,14747,14891,15114,15360,15425,15535,15598,15670,15729,15803,15864,15918,16042,16103,16165,16219,16297,20592,20680,20764,20905,20984,21068,21211,21308,21385,21441,21495,21561,21636,21715,21803,21883,21959,22037,22110,22187,22294,22381,22462,22552,22644,22716,22797,22889,22944,23026,23092,23177,23264,23326,23390,23453,23525,23636,23752,23853,23962,24022,24390", "endLines": "7,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "420,3726,3804,3887,3981,4071,8076,8194,8278,14808,14985,15187,15420,15530,15593,15665,15724,15798,15859,15913,16037,16098,16160,16214,16292,16426,20675,20759,20900,20979,21063,21206,21303,21380,21436,21490,21556,21631,21710,21798,21878,21954,22032,22105,22182,22289,22376,22457,22547,22639,22711,22792,22884,22939,23021,23087,23172,23259,23321,23385,23448,23520,23631,23747,23848,23957,24017,24075,24467"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-lt/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,349,436,504", "endColumns": "66,83,86,67,63", "endOffsets": "264,348,435,503,567"}, "to": {"startLines": "57,72,73,74,75", "startColumns": "4,4,4,4,4", "startOffsets": "5226,6455,6543,6634,6706", "endColumns": "70,87,90,71,67", "endOffsets": "5292,6538,6629,6701,6769"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,24730", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,24809"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "97,98,149,150,152,158,159,251,252,253,254,256,257,260,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8283,8376,14369,14467,14652,15192,15269,24080,24167,24251,24321,24472,24558,24814,25183,25263,25346", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "8371,8455,14462,14567,14742,15264,15355,24162,24246,24316,24385,24553,24641,24884,25258,25341,25463"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,276,381,489,596,717,827,925,1011,1194,1387,1580,1788,1981,2176,2369,2494,2592,2699,2798,2895,2987,3083,3197,3315,3404,3488,3576,3681,3787,3880,3986,4091,4178", "endColumns": "123,96,104,107,106,120,109,97,85,182,192,192,207,192,194,192,124,97,106,98,96,91,95,113,117,88,83,87,104,105,92,105,104,86,96", "endOffsets": "174,271,376,484,591,712,822,920,1006,1189,1382,1575,1783,1976,2171,2364,2489,2587,2694,2793,2890,2982,3078,3192,3310,3399,3483,3571,3676,3782,3875,3981,4086,4173,4270"}, "to": {"startLines": "156,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14990,16496,16593,16698,16806,16913,17034,17144,17242,17328,17511,17704,17897,18105,18298,18493,18686,18811,18909,19016,19115,19212,19304,19400,19514,19632,19721,19805,19893,19998,20104,20197,20303,20408,20495", "endColumns": "123,96,104,107,106,120,109,97,85,182,192,192,207,192,194,192,124,97,106,98,96,91,95,113,117,88,83,87,104,105,92,105,104,86,96", "endOffsets": "15109,16588,16693,16801,16908,17029,17139,17237,17323,17506,17699,17892,18100,18293,18488,18681,18806,18904,19011,19110,19207,19299,19395,19509,19627,19716,19800,19888,19993,20099,20192,20298,20403,20490,20587"}}]}]}