{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-si/values-si.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-si/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,259,328,386,471,545,599,734,823,900,1017,1102,1173,1259,1365,1416,1476,1536,1610,1656,1701,1748,1794,1841,1903,1965,2025,2084,2143,2187,2258,2325,2396,2476,2566,2634,2692,2762,2820", "endColumns": "50,68,57,84,73,53,134,88,76,116,84,70,85,105,50,59,59,73,45,44,46,45,46,61,61,59,58,58,43,70,66,70,79,89,67,57,69,57,62", "endOffsets": "258,327,385,470,544,598,733,822,899,1016,1101,1172,1258,1364,1415,1475,1535,1609,1655,1700,1747,1793,1840,1902,1964,2024,2083,2142,2186,2257,2324,2395,2475,2565,2633,2691,2761,2819,2882"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4619,4674,4747,4809,4898,4976,5105,5244,5337,5418,5539,5628,5703,5793,5903,5958,6022,6086,6164,6214,6581,6632,6682,6733,6799,6865,6929,6992,7055,7103,7178,7249,7324,7408,7502,7574,7636,7710,15930", "endColumns": "54,72,61,88,77,57,138,92,80,120,88,74,89,109,54,63,63,77,49,48,50,49,50,65,65,63,62,62,47,74,70,74,83,93,71,61,73,61,66", "endOffsets": "4669,4742,4804,4893,4971,5029,5239,5332,5413,5534,5623,5698,5788,5898,5953,6017,6081,6159,6209,6258,6627,6677,6728,6794,6860,6924,6987,7050,7098,7173,7244,7319,7403,7497,7569,7631,7705,7767,15992"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3887,3989,4092,4197,4302,4401,4505,24112", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3984,4087,4192,4297,4396,4500,4614,24208"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-si/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8322,8430,8584,8708,8821,8963,9087,9203,9440,9591,9706,9862,9993,10137,10298,10371,10432", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "8425,8579,8703,8816,8958,9082,9198,9296,9586,9701,9857,9988,10132,10293,10366,10427,10507"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-si/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9301", "endColumns": "138", "endOffsets": "9435"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8072,8161,13953,14052,14223,14736,14821,23231,23317,23397,23474,23629,23708,23954,24303,24384,24451", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "8156,8239,14047,14146,14300,14816,14907,23312,23392,23469,23544,23703,23785,24022,24379,24446,24564"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-si/values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1040,1129,1196,1256,1350,1414,1477,1533,1603,1670,1725,1844,1901,1965,2019,2092,2214,2297,2382,2514,2592,2672,2794,2880,2964,3024,3076,3142,3212,3285,3367,3444,3516,3593,3665,3735,3848,3941,4014,4104,4197,4271,4343,4434,4488,4568,4634,4718,4803,4865,4929,4992,5058,5163,5268,5363,5464,5528,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,121,85,83,59,51,65,69,72,81,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79", "endOffsets": "264,340,417,495,586,671,773,888,971,1035,1124,1191,1251,1345,1409,1472,1528,1598,1665,1720,1839,1896,1960,2014,2087,2209,2292,2377,2509,2587,2667,2789,2875,2959,3019,3071,3137,3207,3280,3362,3439,3511,3588,3660,3730,3843,3936,4009,4099,4192,4266,4338,4429,4483,4563,4629,4713,4798,4860,4924,4987,5053,5158,5263,5358,5459,5523,5579,5659"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3480,3556,3633,3711,3802,7772,7874,7989,14305,14449,14669,14912,14972,15066,15130,15193,15249,15319,15386,15441,15560,15617,15681,15735,15808,19861,19944,20029,20161,20239,20319,20441,20527,20611,20671,20723,20789,20859,20932,21014,21091,21163,21240,21312,21382,21495,21588,21661,21751,21844,21918,21990,22081,22135,22215,22281,22365,22450,22512,22576,22639,22705,22810,22915,23010,23111,23175,23549", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,121,85,83,59,51,65,69,72,81,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79", "endOffsets": "314,3551,3628,3706,3797,3882,7869,7984,8067,14364,14533,14731,14967,15061,15125,15188,15244,15314,15381,15436,15555,15612,15676,15730,15803,15925,19939,20024,20156,20234,20314,20436,20522,20606,20666,20718,20784,20854,20927,21009,21086,21158,21235,21307,21377,21490,21583,21656,21746,21839,21913,21985,22076,22130,22210,22276,22360,22445,22507,22571,22634,22700,22805,22910,23005,23106,23170,23226,23624"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,23872", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,23949"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-si/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,347,432,500", "endColumns": "66,81,84,67,66", "endOffsets": "264,346,431,499,566"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5034,6263,6349,6438,6510", "endColumns": "70,85,88,71,70", "endOffsets": "5100,6344,6433,6505,6576"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,287,393,500,611,733,844,935,1017,1193,1361,1529,1698,1858,2027,2195,2310,2403,2501,2597,2692,2789,2885,3000,3106,3190,3279,3365,3463,3567,3660,3764,3871,3958", "endColumns": "130,100,105,106,110,121,110,90,81,175,167,167,168,159,168,167,114,92,97,95,94,96,95,114,105,83,88,85,97,103,92,103,106,86,91", "endOffsets": "181,282,388,495,606,728,839,930,1012,1188,1356,1524,1693,1853,2022,2190,2305,2398,2496,2592,2687,2784,2880,2995,3101,3185,3274,3360,3458,3562,3655,3759,3866,3953,4045"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14538,15997,16098,16204,16311,16422,16544,16655,16746,16828,17004,17172,17340,17509,17669,17838,18006,18121,18214,18312,18408,18503,18600,18696,18811,18917,19001,19090,19176,19274,19378,19471,19575,19682,19769", "endColumns": "130,100,105,106,110,121,110,90,81,175,167,167,168,159,168,167,114,92,97,95,94,96,95,114,105,83,88,85,97,103,92,103,106,86,91", "endOffsets": "14664,16093,16199,16306,16417,16539,16650,16741,16823,16999,17167,17335,17504,17664,17833,18001,18116,18209,18307,18403,18498,18595,18691,18806,18912,18996,19085,19171,19269,19373,19466,19570,19677,19764,19856"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,280,387,500,578,670,776,901,1011,1146,1226,1328,1415,1504,1614,1738,1845,1969,2091,2220,2389,2509,2622,2741,2859,2947,3038,3153,3270,3367,3466,3566,3692,3824,3927,4019,4091,4171,4253,4338,4428,4507,4586,4681,4777,4865,4962,5046,5149,5247,5354,5471,5549,5653", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "162,275,382,495,573,665,771,896,1006,1141,1221,1323,1410,1499,1609,1733,1840,1964,2086,2215,2384,2504,2617,2736,2854,2942,3033,3148,3265,3362,3461,3561,3687,3819,3922,4014,4086,4166,4248,4333,4423,4502,4581,4676,4772,4860,4957,5041,5144,5242,5349,5466,5544,5648,5743"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3035,3147,3260,3367,8244,10512,10604,10710,10835,10945,11080,11160,11262,11349,11438,11548,11672,11779,11903,12025,12154,12323,12443,12556,12675,12793,12881,12972,13087,13204,13301,13400,13500,13626,13758,13861,14151,14369,23790,24027,24213,24569,24648,24727,24822,24918,25006,25103,25187,25290,25388,25495,25612,25690,25794", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "3142,3255,3362,3475,8317,10599,10705,10830,10940,11075,11155,11257,11344,11433,11543,11667,11774,11898,12020,12149,12318,12438,12551,12670,12788,12876,12967,13082,13199,13296,13395,13495,13621,13753,13856,13948,14218,14444,23867,24107,24298,24643,24722,24817,24913,25001,25098,25182,25285,25383,25490,25607,25685,25789,25884"}}]}]}