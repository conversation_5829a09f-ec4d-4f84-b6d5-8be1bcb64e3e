{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "212,258,328,389,476,544,596,733,819,899,1024,1115,1184,1261,1358,1403,1464,1528,1602,1650,1698,1740,1785,1828,1887,1945,2015,2080,2147,2187,2257,2320,2387,2471,2555,2618,2675,2745,2792", "endColumns": "45,69,60,86,67,51,136,85,79,124,90,68,76,96,44,60,63,73,47,47,41,44,42,58,57,69,64,66,39,69,62,66,83,83,62,56,69,46,67", "endOffsets": "257,327,388,475,543,595,732,818,898,1023,1114,1183,1260,1357,1402,1463,1527,1601,1649,1697,1739,1784,1827,1886,1944,2014,2079,2146,2186,2256,2319,2386,2470,2554,2617,2674,2744,2791,2859"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4679,4729,4803,4868,4959,5031,5158,5299,5389,5473,5602,5697,5770,5851,5952,6001,6066,6134,6212,6264,6627,6673,6722,6769,6832,6894,6968,7037,7108,7152,7226,7293,7364,7452,7540,7607,7668,7742,16217", "endColumns": "49,73,64,90,71,55,140,89,83,128,94,72,80,100,48,64,67,77,51,51,45,48,46,62,61,73,68,70,43,73,66,70,87,87,66,60,73,50,71", "endOffsets": "4724,4798,4863,4954,5026,5082,5294,5384,5468,5597,5692,5765,5846,5947,5996,6061,6129,6207,6259,6311,6668,6717,6764,6827,6889,6963,7032,7103,7147,7221,7288,7359,7447,7535,7602,7663,7737,7788,16284"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9364", "endColumns": "144", "endOffsets": "9504"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,282,387,494,605,731,845,949,1040,1215,1375,1542,1706,1862,2032,2196,2319,2415,2520,2617,2713,2808,2899,3018,3134,3218,3306,3389,3491,3607,3700,3810,3921,4008", "endColumns": "127,98,104,106,110,125,113,103,90,174,159,166,163,155,169,163,122,95,104,96,95,94,90,118,115,83,87,82,101,115,92,109,110,86,98", "endOffsets": "178,277,382,489,600,726,840,944,1035,1210,1370,1537,1701,1857,2027,2191,2314,2410,2515,2612,2708,2803,2894,3013,3129,3213,3301,3384,3486,3602,3695,3805,3916,4003,4102"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14768,16289,16388,16493,16600,16711,16837,16951,17055,17146,17321,17481,17648,17812,17968,18138,18302,18425,18521,18626,18723,18819,18914,19005,19124,19240,19324,19412,19495,19597,19713,19806,19916,20027,20114", "endColumns": "127,98,104,106,110,125,113,103,90,174,159,166,163,155,169,163,122,95,104,96,95,94,90,118,115,83,87,82,101,115,92,109,110,86,98", "endOffsets": "14891,16383,16488,16595,16706,16832,16946,17050,17141,17316,17476,17643,17807,17963,18133,18297,18420,18516,18621,18718,18814,18909,19000,19119,19235,19319,19407,19490,19592,19708,19801,19911,20022,20109,20208"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,24336", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,24417"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3061,3176,3293,3405,8276,10614,10706,10816,10946,11060,11207,11287,11385,11476,11572,11683,11809,11912,12047,12181,12317,12479,12611,12727,12848,12972,13064,13157,13273,13385,13481,13588,13693,13829,13970,14076,14370,14602,24251,24498,24684,25037,25113,25193,25290,25392,25480,25575,25659,25767,25864,25963,26078,26154,26250", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "3171,3288,3400,3512,8347,10701,10811,10941,11055,11202,11282,11380,11471,11567,11678,11804,11907,12042,12176,12312,12474,12606,12722,12843,12967,13059,13152,13268,13380,13476,13583,13688,13824,13965,14071,14169,14447,14671,24331,24578,24776,25108,25188,25285,25387,25475,25570,25654,25762,25859,25958,26073,26149,26245,26333"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "202,269,346,425,499", "endColumns": "66,76,78,73,64", "endOffsets": "268,345,424,498,563"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5087,6316,6397,6480,6558", "endColumns": "70,80,82,77,68", "endOffsets": "5153,6392,6475,6553,6622"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8095,8190,14174,14271,14452,14975,15058,23680,23771,23858,23930,24076,24161,24422,24781,24857,24924", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "8185,8271,14266,14365,14533,15053,15150,23766,23853,23925,23994,24156,24246,24493,24852,24919,25032"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-pt-rBR/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8352,8457,8605,8732,8840,9007,9137,9259,9509,9679,9787,9951,10081,10238,10395,10464,10530", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "8452,8600,8727,8835,9002,9132,9254,9359,9674,9782,9946,10076,10233,10390,10459,10525,10609"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2913,3000,3080,3136,3187,3253,3327,3407,3494,3577,3650,3727,3796,3870,3972,4060,4137,4230,4326,4400,4480,4577,4629,4713,4779,4866,4954,5016,5080,5143,5211,5323,5434,5541,5651,5711,5766", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2908,2995,3075,3131,3182,3248,3322,3402,3489,3572,3645,3722,3791,3865,3967,4055,4132,4225,4321,4395,4475,4572,4624,4708,4774,4861,4949,5011,5075,5138,5206,5318,5429,5536,5646,5706,5761,5838"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3517,3598,3676,3760,3855,7793,7894,8014,14538,14676,14896,15155,15215,15305,15369,15440,15503,15578,15642,15696,15823,15881,15943,15997,16076,20213,20300,20382,20521,20604,20688,20827,20914,20994,21050,21101,21167,21241,21321,21408,21491,21564,21641,21710,21784,21886,21974,22051,22144,22240,22314,22394,22491,22543,22627,22693,22780,22868,22930,22994,23057,23125,23237,23348,23455,23565,23625,23999", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "318,3593,3671,3755,3850,3939,7889,8009,8090,14597,14763,14970,15210,15300,15364,15435,15498,15573,15637,15691,15818,15876,15938,15992,16071,16212,20295,20377,20516,20599,20683,20822,20909,20989,21045,21096,21162,21236,21316,21403,21486,21559,21636,21705,21779,21881,21969,22046,22139,22235,22309,22389,22486,22538,22622,22688,22775,22863,22925,22989,23052,23120,23232,23343,23450,23560,23620,23675,24071"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3944,4041,4143,4242,4342,4449,4559,24583", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "4036,4138,4237,4337,4444,4554,4674,24679"}}]}]}