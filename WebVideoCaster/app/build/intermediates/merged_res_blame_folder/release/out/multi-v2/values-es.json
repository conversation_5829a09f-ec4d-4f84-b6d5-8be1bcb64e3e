{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-es/values-es.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8148,8244,14330,14428,14614,15151,15230,24029,24121,24208,24281,24434,24520,24777,25144,25226,25296", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "8239,8321,14423,14526,14698,15225,15318,24116,24203,24276,24346,24515,24606,24849,25221,25291,25412"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-es/values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3527,3608,3687,3774,3875,7841,7945,8067,14703,14846,15070,15323,15386,15475,15539,15608,15671,15745,15809,15865,15983,16041,16103,16159,16239,20513,20602,20684,20825,20906,20986,21137,21227,21307,21363,21419,21485,21564,21646,21734,21823,21897,21974,22044,22123,22223,22307,22391,22483,22583,22657,22738,22840,22893,22978,23045,23138,23227,23289,23353,23416,23484,23597,23704,23808,23909,23969,24351", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "323,3603,3682,3769,3870,3966,7940,8062,8143,14763,14936,15146,15381,15470,15534,15603,15666,15740,15804,15860,15978,16036,16098,16154,16234,16373,20597,20679,20820,20901,20981,21132,21222,21302,21358,21414,21480,21559,21641,21729,21818,21892,21969,22039,22118,22218,22302,22386,22478,22578,22652,22733,22835,22888,22973,23040,23133,23222,23284,23348,23411,23479,23592,23699,23803,23904,23964,24024,24429"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-es/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,317,373,456,524,576,725,807,891,1021,1113,1180,1258,1354,1399,1459,1519,1593,1644,1688,1730,1775,1821,1882,1943,2026,2094,2161,2203,2278,2341,2408,2486,2567,2631,2693,2765,2816", "endColumns": "45,62,55,82,67,51,148,81,83,129,91,66,77,95,44,59,59,73,50,43,41,44,45,60,60,82,67,66,41,74,62,66,77,80,63,61,71,50,60", "endOffsets": "253,316,372,455,523,575,724,806,890,1020,1112,1179,1257,1353,1398,1458,1518,1592,1643,1687,1729,1774,1820,1881,1942,2025,2093,2160,2202,2277,2340,2407,2485,2566,2630,2692,2764,2815,2876"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4703,4753,4820,4880,4967,5039,5166,5319,5405,5493,5627,5723,5794,5876,5976,6025,6089,6153,6231,6286,6641,6687,6736,6786,6851,6916,7003,7075,7146,7192,7271,7338,7409,7491,7576,7644,7710,7786,16378", "endColumns": "49,66,59,86,71,55,152,85,87,133,95,70,81,99,48,63,63,77,54,47,45,48,49,64,64,86,71,70,45,78,66,70,81,84,67,65,75,54,64", "endOffsets": "4748,4815,4875,4962,5034,5090,5314,5400,5488,5622,5718,5789,5871,5971,6020,6084,6148,6226,6281,6329,6682,6731,6781,6846,6911,6998,7070,7141,7187,7266,7333,7404,7486,7571,7639,7705,7781,7836,16438"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3971,4070,4172,4272,4370,4477,4583,24936", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4065,4167,4267,4365,4472,4578,4698,25032"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-es/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9440", "endColumns": "159", "endOffsets": "9595"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,24694", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,24772"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,279,380,483,590,716,828,929,1013,1193,1379,1570,1760,1950,2146,2334,2459,2560,2665,2762,2857,2952,3045,3164,3286,3370,3458,3543,3641,3757,3850,3957,4068,4155", "endColumns": "128,94,100,102,106,125,111,100,83,179,185,190,189,189,195,187,124,100,104,96,94,94,92,118,121,83,87,84,97,115,92,106,110,86,98", "endOffsets": "179,274,375,478,585,711,823,924,1008,1188,1374,1565,1755,1945,2141,2329,2454,2555,2660,2757,2852,2947,3040,3159,3281,3365,3453,3538,3636,3752,3845,3952,4063,4150,4249"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14941,16443,16538,16639,16742,16849,16975,17087,17188,17272,17452,17638,17829,18019,18209,18405,18593,18718,18819,18924,19021,19116,19211,19304,19423,19545,19629,19717,19802,19900,20016,20109,20216,20327,20414", "endColumns": "128,94,100,102,106,125,111,100,83,179,185,190,189,189,195,187,124,100,104,96,94,94,92,118,121,83,87,84,97,115,92,106,110,86,98", "endOffsets": "15065,16533,16634,16737,16844,16970,17082,17183,17267,17447,17633,17824,18014,18204,18400,18588,18713,18814,18919,19016,19111,19206,19299,19418,19540,19624,19712,19797,19895,20011,20104,20211,20322,20409,20508"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-es/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8404,8512,8675,8806,8914,9075,9208,9330,9600,9792,9901,10066,10198,10363,10520,10587,10656", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "8507,8670,8801,8909,9070,9203,9325,9435,9787,9896,10061,10193,10358,10515,10582,10651,10736"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,601,697,811,950,1064,1208,1289,1387,1480,1578,1685,1799,1902,2036,2165,2305,2478,2598,2714,2833,2960,3054,3146,3263,3394,3493,3603,3714,3846,3983,4090,4190,4273,4351,4434,4516,4623,4699,4779,4876,4978,5074,5169,5254,5361,5458,5557,5672,5748,5861", "endColumns": "116,114,118,116,77,95,113,138,113,143,80,97,92,97,106,113,102,133,128,139,172,119,115,118,126,93,91,116,130,98,109,110,131,136,106,99,82,77,82,81,106,75,79,96,101,95,94,84,106,96,98,114,75,112,104", "endOffsets": "167,282,401,518,596,692,806,945,1059,1203,1284,1382,1475,1573,1680,1794,1897,2031,2160,2300,2473,2593,2709,2828,2955,3049,3141,3258,3389,3488,3598,3709,3841,3978,4085,4185,4268,4346,4429,4511,4618,4694,4774,4871,4973,5069,5164,5249,5356,5453,5552,5667,5743,5856,5961"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3059,3176,3291,3410,8326,10741,10837,10951,11090,11204,11348,11429,11527,11620,11718,11825,11939,12042,12176,12305,12445,12618,12738,12854,12973,13100,13194,13286,13403,13534,13633,13743,13854,13986,14123,14230,14531,14768,24611,24854,25037,25417,25493,25573,25670,25772,25868,25963,26048,26155,26252,26351,26466,26542,26655", "endColumns": "116,114,118,116,77,95,113,138,113,143,80,97,92,97,106,113,102,133,128,139,172,119,115,118,126,93,91,116,130,98,109,110,131,136,106,99,82,77,82,81,106,75,79,96,101,95,94,84,106,96,98,114,75,112,104", "endOffsets": "3171,3286,3405,3522,8399,10832,10946,11085,11199,11343,11424,11522,11615,11713,11820,11934,12037,12171,12300,12440,12613,12733,12849,12968,13095,13189,13281,13398,13529,13628,13738,13849,13981,14118,14225,14325,14609,14841,24689,24931,25139,25488,25568,25665,25767,25863,25958,26043,26150,26247,26346,26461,26537,26650,26755"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-es/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,342,421,491", "endColumns": "66,76,78,69,64", "endOffsets": "264,341,420,490,555"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5095,6334,6415,6498,6572", "endColumns": "70,80,82,73,68", "endOffsets": "5161,6410,6493,6567,6636"}}]}]}