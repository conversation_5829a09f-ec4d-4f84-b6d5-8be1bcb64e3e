{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "43,44,45,46,47,48,49,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3998,4096,4198,4295,4399,4503,4608,24526", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4091,4193,4290,4394,4498,4603,4719,24622"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,481,582,688,774,878,1000,1085,1167,1258,1351,1446,1540,1640,1733,1828,1933,2024,2115,2201,2306,2412,2515,2622,2731,2838,3008,24282", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "476,577,683,769,873,995,1080,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2617,2726,2833,3003,3100,24364"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,401,512,593,683,791,923,1039,1179,1260,1356,1447,1541,1653,1774,1875,2012,2148,2277,2453,2574,2690,2812,2931,3023,3117,3231,3357,3453,3551,3656,3793,3938,4043,4141,4214,4294,4379,4463,4566,4642,4721,4814,4913,5002,5096,5179,5283,5376,5473,5602,5678,5781", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "161,272,396,507,588,678,786,918,1034,1174,1255,1351,1442,1536,1648,1769,1870,2007,2143,2272,2448,2569,2685,2807,2926,3018,3112,3226,3352,3448,3546,3651,3788,3933,4038,4136,4209,4289,4374,4458,4561,4637,4716,4809,4908,4997,5091,5174,5278,5371,5468,5597,5673,5776,5871"}, "to": {"startLines": "34,35,36,37,98,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,150,153,257,260,262,266,267,268,269,270,271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3105,3216,3327,3451,8408,10664,10754,10862,10994,11110,11250,11331,11427,11518,11612,11724,11845,11946,12083,12219,12348,12524,12645,12761,12883,13002,13094,13188,13302,13428,13524,13622,13727,13864,14009,14114,14410,14635,24197,24442,24627,24997,25073,25152,25245,25344,25433,25527,25610,25714,25807,25904,26033,26109,26212", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "3211,3322,3446,3557,8484,10749,10857,10989,11105,11245,11326,11422,11513,11607,11719,11840,11941,12078,12214,12343,12519,12640,12756,12878,12997,13089,13183,13297,13423,13519,13617,13722,13859,14004,14109,14207,14478,14710,24277,24521,24725,25068,25147,25240,25339,25428,25522,25605,25709,25802,25899,26028,26104,26207,26302"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-b+sr+Latn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,259,327,388,475,554,607,738,833,911,1022,1105,1174,1254,1352,1398,1469,1543,1617,1663,1711,1757,1797,1863,1932,2004,2080,2147,2215,2273,2340,2402,2467,2550,2632,2694,2756,2825,2874", "endColumns": "43,67,60,86,78,52,130,94,77,110,82,68,79,97,45,70,73,73,45,47,45,39,65,68,71,75,66,67,57,66,61,64,82,81,61,61,68,48,68", "endOffsets": "258,326,387,474,553,606,737,832,910,1021,1104,1173,1253,1351,1397,1468,1542,1616,1662,1710,1756,1796,1862,1931,2003,2079,2146,2214,2272,2339,2401,2466,2549,2631,2693,2755,2824,2873,2942"}, "to": {"startLines": "50,51,52,53,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4724,4772,4844,4909,5000,5083,5211,5346,5445,5527,5642,5729,5802,5886,5988,6038,6113,6191,6269,6319,6682,6732,6776,6846,6919,6995,7075,7146,7218,7280,7351,7417,7486,7573,7659,7725,7791,7864,16189", "endColumns": "47,71,64,90,82,56,134,98,81,114,86,72,83,101,49,74,77,77,49,51,49,43,69,72,75,79,70,71,61,70,65,68,86,85,65,65,72,52,72", "endOffsets": "4767,4839,4904,4995,5078,5135,5341,5440,5522,5637,5724,5797,5881,5983,6033,6108,6186,6264,6314,6366,6727,6771,6841,6914,6990,7070,7141,7213,7275,7346,7412,7481,7568,7654,7720,7786,7859,7912,16257"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "96,97,148,149,151,157,158,250,251,252,253,255,256,259,263,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8224,8321,14212,14309,14483,15003,15080,23632,23724,23809,23881,24032,24112,24369,24730,24809,24879", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "8316,8403,14304,14405,14564,15075,15166,23719,23804,23876,23947,24107,24192,24437,24804,24874,24992"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1133,1226,1294,1357,1460,1520,1586,1642,1713,1773,1827,1939,1996,2057,2111,2187,2312,2399,2482,2621,2703,2786,2917,3005,3083,3137,3193,3259,3333,3411,3500,3582,3658,3734,3809,3881,3988,4078,4151,4243,4339,4411,4487,4583,4636,4718,4785,4872,4959,5021,5085,5148,5217,5322,5432,5528,5636,5694,5754", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "319,396,473,553,661,755,849,981,1062,1128,1221,1289,1352,1455,1515,1581,1637,1708,1768,1822,1934,1991,2052,2106,2182,2307,2394,2477,2616,2698,2781,2912,3000,3078,3132,3188,3254,3328,3406,3495,3577,3653,3729,3804,3876,3983,4073,4146,4238,4334,4406,4482,4578,4631,4713,4780,4867,4954,5016,5080,5143,5212,5317,5427,5523,5631,5689,5749,5829"}, "to": {"startLines": "2,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3562,3639,3716,3796,3904,7917,8011,8143,14569,14715,14935,15171,15234,15337,15397,15463,15519,15590,15650,15704,15816,15873,15934,15988,16064,20190,20277,20360,20499,20581,20664,20795,20883,20961,21015,21071,21137,21211,21289,21378,21460,21536,21612,21687,21759,21866,21956,22029,22121,22217,22289,22365,22461,22514,22596,22663,22750,22837,22899,22963,23026,23095,23200,23310,23406,23514,23572,23952", "endLines": "6,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "369,3634,3711,3791,3899,3993,8006,8138,8219,14630,14803,14998,15229,15332,15392,15458,15514,15585,15645,15699,15811,15868,15929,15983,16059,16184,20272,20355,20494,20576,20659,20790,20878,20956,21010,21066,21132,21206,21284,21373,21455,21531,21607,21682,21754,21861,21951,22024,22116,22212,22284,22360,22456,22509,22591,22658,22745,22832,22894,22958,23021,23090,23195,23305,23401,23509,23567,23627,24027"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-b+sr+Latn/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "205,272,352,433,501", "endColumns": "66,79,80,67,65", "endOffsets": "271,351,432,500,566"}, "to": {"startLines": "56,71,72,73,74", "startColumns": "4,4,4,4,4", "startOffsets": "5140,6371,6455,6540,6612", "endColumns": "70,83,84,71,69", "endOffsets": "5206,6450,6535,6607,6677"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-b+sr+Latn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "99,100,101,102,103,104,105,106,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8489,8596,8754,8881,8991,9145,9272,9384,9616,9765,9872,10032,10159,10308,10451,10519,10584", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "8591,8749,8876,8986,9140,9267,9379,9481,9760,9867,10027,10154,10303,10446,10514,10579,10659"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-b+sr+Latn/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "9486", "endColumns": "129", "endOffsets": "9611"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,280,385,492,607,730,837,933,1021,1199,1373,1547,1721,1887,2062,2232,2354,2448,2554,2652,2745,2841,2934,3050,3162,3248,3331,3417,3523,3632,3725,3825,3930,4016", "endColumns": "126,97,104,106,114,122,106,95,87,177,173,173,173,165,174,169,121,93,105,97,92,95,92,115,111,85,82,85,105,108,92,99,104,85,93", "endOffsets": "177,275,380,487,602,725,832,928,1016,1194,1368,1542,1716,1882,2057,2227,2349,2443,2549,2647,2740,2836,2929,3045,3157,3243,3326,3412,3518,3627,3720,3820,3925,4011,4105"}, "to": {"startLines": "155,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14808,16262,16360,16465,16572,16687,16810,16917,17013,17101,17279,17453,17627,17801,17967,18142,18312,18434,18528,18634,18732,18825,18921,19014,19130,19242,19328,19411,19497,19603,19712,19805,19905,20010,20096", "endColumns": "126,97,104,106,114,122,106,95,87,177,173,173,173,165,174,169,121,93,105,97,92,95,92,115,111,85,82,85,105,108,92,99,104,85,93", "endOffsets": "14930,16355,16460,16567,16682,16805,16912,17008,17096,17274,17448,17622,17796,17962,18137,18307,18429,18523,18629,18727,18820,18916,19009,19125,19237,19323,19406,19492,19598,19707,19800,19900,20005,20091,20185"}}]}]}