{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1070,1166,1232,1293,1398,1462,1534,1592,1666,1728,1782,1895,1955,2016,2075,2153,2277,2358,2443,2579,2660,2743,2874,2957,3043,3105,3159,3225,3302,3381,3469,3552,3621,3697,3778,3846,3950,4041,4119,4212,4309,4383,4462,4560,4620,4708,4774,4862,4950,5012,5080,5143,5209,5314,5420,5515,5620,5686,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "257,346,434,516,611,700,802,912,999,1065,1161,1227,1288,1393,1457,1529,1587,1661,1723,1777,1890,1950,2011,2070,2148,2272,2353,2438,2574,2655,2738,2869,2952,3038,3100,3154,3220,3297,3376,3464,3547,3616,3692,3773,3841,3945,4036,4114,4207,4304,4378,4457,4555,4615,4703,4769,4857,4945,5007,5075,5138,5204,5309,5415,5510,5615,5681,5739,5823"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3548,3637,3725,3807,3902,7901,8003,8113,14827,14977,15200,15444,15505,15610,15674,15746,15804,15878,15940,15994,16107,16167,16228,16287,16365,20910,20991,21076,21212,21293,21376,21507,21590,21676,21738,21792,21858,21935,22014,22102,22185,22254,22330,22411,22479,22583,22674,22752,22845,22942,23016,23095,23193,23253,23341,23407,23495,23583,23645,23713,23776,23842,23947,24053,24148,24253,24319,24682", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "307,3632,3720,3802,3897,3986,7998,8108,8195,14888,15068,15261,15500,15605,15669,15741,15799,15873,15935,15989,16102,16162,16223,16282,16360,16484,20986,21071,21207,21288,21371,21502,21585,21671,21733,21787,21853,21930,22009,22097,22180,22249,22325,22406,22474,22578,22669,22747,22840,22937,23011,23090,23188,23248,23336,23402,23490,23578,23640,23708,23771,23837,23942,24048,24143,24248,24314,24372,24761"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,286,399,516,636,760,870,975,1070,1248,1467,1688,1916,2135,2361,2590,2710,2803,2912,3017,3124,3225,3328,3445,3561,3652,3740,3828,3931,4035,4127,4229,4344,4431", "endColumns": "126,103,112,116,119,123,109,104,94,177,218,220,227,218,225,228,119,92,108,104,106,100,102,116,115,90,87,87,102,103,91,101,114,86,95", "endOffsets": "177,281,394,511,631,755,865,970,1065,1243,1462,1683,1911,2130,2356,2585,2705,2798,2907,3012,3119,3220,3323,3440,3556,3647,3735,3823,3926,4030,4122,4224,4339,4426,4522"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15073,16565,16669,16782,16899,17019,17143,17253,17358,17453,17631,17850,18071,18299,18518,18744,18973,19093,19186,19295,19400,19507,19608,19711,19828,19944,20035,20123,20211,20314,20418,20510,20612,20727,20814", "endColumns": "126,103,112,116,119,123,109,104,94,177,218,220,227,218,225,228,119,92,108,104,106,100,102,116,115,90,87,87,102,103,91,101,114,86,95", "endOffsets": "15195,16664,16777,16894,17014,17138,17248,17353,17448,17626,17845,18066,18294,18513,18739,18968,19088,19181,19290,19395,19502,19603,19706,19823,19939,20030,20118,20206,20309,20413,20505,20607,20722,20809,20905"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,25036", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,25111"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ne/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,321,378,460,529,587,724,805,882,993,1077,1146,1229,1333,1389,1459,1530,1604,1649,1699,1747,1796,1851,1920,1990,2061,2123,2187,2232,2305,2368,2441,2520,2603,2672,2732,2805,2859", "endColumns": "46,65,56,81,68,57,136,80,76,110,83,68,82,103,55,69,70,73,44,49,47,48,54,68,69,70,61,63,44,72,62,72,78,82,68,59,72,53,71", "endOffsets": "254,320,377,459,528,586,723,804,881,992,1076,1145,1228,1332,1388,1458,1529,1603,1648,1698,1746,1795,1850,1919,1989,2060,2122,2186,2231,2304,2367,2440,2519,2602,2671,2731,2804,2858,2930"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4711,4762,4832,4893,4979,5052,5185,5326,5411,5492,5607,5695,5768,5855,5963,6023,6097,6172,6250,6299,6669,6721,6774,6833,6906,6980,7055,7121,7189,7238,7315,7382,7459,7542,7629,7702,7766,7843,16489", "endColumns": "50,69,60,85,72,61,140,84,80,114,87,72,86,107,59,73,74,77,48,53,51,52,58,72,73,74,65,67,48,76,66,76,82,86,72,63,76,57,75", "endOffsets": "4757,4827,4888,4974,5047,5109,5321,5406,5487,5602,5690,5763,5850,5958,6018,6092,6167,6245,6294,6348,6716,6769,6828,6901,6975,7050,7116,7184,7233,7310,7377,7454,7537,7624,7697,7761,7838,7896,16560"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8200,8299,14479,14573,14741,15266,15348,24377,24464,24550,24616,24766,24856,25116,25483,25564,25632", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "8294,8384,14568,14665,14822,15343,15439,24459,24545,24611,24677,24851,24944,25188,25559,25627,25747"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ne/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,304,458,588,701,868,1000,1106,1207,1383,1493,1653,1782,1926,2074,2136,2204", "endColumns": "110,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "303,457,587,700,867,999,1105,1206,1382,1492,1652,1781,1925,2073,2135,2203,2291"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8473,8588,8746,8880,8997,9168,9304,9414,9683,9863,9977,10141,10274,10422,10574,10640,10712", "endColumns": "114,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "8583,8741,8875,8992,9163,9299,9409,9514,9858,9972,10136,10269,10417,10569,10635,10707,10799"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3991,4094,4197,4299,4405,4503,4603,25279", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "4089,4192,4294,4400,4498,4598,4706,25375"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ne/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,346,426,494", "endColumns": "66,80,79,67,70", "endOffsets": "264,345,425,493,564"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5114,6353,6438,6522,6594", "endColumns": "70,84,83,71,74", "endOffsets": "5180,6433,6517,6589,6664"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ne/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9519", "endColumns": "163", "endOffsets": "9678"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,295,407,531,615,706,815,955,1072,1221,1301,1405,1499,1594,1702,1822,1931,2073,2212,2342,2503,2629,2777,2931,3057,3152,3243,3355,3475,3575,3691,3796,3937,4081,4187,4290,4361,4445,4532,4618,4721,4797,4878,4975,5080,5171,5270,5353,5460,5555,5655,5786,5862,5962", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "175,290,402,526,610,701,810,950,1067,1216,1296,1400,1494,1589,1697,1817,1926,2068,2207,2337,2498,2624,2772,2926,3052,3147,3238,3350,3470,3570,3686,3791,3932,4076,4182,4285,4356,4440,4527,4613,4716,4792,4873,4970,5075,5166,5265,5348,5455,5550,5650,5781,5857,5957,6047"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3072,3197,3312,3424,8389,10804,10895,11004,11144,11261,11410,11490,11594,11688,11783,11891,12011,12120,12262,12401,12531,12692,12818,12966,13120,13246,13341,13432,13544,13664,13764,13880,13985,14126,14270,14376,14670,14893,24949,25193,25380,25752,25828,25909,26006,26111,26202,26301,26384,26491,26586,26686,26817,26893,26993", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "3192,3307,3419,3543,8468,10890,10999,11139,11256,11405,11485,11589,11683,11778,11886,12006,12115,12257,12396,12526,12687,12813,12961,13115,13241,13336,13427,13539,13659,13759,13875,13980,14121,14265,14371,14474,14736,14972,25031,25274,25478,25823,25904,26001,26106,26197,26296,26379,26486,26581,26681,26812,26888,26988,27078"}}]}]}