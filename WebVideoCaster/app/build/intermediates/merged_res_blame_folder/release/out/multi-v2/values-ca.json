{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-ca/values-ca.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,24826", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,24903"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ca/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9425", "endColumns": "134", "endOffsets": "9555"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,196,289,389,499,605,732,853,961,1053,1232,1426,1624,1824,2021,2224,2423,2551,2652,2767,2863,2958,3053,3147,3267,3399,3490,3578,3661,3762,3874,3966,4073,4183,4270", "endColumns": "140,92,99,109,105,126,120,107,91,178,193,197,199,196,202,198,127,100,114,95,94,94,93,119,131,90,87,82,100,111,91,106,109,86,97", "endOffsets": "191,284,384,494,600,727,848,956,1048,1227,1421,1619,1819,2016,2219,2418,2546,2647,2762,2858,2953,3048,3142,3262,3394,3485,3573,3656,3757,3869,3961,4068,4178,4265,4363"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14911,16434,16527,16627,16737,16843,16970,17091,17199,17291,17470,17664,17862,18062,18259,18462,18661,18789,18890,19005,19101,19196,19291,19385,19505,19637,19728,19816,19899,20000,20112,20204,20311,20421,20508", "endColumns": "140,92,99,109,105,126,120,107,91,178,193,197,199,196,202,198,127,100,114,95,94,94,93,119,131,90,87,82,100,111,91,106,109,86,97", "endOffsets": "15047,16522,16622,16732,16838,16965,17086,17194,17286,17465,17659,17857,18057,18254,18457,18656,18784,18885,19000,19096,19191,19286,19380,19500,19632,19723,19811,19894,19995,20107,20199,20306,20416,20503,20601"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ca/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,323,378,470,546,598,749,829,914,1048,1142,1218,1296,1396,1440,1497,1554,1628,1679,1722,1771,1816,1861,1919,1977,2041,2105,2170,2210,2288,2351,2419,2496,2577,2637,2700,2771,2826", "endColumns": "44,69,54,91,75,51,150,79,84,133,93,75,77,99,43,56,56,73,50,42,48,44,44,57,57,63,63,64,39,77,62,67,76,80,59,62,70,54,60", "endOffsets": "252,322,377,469,545,597,748,828,913,1047,1141,1217,1295,1395,1439,1496,1553,1627,1678,1721,1770,1815,1860,1918,1976,2040,2104,2169,2209,2287,2350,2418,2495,2576,2636,2699,2770,2825,2886"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4687,4736,4810,4869,4965,5045,5172,5327,5411,5500,5638,5736,5816,5898,6002,6050,6111,6172,6250,6305,6671,6724,6773,6822,6884,6946,7014,7082,7151,7195,7277,7344,7416,7497,7582,7646,7713,7788,16369", "endColumns": "48,73,58,95,79,55,154,83,88,137,97,79,81,103,47,60,60,77,54,46,52,48,48,61,61,67,67,68,43,81,66,71,80,84,63,66,74,58,64", "endOffsets": "4731,4805,4864,4960,5040,5096,5322,5406,5495,5633,5731,5811,5893,5997,6045,6106,6167,6245,6300,6347,6719,6768,6817,6879,6941,7009,7077,7146,7190,7272,7339,7411,7492,7577,7641,7708,7783,7842,16429"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,402,518,601,695,807,945,1056,1211,1291,1385,1479,1576,1689,1814,1913,2050,2183,2330,2492,2621,2734,2852,2978,3073,3166,3283,3424,3526,3635,3745,3879,4020,4125,4227,4299,4382,4464,4546,4653,4729,4809,4906,5010,5105,5205,5288,5397,5493,5595,5710,5786,5899", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "167,282,397,513,596,690,802,940,1051,1206,1286,1380,1474,1571,1684,1809,1908,2045,2178,2325,2487,2616,2729,2847,2973,3068,3161,3278,3419,3521,3630,3740,3874,4015,4120,4222,4294,4377,4459,4541,4648,4724,4804,4901,5005,5100,5200,5283,5392,5488,5590,5705,5781,5894,5997"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3049,3166,3281,3396,8341,10672,10766,10878,11016,11127,11282,11362,11456,11550,11647,11760,11885,11984,12121,12254,12401,12563,12692,12805,12923,13049,13144,13237,13354,13495,13597,13706,13816,13950,14091,14196,14505,14731,24744,24984,25167,25547,25623,25703,25800,25904,25999,26099,26182,26291,26387,26489,26604,26680,26793", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "3161,3276,3391,3507,8419,10761,10873,11011,11122,11277,11357,11451,11545,11642,11755,11880,11979,12116,12249,12396,12558,12687,12800,12918,13044,13139,13232,13349,13490,13592,13701,13811,13945,14086,14191,14293,14572,14809,24821,25061,25269,25618,25698,25795,25899,25994,26094,26177,26286,26382,26484,26599,26675,26788,26891"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ca/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8424,8529,8681,8808,8917,9067,9194,9317,9560,9731,9840,9999,10130,10294,10452,10517,10585", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "8524,8676,8803,8912,9062,9189,9312,9420,9726,9835,9994,10125,10289,10447,10512,10580,10667"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ca/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,348,435,503", "endColumns": "66,82,86,67,64", "endOffsets": "264,347,434,502,567"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5101,6352,6439,6530,6602", "endColumns": "70,86,90,71,68", "endOffsets": "5167,6434,6525,6597,6666"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8160,8257,14298,14402,14577,15132,15210,24163,24254,24340,24412,24567,24653,24908,25274,25356,25427", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "8252,8336,14397,14500,14661,15205,15296,24249,24335,24407,24476,24648,24739,24979,25351,25422,25542"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3956,4052,4154,4253,4350,4456,4561,25066", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "4047,4149,4248,4345,4451,4556,4682,25162"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ca/values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1096,1193,1273,1338,1433,1497,1569,1631,1707,1770,1827,1948,2006,2067,2124,2204,2341,2428,2512,2651,2729,2808,2960,3049,3125,3182,3238,3304,3382,3463,3551,3639,3717,3794,3868,3947,4057,4147,4239,4331,4432,4506,4588,4689,4739,4822,4888,4980,5067,5129,5193,5256,5329,5452,5565,5669,5777,5838,5898", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "269,350,430,518,621,713,814,942,1026,1091,1188,1268,1333,1428,1492,1564,1626,1702,1765,1822,1943,2001,2062,2119,2199,2336,2423,2507,2646,2724,2803,2955,3044,3120,3177,3233,3299,3377,3458,3546,3634,3712,3789,3863,3942,4052,4142,4234,4326,4427,4501,4583,4684,4734,4817,4883,4975,5062,5124,5188,5251,5324,5447,5560,5664,5772,5833,5893,5979"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3512,3593,3673,3761,3864,7847,7948,8076,14666,14814,15052,15301,15366,15461,15525,15597,15659,15735,15798,15855,15976,16034,16095,16152,16232,20606,20693,20777,20916,20994,21073,21225,21314,21390,21447,21503,21569,21647,21728,21816,21904,21982,22059,22133,22212,22322,22412,22504,22596,22697,22771,22853,22954,23004,23087,23153,23245,23332,23394,23458,23521,23594,23717,23830,23934,24042,24103,24481", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "319,3588,3668,3756,3859,3951,7943,8071,8155,14726,14906,15127,15361,15456,15520,15592,15654,15730,15793,15850,15971,16029,16090,16147,16227,16364,20688,20772,20911,20989,21068,21220,21309,21385,21442,21498,21564,21642,21723,21811,21899,21977,22054,22128,22207,22317,22407,22499,22591,22692,22766,22848,22949,22999,23082,23148,23240,23327,23389,23453,23516,23589,23712,23825,23929,24037,24098,24158,24562"}}]}]}