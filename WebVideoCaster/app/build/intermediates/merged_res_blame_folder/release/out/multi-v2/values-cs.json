{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-cs/values-cs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2939,3027,3107,3161,3212,3278,3350,3427,3511,3592,3664,3741,3815,3886,3991,4079,4150,4243,4338,4412,4486,4582,4634,4717,4784,4870,4958,5020,5084,5147,5215,5325,5431,5530,5644,5702,5757", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2934,3022,3102,3156,3207,3273,3345,3422,3506,3587,3659,3736,3810,3881,3986,4074,4145,4238,4333,4407,4481,4577,4629,4712,4779,4865,4953,5015,5079,5142,5210,5320,5426,5525,5639,5697,5752,5831"}, "to": {"startLines": "2,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3568,3646,3724,3801,3904,7863,7955,8081,14487,14629,14850,15096,15157,15246,15310,15377,15431,15499,15559,15613,15730,15790,15852,15906,15978,20075,20159,20251,20388,20466,20548,20675,20763,20843,20897,20948,21014,21086,21163,21247,21328,21400,21477,21551,21622,21727,21815,21886,21979,22074,22148,22222,22318,22370,22453,22520,22606,22694,22756,22820,22883,22951,23061,23167,23266,23380,23438,23803", "endLines": "7,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "413,3641,3719,3796,3899,3991,7950,8076,8157,14547,14723,14921,15152,15241,15305,15372,15426,15494,15554,15608,15725,15785,15847,15901,15973,16095,20154,20246,20383,20461,20543,20670,20758,20838,20892,20943,21009,21081,21158,21242,21323,21395,21472,21546,21617,21722,21810,21881,21974,22069,22143,22217,22313,22365,22448,22515,22601,22689,22751,22815,22878,22946,23056,23162,23261,23375,23433,23488,23877"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-cs/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,352,442,510", "endColumns": "66,86,89,67,60", "endOffsets": "264,351,441,509,570"}, "to": {"startLines": "57,72,73,74,75", "startColumns": "4,4,4,4,4", "startOffsets": "5148,6375,6466,6560,6632", "endColumns": "70,90,93,71,64", "endOffsets": "5214,6461,6555,6627,6692"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,24133", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,24211"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,980,1049,1131,1217,1289,1368,1436", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,975,1044,1126,1212,1284,1363,1431,1551"}, "to": {"startLines": "97,98,149,150,152,158,159,251,252,253,254,256,257,260,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8162,8255,14119,14213,14395,14926,15004,23493,23584,23665,23734,23882,23964,24216,24565,24644,24712", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "8250,8333,14208,14310,14482,14999,15091,23579,23660,23729,23798,23959,24045,24283,24639,24707,24827"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,273,375,480,584,715,822,918,1013,1193,1352,1517,1682,1844,2011,2175,2302,2394,2508,2605,2699,2790,2884,3008,3131,3219,3304,3390,3493,3599,3694,3796,3906,3992", "endColumns": "121,95,101,104,103,130,106,95,94,179,158,164,164,161,166,163,126,91,113,96,93,90,93,123,122,87,84,85,102,105,94,101,109,85,94", "endOffsets": "172,268,370,475,579,710,817,913,1008,1188,1347,1512,1677,1839,2006,2170,2297,2389,2503,2600,2694,2785,2879,3003,3126,3214,3299,3385,3488,3594,3689,3791,3901,3987,4082"}, "to": {"startLines": "156,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14728,16165,16261,16363,16468,16572,16703,16810,16906,17001,17181,17340,17505,17670,17832,17999,18163,18290,18382,18496,18593,18687,18778,18872,18996,19119,19207,19292,19378,19481,19587,19682,19784,19894,19980", "endColumns": "121,95,101,104,103,130,106,95,94,179,158,164,164,161,166,163,126,91,113,96,93,90,93,123,122,87,84,85,102,105,94,101,109,85,94", "endOffsets": "14845,16256,16358,16463,16567,16698,16805,16901,16996,17176,17335,17500,17665,17827,17994,18158,18285,18377,18491,18588,18682,18773,18867,18991,19114,19202,19287,19373,19476,19582,19677,19779,19889,19975,20070"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-cs/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,328,388,479,557,605,758,841,917,1030,1114,1181,1262,1358,1410,1477,1544,1618,1663,1705,1751,1793,1843,1908,1973,2031,2094,2161,2204,2272,2334,2399,2481,2562,2624,2681,2750,2799", "endColumns": "45,73,59,90,77,47,152,82,75,112,83,66,80,95,51,66,66,73,44,41,45,41,49,64,64,57,62,66,42,67,61,64,81,80,61,56,68,48,60", "endOffsets": "253,327,387,478,556,604,757,840,916,1029,1113,1180,1261,1357,1409,1476,1543,1617,1662,1704,1750,1792,1842,1907,1972,2030,2093,2160,2203,2271,2333,2398,2480,2561,2623,2680,2749,2798,2859"}, "to": {"startLines": "51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4727,4777,4855,4919,5014,5096,5219,5376,5463,5543,5660,5748,5819,5904,6004,6060,6131,6202,6280,6329,6697,6747,6793,6847,6916,6985,7047,7114,7185,7232,7304,7370,7439,7525,7610,7676,7737,7810,16100", "endColumns": "49,77,63,94,81,51,156,86,79,116,87,70,84,99,55,70,70,77,48,45,49,45,53,68,68,61,66,70,46,71,65,68,85,84,65,60,72,52,64", "endOffsets": "4772,4850,4914,5009,5091,5143,5371,5458,5538,5655,5743,5814,5899,5999,6055,6126,6197,6275,6324,6370,6742,6788,6842,6911,6980,7042,7109,7180,7227,7299,7365,7434,7520,7605,7671,7732,7805,7858,16160"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-cs/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8414,8520,8680,8805,8915,9068,9195,9307,9551,9726,9837,10001,10129,10290,10445,10513,10580", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "8515,8675,8800,8910,9063,9190,9302,9403,9721,9832,9996,10124,10285,10440,10508,10575,10661"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-cs/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "9408", "endColumns": "142", "endOffsets": "9546"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,397,511,587,678,787,917,1029,1160,1241,1337,1425,1518,1630,1748,1849,1978,2104,2238,2397,2521,2634,2755,2873,2962,3055,3168,3279,3373,3472,3576,3703,3840,3946,4040,4120,4197,4280,4362,4456,4532,4614,4711,4810,4903,5000,5084,5186,5281,5379,5494,5570,5670", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "164,277,392,506,582,673,782,912,1024,1155,1236,1332,1420,1513,1625,1743,1844,1973,2099,2233,2392,2516,2629,2750,2868,2957,3050,3163,3274,3368,3467,3571,3698,3835,3941,4035,4115,4192,4275,4357,4451,4527,4609,4706,4805,4898,4995,5079,5181,5276,5374,5489,5565,5665,5756"}, "to": {"startLines": "35,36,37,38,99,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151,154,258,261,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3112,3226,3339,3454,8338,10666,10757,10866,10996,11108,11239,11320,11416,11504,11597,11709,11827,11928,12057,12183,12317,12476,12600,12713,12834,12952,13041,13134,13247,13358,13452,13551,13655,13782,13919,14025,14315,14552,24050,24288,24471,24832,24908,24990,25087,25186,25279,25376,25460,25562,25657,25755,25870,25946,26046", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "3221,3334,3449,3563,8409,10752,10861,10991,11103,11234,11315,11411,11499,11592,11704,11822,11923,12052,12178,12312,12471,12595,12708,12829,12947,13036,13129,13242,13353,13447,13546,13650,13777,13914,14020,14114,14390,14624,24128,24365,24560,24903,24985,25082,25181,25274,25371,25455,25557,25652,25750,25865,25941,26041,26132"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "44,45,46,47,48,49,50,262", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3996,4094,4196,4297,4396,4501,4608,24370", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4089,4191,4292,4391,4496,4603,4722,24466"}}]}]}