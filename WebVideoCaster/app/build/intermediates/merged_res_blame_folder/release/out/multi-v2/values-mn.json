{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-mn/values-mn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-mn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8252,8362,8519,8651,8758,8895,9021,9150,9410,9554,9661,9829,9958,10099,10267,10328,10390", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "8357,8514,8646,8753,8890,9016,9145,9255,9549,9656,9824,9953,10094,10262,10323,10385,10462"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-mn/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9260", "endColumns": "149", "endOffsets": "9405"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,281,387,499,606,716,833,939,1033,1218,1398,1579,1763,1940,2128,2308,2436,2532,2644,2739,2834,2924,3016,3119,3238,3327,3414,3499,3603,3714,3807,3908,4013,4099", "endColumns": "127,97,105,111,106,109,116,105,93,184,179,180,183,176,187,179,127,95,111,94,94,89,91,102,118,88,86,84,103,110,92,100,104,85,98", "endOffsets": "178,276,382,494,601,711,828,934,1028,1213,1393,1574,1758,1935,2123,2303,2431,2527,2639,2734,2829,2919,3011,3114,3233,3322,3409,3494,3598,3709,3802,3903,4008,4094,4193"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14610,16083,16181,16287,16399,16506,16616,16733,16839,16933,17118,17298,17479,17663,17840,18028,18208,18336,18432,18544,18639,18734,18824,18916,19019,19138,19227,19314,19399,19503,19614,19707,19808,19913,19999", "endColumns": "127,97,105,111,106,109,116,105,93,184,179,180,183,176,187,179,127,95,111,94,94,89,91,102,118,88,86,84,103,110,92,100,104,85,98", "endOffsets": "14733,16176,16282,16394,16501,16611,16728,16834,16928,17113,17293,17474,17658,17835,18023,18203,18331,18427,18539,18634,18729,18819,18911,19014,19133,19222,19309,19394,19498,19609,19702,19803,19908,19994,20093"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2829,2919,2999,3053,3104,3170,3241,3319,3405,3484,3559,3637,3717,3800,3905,3993,4072,4162,4255,4329,4399,4490,4544,4624,4691,4775,4860,4922,4986,5049,5120,5224,5339,5436,5550,5608,5663", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2824,2914,2994,3048,3099,3165,3236,3314,3400,3479,3554,3632,3712,3795,3900,3988,4067,4157,4250,4324,4394,4485,4539,4619,4686,4770,4855,4917,4981,5044,5115,5219,5334,5431,5545,5603,5658,5742"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3455,3534,3611,3690,3777,7695,7793,7912,14371,14512,14738,14999,15058,15151,15213,15276,15334,15405,15467,15521,15642,15699,15760,15814,15885,20098,20182,20265,20398,20480,20558,20690,20780,20860,20914,20965,21031,21102,21180,21266,21345,21420,21498,21578,21661,21766,21854,21933,22023,22116,22190,22260,22351,22405,22485,22552,22636,22721,22783,22847,22910,22981,23085,23200,23297,23411,23469,23836", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "310,3529,3606,3685,3772,3857,7788,7907,7992,14431,14605,14814,15053,15146,15208,15271,15329,15400,15462,15516,15637,15694,15755,15809,15880,16013,20177,20260,20393,20475,20553,20685,20775,20855,20909,20960,21026,21097,21175,21261,21340,21415,21493,21573,21656,21761,21849,21928,22018,22111,22185,22255,22346,22400,22480,22547,22631,22716,22778,22842,22905,22976,23080,23195,23292,23406,23464,23519,23915"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-mn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,250,319,384,472,545,592,722,811,891,1008,1095,1168,1245,1342,1389,1454,1519,1593,1638,1682,1729,1773,1816,1875,1934,2002,2065,2124,2166,2238,2299,2364,2448,2533,2597,2654,2722,2768", "endColumns": "41,68,64,87,72,46,129,88,79,116,86,72,76,96,46,64,64,73,44,43,46,43,42,58,58,67,62,58,41,71,60,64,83,84,63,56,67,45,60", "endOffsets": "249,318,383,471,544,591,721,810,890,1007,1094,1167,1244,1341,1388,1453,1518,1592,1637,1681,1728,1772,1815,1874,1933,2001,2064,2123,2165,2237,2298,2363,2447,2532,2596,2653,2721,2767,2828"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4597,4643,4716,4785,4877,4954,5076,5210,5303,5387,5508,5599,5676,5757,5858,5909,5978,6047,6125,6174,6537,6588,6636,6683,6746,6809,6881,6948,7011,7057,7133,7198,7267,7355,7444,7512,7573,7645,16018", "endColumns": "45,72,68,91,76,50,133,92,83,120,90,76,80,100,50,68,68,77,48,47,50,47,46,62,62,71,66,62,45,75,64,68,87,88,67,60,71,49,64", "endOffsets": "4638,4711,4780,4872,4949,5000,5205,5298,5382,5503,5594,5671,5752,5853,5904,5973,6042,6120,6169,6217,6583,6631,6678,6741,6804,6876,6943,7006,7052,7128,7193,7262,7350,7439,7507,7568,7640,7690,16078"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,24167", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,24243"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,975,1046,1129,1212,1285,1362,1428", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,970,1041,1124,1207,1280,1357,1423,1540"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7997,8089,14021,14113,14288,14819,14905,23524,23611,23692,23765,23920,24003,24248,24606,24683,24749", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "8084,8170,14108,14204,14366,14900,14994,23606,23687,23760,23831,23998,24081,24316,24678,24744,24861"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,390,503,580,673,784,922,1036,1171,1252,1350,1438,1532,1646,1764,1867,2005,2145,2273,2445,2567,2684,2801,2918,3007,3103,3222,3356,3451,3555,3657,3796,3937,4040,4134,4213,4289,4370,4457,4554,4630,4709,4804,4900,4991,5089,5172,5276,5371,5471,5598,5674,5774", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "165,277,385,498,575,668,779,917,1031,1166,1247,1345,1433,1527,1641,1759,1862,2000,2140,2268,2440,2562,2679,2796,2913,3002,3098,3217,3351,3446,3550,3652,3791,3932,4035,4129,4208,4284,4365,4452,4549,4625,4704,4799,4895,4986,5084,5167,5271,5366,5466,5593,5669,5769,5860"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,3122,3234,3342,8175,10467,10560,10671,10809,10923,11058,11139,11237,11325,11419,11533,11651,11754,11892,12032,12160,12332,12454,12571,12688,12805,12894,12990,13109,13243,13338,13442,13544,13683,13824,13927,14209,14436,24086,24321,24509,24866,24942,25021,25116,25212,25303,25401,25484,25588,25683,25783,25910,25986,26086", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "3117,3229,3337,3450,8247,10555,10666,10804,10918,11053,11134,11232,11320,11414,11528,11646,11749,11887,12027,12155,12327,12449,12566,12683,12800,12889,12985,13104,13238,13333,13437,13539,13678,13819,13922,14016,14283,14507,24162,24403,24601,24937,25016,25111,25207,25298,25396,25479,25583,25678,25778,25905,25981,26081,26172"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3862,3960,4062,4163,4261,4366,4478,24408", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3955,4057,4158,4256,4361,4473,4592,24504"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-mn/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,345,431,504", "endColumns": "66,79,85,72,59", "endOffsets": "264,344,430,503,563"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5005,6222,6306,6396,6473", "endColumns": "70,83,89,76,63", "endOffsets": "5071,6301,6391,6468,6532"}}]}]}