{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-lo/values-lo.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3812,3908,4011,4110,4208,4309,4407,23806", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3903,4006,4105,4203,4304,4402,4513,23902"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1047,1136,1205,1264,1359,1425,1490,1548,1613,1674,1734,1840,1901,1961,2019,2090,2209,2295,2377,2520,2595,2671,2802,2892,2970,3025,3080,3146,3215,3289,3368,3447,3520,3597,3666,3736,3833,3918,3993,4086,4179,4253,4322,4416,4468,4551,4618,4702,4786,4848,4912,4975,5045,5144,5242,5337,5431,5490,5549", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "264,338,409,490,576,659,774,893,976,1042,1131,1200,1259,1354,1420,1485,1543,1608,1669,1729,1835,1896,1956,2014,2085,2204,2290,2372,2515,2590,2666,2797,2887,2965,3020,3075,3141,3210,3284,3363,3442,3515,3592,3661,3731,3828,3913,3988,4081,4174,4248,4317,4411,4463,4546,4613,4697,4781,4843,4907,4970,5040,5139,5237,5332,5426,5485,5544,5623"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3417,3491,3562,3643,3729,7534,7649,7768,14099,14243,14450,14698,14757,14852,14918,14983,15041,15106,15167,15227,15333,15394,15454,15512,15583,19603,19689,19771,19914,19989,20065,20196,20286,20364,20419,20474,20540,20609,20683,20762,20841,20914,20991,21060,21130,21227,21312,21387,21480,21573,21647,21716,21810,21862,21945,22012,22096,22180,22242,22306,22369,22439,22538,22636,22731,22825,22884,23243", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "314,3486,3557,3638,3724,3807,7644,7763,7846,14160,14327,14514,14752,14847,14913,14978,15036,15101,15162,15222,15328,15389,15449,15507,15578,15697,19684,19766,19909,19984,20060,20191,20281,20359,20414,20469,20535,20604,20678,20757,20836,20909,20986,21055,21125,21222,21307,21382,21475,21568,21642,21711,21805,21857,21940,22007,22091,22175,22237,22301,22364,22434,22533,22631,22726,22820,22879,22938,23317"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-lo/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8095,8203,8373,8503,8610,8762,8887,8995,9243,9395,9500,9666,9795,9966,10127,10195,10264", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "8198,8368,8498,8605,8757,8882,8990,9106,9390,9495,9661,9790,9961,10122,10190,10259,10344"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-lo/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,347,430,507", "endColumns": "66,81,82,76,68", "endOffsets": "264,346,429,506,575"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4922,6083,6169,6256,6337", "endColumns": "70,85,86,80,72", "endOffsets": "4988,6164,6251,6332,6405"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7851,7941,13727,13836,14010,14519,14608,22943,23029,23112,23177,23322,23402,23650,24007,24085,24151", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "7936,8013,13831,13929,14094,14603,14693,23024,23107,23172,23238,23397,23481,23719,24080,24146,24267"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,272,383,496,616,724,834,931,1022,1201,1356,1520,1686,1847,2007,2165,2285,2382,2488,2582,2677,2776,2868,2969,3076,3164,3247,3328,3429,3530,3622,3723,3824,3909", "endColumns": "117,98,110,112,119,107,109,96,90,178,154,163,165,160,159,157,119,96,105,93,94,98,91,100,106,87,82,80,100,100,91,100,100,84,93", "endOffsets": "168,267,378,491,611,719,829,926,1017,1196,1351,1515,1681,1842,2002,2160,2280,2377,2483,2577,2672,2771,2863,2964,3071,3159,3242,3323,3424,3525,3617,3718,3819,3904,3998"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14332,15773,15872,15983,16096,16216,16324,16434,16531,16622,16801,16956,17120,17286,17447,17607,17765,17885,17982,18088,18182,18277,18376,18468,18569,18676,18764,18847,18928,19029,19130,19222,19323,19424,19509", "endColumns": "117,98,110,112,119,107,109,96,90,178,154,163,165,160,159,157,119,96,105,93,94,98,91,100,106,87,82,80,100,100,91,100,100,84,93", "endOffsets": "14445,15867,15978,16091,16211,16319,16429,16526,16617,16796,16951,17115,17281,17442,17602,17760,17880,17977,18083,18177,18272,18371,18463,18564,18671,18759,18842,18923,19024,19125,19217,19318,19419,19504,19598"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,23568", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,23645"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,379,486,563,655,765,890,1004,1131,1212,1308,1394,1490,1604,1720,1821,1944,2065,2191,2337,2454,2564,2679,2788,2875,2970,3079,3200,3290,3389,3491,3613,3743,3849,3941,4017,4095,4177,4259,4359,4441,4524,4623,4721,4812,4911,4993,5090,5184,5281,5404,5486,5582", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "160,268,374,481,558,650,760,885,999,1126,1207,1303,1389,1485,1599,1715,1816,1939,2060,2186,2332,2449,2559,2674,2783,2870,2965,3074,3195,3285,3384,3486,3608,3738,3844,3936,4012,4090,4172,4254,4354,4436,4519,4618,4716,4807,4906,4988,5085,5179,5276,5399,5481,5577,5668"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2986,3096,3204,3310,8018,10349,10441,10551,10676,10790,10917,10998,11094,11180,11276,11390,11506,11607,11730,11851,11977,12123,12240,12350,12465,12574,12661,12756,12865,12986,13076,13175,13277,13399,13529,13635,13934,14165,23486,23724,23907,24272,24354,24437,24536,24634,24725,24824,24906,25003,25097,25194,25317,25399,25495", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "3091,3199,3305,3412,8090,10436,10546,10671,10785,10912,10993,11089,11175,11271,11385,11501,11602,11725,11846,11972,12118,12235,12345,12460,12569,12656,12751,12860,12981,13071,13170,13272,13394,13524,13630,13722,14005,14238,23563,23801,24002,24349,24432,24531,24629,24720,24819,24901,24998,25092,25189,25312,25394,25490,25581"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-lo/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9111", "endColumns": "131", "endOffsets": "9238"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-lo/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,326,380,460,532,588,711,788,869,981,1063,1131,1205,1301,1346,1403,1463,1537,1581,1622,1668,1708,1752,1808,1867,1927,1984,2043,2081,2144,2205,2271,2353,2434,2496,2557,2627,2674", "endColumns": "44,72,53,79,71,55,122,76,80,111,81,67,73,95,44,56,59,73,43,40,45,39,43,55,58,59,56,58,37,62,60,65,81,80,61,60,69,46,66", "endOffsets": "252,325,379,459,531,587,710,787,868,980,1062,1130,1204,1300,1345,1402,1462,1536,1580,1621,1667,1707,1751,1807,1866,1926,1983,2042,2080,2143,2204,2270,2352,2433,2495,2556,2626,2673,2740"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4518,4567,4644,4702,4786,4862,4993,5120,5201,5286,5402,5488,5560,5638,5738,5787,5848,5912,5990,6038,6410,6460,6504,6552,6612,6675,6739,6800,6863,6905,6972,7037,7107,7193,7278,7344,7409,7483,15702", "endColumns": "48,76,57,83,75,59,126,80,84,115,85,71,77,99,48,60,63,77,47,44,49,43,47,59,62,63,60,62,41,66,64,69,85,84,65,64,73,50,70", "endOffsets": "4562,4639,4697,4781,4857,4917,5115,5196,5281,5397,5483,5555,5633,5733,5782,5843,5907,5985,6033,6078,6455,6499,6547,6607,6670,6734,6795,6858,6900,6967,7032,7102,7188,7273,7339,7404,7478,7529,15768"}}]}]}