{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-nl/values-nl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8151,8243,14264,14361,14540,15062,15138,23800,23887,23976,24041,24187,24268,24517,24879,24963,25033", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "8238,8321,14356,14455,14620,15133,15229,23882,23971,24036,24101,24263,24346,24589,24958,25028,25148"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-nl/values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2873,2963,3040,3097,3148,3214,3292,3375,3463,3539,3614,3693,3766,3837,3946,4040,4118,4207,4297,4371,4452,4539,4592,4671,4738,4819,4903,4965,5029,5092,5163,5271,5383,5485,5596,5657,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2868,2958,3035,3092,3143,3209,3287,3370,3458,3534,3609,3688,3761,3832,3941,4035,4113,4202,4292,4366,4447,4534,4587,4666,4733,4814,4898,4960,5024,5087,5158,5266,5378,5480,5591,5652,5707,5788"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3503,3589,3671,3748,3846,7851,7948,8070,14625,14768,14983,15234,15297,15390,15452,15518,15576,15649,15713,15769,15891,15948,16010,16066,16142,20368,20453,20539,20677,20758,20837,20961,21051,21128,21185,21236,21302,21380,21463,21551,21627,21702,21781,21854,21925,22034,22128,22206,22295,22385,22459,22540,22627,22680,22759,22826,22907,22991,23053,23117,23180,23251,23359,23471,23573,23684,23745,24106", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "314,3584,3666,3743,3841,3935,7943,8065,8146,14684,14852,15057,15292,15385,15447,15513,15571,15644,15708,15764,15886,15943,16005,16061,16137,16271,20448,20534,20672,20753,20832,20956,21046,21123,21180,21231,21297,21375,21458,21546,21622,21697,21776,21849,21920,22029,22123,22201,22290,22380,22454,22535,22622,22675,22754,22821,22902,22986,23048,23112,23175,23246,23354,23466,23568,23679,23740,23795,24182"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3940,4042,4144,4244,4344,4451,4555,24677", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "4037,4139,4239,4339,4446,4550,4669,24773"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-nl/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,344,424,494", "endColumns": "66,78,79,69,73", "endOffsets": "264,343,423,493,567"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5089,6341,6424,6508,6582", "endColumns": "70,82,83,73,77", "endOffsets": "5155,6419,6503,6577,6655"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,403,517,599,693,805,953,1070,1208,1289,1384,1476,1575,1689,1806,1906,2033,2157,2289,2463,2590,2706,2824,2956,3049,3145,3261,3386,3482,3585,3684,3816,3952,4054,4153,4233,4312,4395,4478,4579,4655,4734,4829,4929,5020,5115,5199,5305,5402,5502,5618,5694,5791", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "168,281,398,512,594,688,800,948,1065,1203,1284,1379,1471,1570,1684,1801,1901,2028,2152,2284,2458,2585,2701,2819,2951,3044,3140,3256,3381,3477,3580,3679,3811,3947,4049,4148,4228,4307,4390,4473,4574,4650,4729,4824,4924,5015,5110,5194,5300,5397,5497,5613,5689,5786,5877"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3041,3159,3272,3389,8326,10710,10804,10916,11064,11181,11319,11400,11495,11587,11686,11800,11917,12017,12144,12268,12400,12574,12701,12817,12935,13067,13160,13256,13372,13497,13593,13696,13795,13927,14063,14165,14460,14689,24351,24594,24778,25153,25229,25308,25403,25503,25594,25689,25773,25879,25976,26076,26192,26268,26365", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "3154,3267,3384,3498,8403,10799,10911,11059,11176,11314,11395,11490,11582,11681,11795,11912,12012,12139,12263,12395,12569,12696,12812,12930,13062,13155,13251,13367,13492,13588,13691,13790,13922,14058,14160,14259,14535,14763,24429,24672,24874,25224,25303,25398,25498,25589,25684,25768,25874,25971,26071,26187,26263,26360,26451"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,24434", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,24512"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-nl/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,448,572,679,842,965,1084,1186,1360,1462,1627,1749,1908,2086,2150,2209", "endColumns": "103,150,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,447,571,678,841,964,1083,1185,1359,1461,1626,1748,1907,2085,2149,2208,2283"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8408,8516,8671,8799,8910,9077,9204,9327,9576,9754,9860,10029,10155,10318,10500,10568,10631", "endColumns": "107,154,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "8511,8666,8794,8905,9072,9199,9322,9428,9749,9855,10024,10150,10313,10495,10563,10626,10705"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,276,378,488,604,728,837,933,1020,1200,1381,1568,1754,1932,2120,2309,2429,2521,2622,2720,2819,2923,3018,3135,3249,3335,3421,3506,3605,3712,3805,3913,4023,4110", "endColumns": "125,94,101,109,115,123,108,95,86,179,180,186,185,177,187,188,119,91,100,97,98,103,94,116,113,85,85,84,98,106,92,107,109,86,95", "endOffsets": "176,271,373,483,599,723,832,928,1015,1195,1376,1563,1749,1927,2115,2304,2424,2516,2617,2715,2814,2918,3013,3130,3244,3330,3416,3501,3600,3707,3800,3908,4018,4105,4201"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14857,16343,16438,16540,16650,16766,16890,16999,17095,17182,17362,17543,17730,17916,18094,18282,18471,18591,18683,18784,18882,18981,19085,19180,19297,19411,19497,19583,19668,19767,19874,19967,20075,20185,20272", "endColumns": "125,94,101,109,115,123,108,95,86,179,180,186,185,177,187,188,119,91,100,97,98,103,94,116,113,85,85,84,98,106,92,107,109,86,95", "endOffsets": "14978,16433,16535,16645,16761,16885,16994,17090,17177,17357,17538,17725,17911,18089,18277,18466,18586,18678,18779,18877,18976,19080,19175,19292,19406,19492,19578,19663,19762,19869,19962,20070,20180,20267,20363"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-nl/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9433", "endColumns": "142", "endOffsets": "9571"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-nl/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,258,322,381,469,538,599,752,846,922,1045,1128,1192,1277,1372,1424,1493,1564,1638,1683,1724,1771,1814,1863,1929,1997,2059,2129,2197,2239,2304,2367,2435,2516,2598,2659,2716,2791,2843", "endColumns": "49,63,58,87,68,60,152,93,75,122,82,63,84,94,51,68,70,73,44,40,46,42,48,65,67,61,69,67,41,64,62,67,80,81,60,56,74,51,62", "endOffsets": "257,321,380,468,537,598,751,845,921,1044,1127,1191,1276,1371,1423,1492,1563,1637,1682,1723,1770,1813,1862,1928,1996,2058,2128,2196,2238,2303,2366,2434,2515,2597,2658,2715,2790,2842,2905"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4674,4728,4796,4859,4951,5024,5160,5317,5415,5495,5622,5709,5777,5866,5965,6021,6094,6169,6247,6296,6660,6711,6758,6811,6881,6953,7019,7093,7165,7211,7280,7347,7419,7504,7590,7655,7716,7795,16276", "endColumns": "53,67,62,91,72,64,156,97,79,126,86,67,88,98,55,72,74,77,48,44,50,46,52,69,71,65,73,71,45,68,66,71,84,85,64,60,78,55,66", "endOffsets": "4723,4791,4854,4946,5019,5084,5312,5410,5490,5617,5704,5772,5861,5960,6016,6089,6164,6242,6291,6336,6706,6753,6806,6876,6948,7014,7088,7160,7206,7275,7342,7414,7499,7585,7650,7711,7790,7846,16338"}}]}]}