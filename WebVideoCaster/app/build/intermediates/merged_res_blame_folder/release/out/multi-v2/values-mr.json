{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-mr/values-mr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,24007", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,24082"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,402,523,604,700,812,944,1063,1205,1286,1388,1475,1569,1676,1802,1909,2042,2171,2295,2470,2591,2703,2819,2939,3027,3118,3234,3359,3455,3554,3659,3791,3929,4040,4133,4205,4287,4368,4454,4550,4626,4705,4800,4895,4988,5083,5166,5266,5362,5461,5575,5651,5747", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "171,290,397,518,599,695,807,939,1058,1200,1281,1383,1470,1564,1671,1797,1904,2037,2166,2290,2465,2586,2698,2814,2934,3022,3113,3229,3354,3450,3549,3654,3786,3924,4035,4128,4200,4282,4363,4449,4545,4621,4700,4795,4890,4983,5078,5161,5261,5357,5456,5570,5646,5742,5832"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,3120,3239,3346,8226,10466,10562,10674,10806,10925,11067,11148,11250,11337,11431,11538,11664,11771,11904,12033,12157,12332,12453,12565,12681,12801,12889,12980,13096,13221,13317,13416,13521,13653,13791,13902,14190,14412,23926,24158,24345,24701,24777,24856,24951,25046,25139,25234,25317,25417,25513,25612,25726,25802,25898", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "3115,3234,3341,3462,8302,10557,10669,10801,10920,11062,11143,11245,11332,11426,11533,11659,11766,11899,12028,12152,12327,12448,12560,12676,12796,12884,12975,13091,13216,13312,13411,13516,13648,13786,13897,13990,14257,14489,24002,24239,24436,24772,24851,24946,25041,25134,25229,25312,25412,25508,25607,25721,25797,25893,25983"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-mr/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,345,429,503", "endColumns": "66,79,83,73,67", "endOffsets": "264,344,428,502,570"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5031,6259,6343,6431,6509", "endColumns": "70,83,87,77,71", "endOffsets": "5097,6338,6426,6504,6576"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1052,1143,1208,1267,1355,1417,1479,1539,1606,1669,1723,1837,1894,1955,2009,2079,2198,2279,2364,2499,2576,2653,2794,2880,2964,3020,3072,3138,3208,3286,3373,3455,3525,3601,3672,3741,3855,3951,4025,4123,4219,4293,4363,4465,4520,4608,4675,4762,4855,4918,4982,5045,5111,5211,5320,5414,5521,5581,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,140,85,83,55,51,65,69,77,86,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77", "endOffsets": "254,339,426,509,602,686,786,902,984,1047,1138,1203,1262,1350,1412,1474,1534,1601,1664,1718,1832,1889,1950,2004,2074,2193,2274,2359,2494,2571,2648,2789,2875,2959,3015,3067,3133,3203,3281,3368,3450,3520,3596,3667,3736,3850,3946,4020,4118,4214,4288,4358,4460,4515,4603,4670,4757,4850,4913,4977,5040,5106,5206,5315,5409,5516,5576,5632,5710"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3467,3552,3639,3722,3815,7752,7852,7968,14349,14494,14713,14949,15008,15096,15158,15220,15280,15347,15410,15464,15578,15635,15696,15750,15820,19933,20014,20099,20234,20311,20388,20529,20615,20699,20755,20807,20873,20943,21021,21108,21190,21260,21336,21407,21476,21590,21686,21760,21858,21954,22028,22098,22200,22255,22343,22410,22497,22590,22653,22717,22780,22846,22946,23055,23149,23256,23316,23683", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,140,85,83,55,51,65,69,77,86,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77", "endOffsets": "304,3547,3634,3717,3810,3894,7847,7963,8045,14407,14580,14773,15003,15091,15153,15215,15275,15342,15405,15459,15573,15630,15691,15745,15815,15934,20009,20094,20229,20306,20383,20524,20610,20694,20750,20802,20868,20938,21016,21103,21185,21255,21331,21402,21471,21585,21681,21755,21853,21949,22023,22093,22195,22250,22338,22405,22492,22585,22648,22712,22775,22841,22941,23050,23144,23251,23311,23367,23756"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8050,8144,13995,14092,14262,14778,14864,23372,23461,23544,23613,23761,23841,24087,24441,24517,24583", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "8139,8221,14087,14185,14344,14859,14944,23456,23539,23608,23678,23836,23921,24153,24512,24578,24696"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,282,393,504,622,745,853,950,1040,1220,1388,1558,1732,1902,2079,2249,2372,2464,2574,2668,2763,2862,2959,3075,3184,3268,3350,3433,3535,3639,3729,3828,3931,4017", "endColumns": "127,98,110,110,117,122,107,96,89,179,167,169,173,169,176,169,122,91,109,93,94,98,96,115,108,83,81,82,101,103,89,98,102,85,94", "endOffsets": "178,277,388,499,617,740,848,945,1035,1215,1383,1553,1727,1897,2074,2244,2367,2459,2569,2663,2758,2857,2954,3070,3179,3263,3345,3428,3530,3634,3724,3823,3926,4012,4107"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14585,16004,16103,16214,16325,16443,16566,16674,16771,16861,17041,17209,17379,17553,17723,17900,18070,18193,18285,18395,18489,18584,18683,18780,18896,19005,19089,19171,19254,19356,19460,19550,19649,19752,19838", "endColumns": "127,98,110,110,117,122,107,96,89,179,167,169,173,169,176,169,122,91,109,93,94,98,96,115,108,83,81,82,101,103,89,98,102,85,94", "endOffsets": "14708,16098,16209,16320,16438,16561,16669,16766,16856,17036,17204,17374,17548,17718,17895,18065,18188,18280,18390,18484,18579,18678,18775,18891,19000,19084,19166,19249,19351,19455,19545,19644,19747,19833,19928"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-mr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8307,8414,8582,8705,8817,8962,9083,9191,9431,9581,9689,9843,9967,10106,10259,10319,10385", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "8409,8577,8700,8812,8957,9078,9186,9283,9576,9684,9838,9962,10101,10254,10314,10380,10461"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-mr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9288", "endColumns": "142", "endOffsets": "9426"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-mr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,323,377,457,529,584,719,810,886,1007,1093,1165,1241,1340,1386,1453,1520,1594,1641,1685,1731,1774,1820,1887,1954,2015,2070,2125,2165,2233,2295,2362,2445,2535,2603,2665,2736,2784", "endColumns": "45,68,53,79,71,54,134,90,75,120,85,71,75,98,45,66,66,73,46,43,45,42,45,66,66,60,54,54,39,67,61,66,82,89,67,61,70,47,60", "endOffsets": "253,322,376,456,528,583,718,809,885,1006,1092,1164,1240,1339,1385,1452,1519,1593,1640,1684,1730,1773,1819,1886,1953,2014,2069,2124,2164,2232,2294,2361,2444,2534,2602,2664,2735,2783,2844"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4631,4681,4754,4812,4896,4972,5102,5241,5336,5416,5541,5631,5707,5787,5890,5940,6011,6082,6160,6211,6581,6631,6678,6728,6799,6870,6935,6994,7053,7097,7169,7235,7306,7393,7487,7559,7625,7700,15939", "endColumns": "49,72,57,83,75,58,138,94,79,124,89,75,79,102,49,70,70,77,50,47,49,46,49,70,70,64,58,58,43,71,65,70,86,93,71,65,74,51,64", "endOffsets": "4676,4749,4807,4891,4967,5026,5236,5331,5411,5536,5626,5702,5782,5885,5935,6006,6077,6155,6206,6254,6626,6673,6723,6794,6865,6930,6989,7048,7092,7164,7230,7301,7388,7482,7554,7620,7695,7747,15999"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3899,3999,4103,4204,4307,4409,4514,24244", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3994,4098,4199,4302,4404,4509,4626,24340"}}]}]}