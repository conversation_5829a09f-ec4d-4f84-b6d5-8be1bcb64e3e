{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-kn/values-kn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,413,532,614,708,820,973,1099,1247,1329,1431,1528,1634,1746,1875,1982,2117,2248,2377,2561,2681,2795,2913,3037,3135,3228,3346,3480,3582,3687,3789,3923,4064,4167,4271,4343,4425,4508,4593,4700,4776,4856,4952,5056,5152,5249,5332,5441,5539,5639,5756,5832,5938", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "170,290,408,527,609,703,815,968,1094,1242,1324,1426,1523,1629,1741,1870,1977,2112,2243,2372,2556,2676,2790,2908,3032,3130,3223,3341,3475,3577,3682,3784,3918,4059,4162,4266,4338,4420,4503,4588,4695,4771,4851,4947,5051,5147,5244,5327,5436,5534,5634,5751,5827,5933,6026"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3073,3193,3313,3431,8521,10839,10933,11045,11198,11324,11472,11554,11656,11753,11859,11971,12100,12207,12342,12473,12602,12786,12906,13020,13138,13262,13360,13453,13571,13705,13807,13912,14014,14148,14289,14392,14692,14916,24717,24962,25148,25519,25595,25675,25771,25875,25971,26068,26151,26260,26358,26458,26575,26651,26757", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "3188,3308,3426,3545,8598,10928,11040,11193,11319,11467,11549,11651,11748,11854,11966,12095,12202,12337,12468,12597,12781,12901,13015,13133,13257,13355,13448,13566,13700,13802,13907,14009,14143,14284,14387,14491,14759,14993,24795,25042,25250,25590,25670,25766,25870,25966,26063,26146,26255,26353,26453,26570,26646,26752,26845"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-kn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8603,8714,8884,9017,9132,9275,9404,9512,9757,9907,10020,10185,10320,10465,10622,10691,10754", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "8709,8879,9012,9127,9270,9399,9507,9606,9902,10015,10180,10315,10460,10617,10686,10749,10834"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-kn/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,440,517", "endColumns": "66,84,89,76,71", "endOffsets": "264,349,439,516,588"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5194,6488,6577,6671,6752", "endColumns": "70,88,93,80,75", "endOffsets": "5260,6572,6666,6747,6823"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4009,4107,4210,4311,4417,4518,4626,25047", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "4102,4205,4306,4412,4513,4621,4749,25143"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-kn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,329,394,487,566,624,783,874,951,1073,1160,1241,1325,1432,1479,1554,1631,1705,1750,1791,1832,1871,1915,1987,2061,2125,2190,2255,2298,2369,2432,2501,2588,2677,2745,2803,2879,2926", "endColumns": "46,73,64,92,78,57,158,90,76,121,86,80,83,106,46,74,76,73,44,40,40,38,43,71,73,63,64,64,42,70,62,68,86,88,67,57,75,46,65", "endOffsets": "254,328,393,486,565,623,782,873,950,1072,1159,1240,1324,1431,1478,1553,1630,1704,1749,1790,1831,1870,1914,1986,2060,2124,2189,2254,2297,2368,2431,2500,2587,2676,2744,2802,2878,2925,2991"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4754,4805,4883,4952,5049,5132,5265,5428,5523,5604,5730,5821,5906,5994,6105,6156,6235,6316,6394,6443,6828,6873,6916,6964,7040,7118,7186,7255,7324,7371,7446,7513,7586,7677,7770,7842,7904,7984,16501", "endColumns": "50,77,68,96,82,61,162,94,80,125,90,84,87,110,50,78,80,77,48,44,44,42,47,75,77,67,68,68,46,74,66,72,90,92,71,61,79,50,69", "endOffsets": "4800,4878,4947,5044,5127,5189,5423,5518,5599,5725,5816,5901,5989,6100,6151,6230,6311,6389,6438,6483,6868,6911,6959,7035,7113,7181,7250,7319,7366,7441,7508,7581,7672,7765,7837,7899,7979,8030,16566"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,353,436,518,633,728,835,948,1033,1096,1190,1256,1318,1421,1487,1558,1617,1693,1758,1812,1925,1983,2044,2098,2177,2293,2376,2467,2609,2688,2767,2896,2984,3068,3125,3177,3243,3323,3413,3497,3576,3653,3730,3807,3876,3993,4092,4169,4262,4357,4431,4512,4608,4659,4743,4811,4897,4985,5048,5113,5176,5244,5349,5454,5549,5652,5713,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "264,348,431,513,628,723,830,943,1028,1091,1185,1251,1313,1416,1482,1553,1612,1688,1753,1807,1920,1978,2039,2093,2172,2288,2371,2462,2604,2683,2762,2891,2979,3063,3120,3172,3238,3318,3408,3492,3571,3648,3725,3802,3871,3988,4087,4164,4257,4352,4426,4507,4603,4654,4738,4806,4892,4980,5043,5108,5171,5239,5344,5449,5544,5647,5708,5764,5846"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3550,3634,3717,3799,3914,8035,8142,8255,14853,14998,15221,15464,15526,15629,15695,15766,15825,15901,15966,16020,16133,16191,16252,16306,16385,20673,20756,20847,20989,21068,21147,21276,21364,21448,21505,21557,21623,21703,21793,21877,21956,22033,22110,22187,22256,22373,22472,22549,22642,22737,22811,22892,22988,23039,23123,23191,23277,23365,23428,23493,23556,23624,23729,23834,23929,24032,24093,24467", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "314,3629,3712,3794,3909,4004,8137,8250,8335,14911,15087,15282,15521,15624,15690,15761,15820,15896,15961,16015,16128,16186,16247,16301,16380,16496,20751,20842,20984,21063,21142,21271,21359,21443,21500,21552,21618,21698,21788,21872,21951,22028,22105,22182,22251,22368,22467,22544,22637,22732,22806,22887,22983,23034,23118,23186,23272,23360,23423,23488,23551,23619,23724,23829,23924,24027,24088,24144,24544"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,282,390,507,627,747,866,972,1064,1246,1433,1623,1820,2006,2197,2387,2505,2598,2714,2811,2907,3008,3102,3215,3338,3421,3503,3589,3699,3802,3895,3997,4106,4192", "endColumns": "128,97,107,116,119,119,118,105,91,181,186,189,196,185,190,189,117,92,115,96,95,100,93,112,122,82,81,85,109,102,92,101,108,85,93", "endOffsets": "179,277,385,502,622,742,861,967,1059,1241,1428,1618,1815,2001,2192,2382,2500,2593,2709,2806,2902,3003,3097,3210,3333,3416,3498,3584,3694,3797,3890,3992,4101,4187,4281"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15092,16571,16669,16777,16894,17014,17134,17253,17359,17451,17633,17820,18010,18207,18393,18584,18774,18892,18985,19101,19198,19294,19395,19489,19602,19725,19808,19890,19976,20086,20189,20282,20384,20493,20579", "endColumns": "128,97,107,116,119,119,118,105,91,181,186,189,196,185,190,189,117,92,115,96,95,100,93,112,122,82,81,85,109,102,92,101,108,85,93", "endOffsets": "15216,16664,16772,16889,17009,17129,17248,17354,17446,17628,17815,18005,18202,18388,18579,18769,18887,18980,19096,19193,19289,19390,19484,19597,19720,19803,19885,19971,20081,20184,20277,20379,20488,20574,20668"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,433,545,658,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,24800", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "428,540,653,741,848,974,1052,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2147,2263,2373,2472,2585,2690,2804,2968,3068,24878"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8340,8437,14496,14592,14764,15287,15371,24149,24240,24325,24396,24549,24631,24883,25255,25332,25401", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "8432,8516,14587,14687,14848,15366,15459,24235,24320,24391,24462,24626,24712,24957,25327,25396,25514"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-kn/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9611", "endColumns": "145", "endOffsets": "9752"}}]}]}