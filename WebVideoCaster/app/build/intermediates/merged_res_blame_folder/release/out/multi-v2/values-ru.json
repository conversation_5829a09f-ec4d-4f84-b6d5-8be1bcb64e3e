{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-ru/values-ru.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,679,790,927,1040,1176,1256,1350,1439,1533,1644,1763,1869,1994,2118,2240,2416,2536,2655,2779,2896,2983,3079,3196,3325,3419,3529,3633,3760,3894,3997,4092,4173,4251,4341,4424,4528,4604,4684,4778,4875,4965,5056,5140,5242,5336,5430,5573,5649,5751", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "164,276,390,504,581,674,785,922,1035,1171,1251,1345,1434,1528,1639,1758,1864,1989,2113,2235,2411,2531,2650,2774,2891,2978,3074,3191,3320,3414,3524,3628,3755,3889,3992,4087,4168,4246,4336,4419,4523,4599,4679,4773,4870,4960,5051,5135,5237,5331,5425,5568,5644,5746,5839"}, "to": {"startLines": "35,36,37,38,99,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151,154,258,261,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,3262,3374,3488,8361,10711,10804,10915,11052,11165,11301,11381,11475,11564,11658,11769,11888,11994,12119,12243,12365,12541,12661,12780,12904,13021,13108,13204,13321,13450,13544,13654,13758,13885,14019,14122,14417,14656,24273,24517,24701,25082,25158,25238,25332,25429,25519,25610,25694,25796,25890,25984,26127,26203,26305", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "3257,3369,3483,3597,8433,10799,10910,11047,11160,11296,11376,11470,11559,11653,11764,11883,11989,12114,12238,12360,12536,12656,12775,12899,13016,13103,13199,13316,13445,13539,13649,13753,13880,14014,14117,14212,14493,14729,24358,24595,24800,25153,25233,25327,25424,25514,25605,25689,25791,25885,25979,26122,26198,26300,26393"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ru/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,257,338,392,469,539,589,714,801,875,981,1064,1131,1205,1296,1351,1422,1493,1567,1615,1664,1713,1761,1814,1883,1952,2009,2077,2146,2191,2259,2322,2387,2463,2543,2603,2660,2730,2780", "endColumns": "48,80,53,76,69,49,124,86,73,105,82,66,73,90,54,70,70,73,47,48,48,47,52,68,68,56,67,68,44,67,62,64,75,79,59,56,69,49,60", "endOffsets": "256,337,391,468,538,588,713,800,874,980,1063,1130,1204,1295,1350,1421,1492,1566,1614,1663,1712,1760,1813,1882,1951,2008,2076,2145,2190,2258,2321,2386,2462,2542,2602,2659,2729,2779,2840"}, "to": {"startLines": "51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4811,4896,4954,5035,5109,5234,5363,5454,5532,5642,5729,5800,5878,5973,6032,6107,6182,6260,6312,6671,6724,6776,6833,6906,6979,7040,7112,7185,7234,7306,7373,7442,7522,7606,7670,7731,7805,16242", "endColumns": "52,84,57,80,73,53,128,90,77,109,86,70,77,94,58,74,74,77,51,52,52,51,56,72,72,60,71,72,48,71,66,68,79,83,63,60,73,53,64", "endOffsets": "4806,4891,4949,5030,5104,5158,5358,5449,5527,5637,5724,5795,5873,5968,6027,6102,6177,6255,6307,6360,6719,6771,6828,6901,6974,7035,7107,7180,7229,7301,7368,7437,7517,7601,7665,7726,7800,7854,16302"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ru/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8438,8545,8711,8837,8947,9089,9218,9333,9594,9775,9882,10045,10171,10338,10496,10565,10625", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "8540,8706,8832,8942,9084,9213,9328,9432,9770,9877,10040,10166,10333,10491,10560,10620,10706"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ru/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "9437", "endColumns": "156", "endOffsets": "9589"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "44,45,46,47,48,49,50,262", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4031,4129,4231,4332,4433,4538,4641,24600", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4124,4226,4327,4428,4533,4636,4753,24696"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ru/values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "2,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3602,3680,3758,3842,3940,7859,7956,8093,14590,14734,14953,15202,15265,15383,15444,15509,15566,15636,15697,15751,15867,15924,15986,16040,16114,20288,20376,20462,20599,20683,20768,20902,20993,21069,21123,21174,21240,21312,21390,21486,21568,21648,21724,21801,21878,21985,22074,22147,22237,22332,22406,22487,22580,22635,22716,22782,22868,22953,23015,23079,23142,23214,23312,23411,23506,23598,23656,24022", "endLines": "7,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "426,3675,3753,3837,3935,4026,7951,8088,8180,14651,14828,15025,15260,15378,15439,15504,15561,15631,15692,15746,15862,15919,15981,16035,16109,16237,20371,20457,20594,20678,20763,20897,20988,21064,21118,21169,21235,21307,21385,21481,21563,21643,21719,21796,21873,21980,22069,22142,22232,22327,22401,22482,22575,22630,22711,22777,22863,22948,23010,23074,23137,23209,23307,23406,23501,23593,23651,23706,24097"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,277,386,502,617,724,830,926,1018,1193,1373,1552,1732,1910,2098,2274,2391,2481,2587,2685,2779,2872,2966,3066,3176,3267,3358,3446,3552,3658,3752,3859,3972,4059", "endColumns": "119,101,108,115,114,106,105,95,91,174,179,178,179,177,187,175,116,89,105,97,93,92,93,99,109,90,90,87,105,105,93,106,112,86,96", "endOffsets": "170,272,381,497,612,719,825,921,1013,1188,1368,1547,1727,1905,2093,2269,2386,2476,2582,2680,2774,2867,2961,3061,3171,3262,3353,3441,3547,3653,3747,3854,3967,4054,4151"}, "to": {"startLines": "156,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14833,16307,16409,16518,16634,16749,16856,16962,17058,17150,17325,17505,17684,17864,18042,18230,18406,18523,18613,18719,18817,18911,19004,19098,19198,19308,19399,19490,19578,19684,19790,19884,19991,20104,20191", "endColumns": "119,101,108,115,114,106,105,95,91,174,179,178,179,177,187,175,116,89,105,97,93,92,93,99,109,90,90,87,105,105,93,106,112,86,96", "endOffsets": "14948,16404,16513,16629,16744,16851,16957,17053,17145,17320,17500,17679,17859,18037,18225,18401,18518,18608,18714,18812,18906,18999,19093,19193,19303,19394,19485,19573,19679,19785,19879,19986,20099,20186,20283"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,24363", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,24440"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ru/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,343,424,492", "endColumns": "66,77,80,67,62", "endOffsets": "264,342,423,491,554"}, "to": {"startLines": "57,72,73,74,75", "startColumns": "4,4,4,4,4", "startOffsets": "5163,6365,6447,6532,6604", "endColumns": "70,81,84,71,66", "endOffsets": "5229,6442,6527,6599,6666"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "97,98,149,150,152,158,159,251,252,253,254,256,257,260,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8185,8278,14217,14315,14498,15030,15112,23711,23799,23881,23952,24102,24186,24445,24805,24889,24959", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "8273,8356,14310,14412,14585,15107,15197,23794,23876,23947,24017,24181,24268,24512,24884,24954,25077"}}]}]}