{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-eu/values-eu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8373,8464,14500,14601,14777,15300,15376,23914,24003,24087,24162,24317,24405,24663,25028,25105,25173", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "8459,8540,14596,14700,14864,15371,15458,23998,24082,24157,24229,24400,24490,24732,25100,25168,25288"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1137,1236,1304,1365,1453,1516,1582,1646,1717,1780,1834,1943,2002,2065,2119,2193,2318,2408,2488,2633,2716,2798,2936,3027,3110,3162,3215,3281,3352,3432,3518,3598,3676,3754,3827,3902,4009,4096,4183,4274,4367,4439,4515,4607,4658,4740,4806,4890,4976,5038,5102,5165,5233,5340,5449,5545,5650,5706,5763", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "270,377,482,562,669,769,867,982,1065,1132,1231,1299,1360,1448,1511,1577,1641,1712,1775,1829,1938,1997,2060,2114,2188,2313,2403,2483,2628,2711,2793,2931,3022,3105,3157,3210,3276,3347,3427,3513,3593,3671,3749,3822,3897,4004,4091,4178,4269,4362,4434,4510,4602,4653,4735,4801,4885,4971,5033,5097,5160,5228,5335,5444,5540,5645,5701,5758,5841"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3620,3727,3832,3912,4019,8077,8175,8290,14869,15013,15232,15463,15524,15612,15675,15741,15805,15876,15939,15993,16102,16161,16224,16278,16352,20469,20559,20639,20784,20867,20949,21087,21178,21261,21313,21366,21432,21503,21583,21669,21749,21827,21905,21978,22053,22160,22247,22334,22425,22518,22590,22666,22758,22809,22891,22957,23041,23127,23189,23253,23316,23384,23491,23600,23696,23801,23857,24234", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "320,3722,3827,3907,4014,4114,8170,8285,8368,14931,15107,15295,15519,15607,15670,15736,15800,15871,15934,15988,16097,16156,16219,16273,16347,16472,20554,20634,20779,20862,20944,21082,21173,21256,21308,21361,21427,21498,21578,21664,21744,21822,21900,21973,22048,22155,22242,22329,22420,22513,22585,22661,22753,22804,22886,22952,23036,23122,23184,23248,23311,23379,23486,23595,23691,23796,23852,23909,24312"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,270,370,476,587,705,812,908,995,1184,1350,1522,1693,1862,2035,2205,2315,2413,2518,2613,2708,2804,2897,3008,3132,3217,3308,3393,3494,3606,3700,3802,3917,4004", "endColumns": "119,94,99,105,110,117,106,95,86,188,165,171,170,168,172,169,109,97,104,94,94,95,92,110,123,84,90,84,100,111,93,101,114,86,93", "endOffsets": "170,265,365,471,582,700,807,903,990,1179,1345,1517,1688,1857,2030,2200,2310,2408,2513,2608,2703,2799,2892,3003,3127,3212,3303,3388,3489,3601,3695,3797,3912,3999,4093"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15112,16546,16641,16741,16847,16958,17076,17183,17279,17366,17555,17721,17893,18064,18233,18406,18576,18686,18784,18889,18984,19079,19175,19268,19379,19503,19588,19679,19764,19865,19977,20071,20173,20288,20375", "endColumns": "119,94,99,105,110,117,106,95,86,188,165,171,170,168,172,169,109,97,104,94,94,95,92,110,123,84,90,84,100,111,93,101,114,86,93", "endOffsets": "15227,16636,16736,16842,16953,17071,17178,17274,17361,17550,17716,17888,18059,18228,18401,18571,18681,18779,18884,18979,19074,19170,19263,19374,19498,19583,19674,19759,19860,19972,20066,20168,20283,20370,20464"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-eu/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,436,504", "endColumns": "66,84,85,67,65", "endOffsets": "264,349,435,503,569"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5268,6552,6641,6731,6803", "endColumns": "70,88,89,71,69", "endOffsets": "5334,6636,6726,6798,6868"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,341,464,605,683,777,889,1025,1140,1281,1361,1462,1554,1650,1765,1881,1987,2126,2266,2397,2584,2705,2823,2944,3063,3156,3249,3373,3504,3598,3695,3797,3939,4086,4190,4285,4357,4434,4519,4603,4709,4785,4867,4958,5057,5144,5239,5325,5429,5525,5626,5740,5816,5916", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "193,336,459,600,678,772,884,1020,1135,1276,1356,1457,1549,1645,1760,1876,1982,2121,2261,2392,2579,2700,2818,2939,3058,3151,3244,3368,3499,3593,3690,3792,3934,4081,4185,4280,4352,4429,4514,4598,4704,4780,4862,4953,5052,5139,5234,5320,5424,5520,5621,5735,5811,5911,6002"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,3213,3356,3479,8545,10898,10992,11104,11240,11355,11496,11576,11677,11769,11865,11980,12096,12202,12341,12481,12612,12799,12920,13038,13159,13278,13371,13464,13588,13719,13813,13910,14012,14154,14301,14405,14705,14936,24495,24737,24922,25293,25369,25451,25542,25641,25728,25823,25909,26013,26109,26210,26324,26400,26500", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "3208,3351,3474,3615,8618,10987,11099,11235,11350,11491,11571,11672,11764,11860,11975,12091,12197,12336,12476,12607,12794,12915,13033,13154,13273,13366,13459,13583,13714,13808,13905,14007,14149,14296,14400,14495,14772,15008,24575,24816,25023,25364,25446,25537,25636,25723,25818,25904,26008,26104,26205,26319,26395,26495,26586"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,24580", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,24658"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-eu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8623,8727,8899,9023,9132,9284,9409,9533,9784,9962,10070,10233,10361,10515,10675,10741,10806", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "8722,8894,9018,9127,9279,9404,9528,9636,9957,10065,10228,10356,10510,10670,10736,10801,10893"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4119,4217,4320,4420,4523,4628,4731,24821", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "4212,4315,4415,4518,4623,4726,4845,24917"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-eu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,256,325,383,474,549,602,751,832,914,1047,1142,1210,1291,1400,1447,1511,1583,1657,1706,1759,1802,1850,1895,1957,2027,2097,2164,2230,2272,2350,2414,2477,2557,2639,2706,2765,2839,2891", "endColumns": "47,68,57,90,74,52,148,80,81,132,94,67,80,108,46,63,71,73,48,52,42,47,44,61,69,69,66,65,41,77,63,62,79,81,66,58,73,51,64", "endOffsets": "255,324,382,473,548,601,750,831,913,1046,1141,1209,1290,1399,1446,1510,1582,1656,1705,1758,1801,1849,1894,1956,2026,2096,2163,2229,2271,2349,2413,2476,2556,2638,2705,2764,2838,2890,2955"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4850,4902,4975,5037,5132,5211,5339,5492,5577,5663,5800,5899,5971,6056,6169,6220,6288,6364,6442,6495,6873,6920,6972,7021,7087,7161,7235,7306,7376,7422,7504,7572,7639,7723,7809,7880,7943,8021,16477", "endColumns": "51,72,61,94,78,56,152,84,85,136,98,71,84,112,50,67,75,77,52,56,46,51,48,65,73,73,70,69,45,81,67,66,83,85,70,62,77,55,68", "endOffsets": "4897,4970,5032,5127,5206,5263,5487,5572,5658,5795,5894,5966,6051,6164,6215,6283,6359,6437,6490,6547,6915,6967,7016,7082,7156,7230,7301,7371,7417,7499,7567,7634,7718,7804,7875,7938,8016,8072,16541"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-eu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9641", "endColumns": "142", "endOffsets": "9779"}}]}]}