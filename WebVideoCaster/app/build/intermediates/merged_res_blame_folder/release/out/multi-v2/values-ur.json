{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-ur/values-ur.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ur/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,323,378,461,535,590,716,802,877,992,1075,1147,1235,1344,1393,1454,1516,1590,1636,1681,1727,1768,1818,1879,1941,2007,2065,2124,2164,2228,2290,2359,2436,2516,2586,2648,2719,2770", "endColumns": "44,69,54,82,73,54,125,85,74,114,82,71,87,108,48,60,61,73,45,44,45,40,49,60,61,65,57,58,39,63,61,68,76,79,69,61,70,50,60", "endOffsets": "252,322,377,460,534,589,715,801,876,991,1074,1146,1234,1343,1392,1453,1515,1589,1635,1680,1726,1767,1817,1878,1940,2006,2064,2123,2163,2227,2289,2358,2435,2515,2585,2647,2718,2769,2830"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4636,4685,4759,4818,4905,4983,5113,5243,5333,5412,5531,5618,5694,5786,5899,5952,6017,6083,6161,6211,6579,6629,6674,6728,6793,6859,6929,6991,7054,7098,7166,7232,7305,7386,7470,7544,7610,7685,16113", "endColumns": "48,73,58,86,77,58,129,89,78,118,86,75,91,112,52,64,65,77,49,48,49,44,53,64,65,69,61,62,43,67,65,72,80,83,73,65,74,54,64", "endOffsets": "4680,4754,4813,4900,4978,5037,5238,5328,5407,5526,5613,5689,5781,5894,5947,6012,6078,6156,6206,6255,6624,6669,6723,6788,6854,6924,6986,7049,7093,7161,7227,7300,7381,7465,7539,7605,7680,7735,16173"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,287,399,513,591,684,796,927,1046,1181,1262,1362,1454,1550,1663,1789,1894,2034,2173,2301,2495,2619,2734,2854,2989,3082,3173,3293,3413,3510,3611,3713,3853,4000,4102,4201,4273,4352,4438,4525,4636,4722,4803,4902,5004,5097,5196,5277,5380,5475,5573,5709,5795,5893", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "164,282,394,508,586,679,791,922,1041,1176,1257,1357,1449,1545,1658,1784,1889,2029,2168,2296,2490,2614,2729,2849,2984,3077,3168,3288,3408,3505,3606,3708,3848,3995,4097,4196,4268,4347,4433,4520,4631,4717,4798,4897,4999,5092,5191,5272,5375,5470,5568,5704,5790,5888,5978"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,3150,3268,3380,8219,10536,10629,10741,10872,10991,11126,11207,11307,11399,11495,11608,11734,11839,11979,12118,12246,12440,12564,12679,12799,12934,13027,13118,13238,13358,13455,13556,13658,13798,13945,14047,14333,14558,24025,24272,24460,24829,24915,24996,25095,25197,25290,25389,25470,25573,25668,25766,25902,25988,26086", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "3145,3263,3375,3489,8292,10624,10736,10867,10986,11121,11202,11302,11394,11490,11603,11729,11834,11974,12113,12241,12435,12559,12674,12794,12929,13022,13113,13233,13353,13450,13551,13653,13793,13940,14042,14141,14400,14632,24106,24354,24566,24910,24991,25090,25192,25285,25384,25465,25568,25663,25761,25897,25983,26081,26171"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ur/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8297,8405,8569,8695,8806,8946,9073,9185,9441,9593,9703,9875,10003,10149,10318,10381,10448", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "8400,8564,8690,8801,8941,9068,9180,9284,9588,9698,9870,9998,10144,10313,10376,10443,10531"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1047,1140,1210,1269,1359,1423,1492,1550,1619,1679,1743,1855,1914,1973,2028,2103,2226,2306,2390,2523,2605,2686,2817,2904,2986,3044,3100,3166,3241,3321,3406,3485,3552,3627,3704,3768,3875,3969,4039,4128,4221,4295,4370,4460,4516,4595,4662,4746,4830,4892,4956,5019,5085,5185,5292,5386,5494,5556,5616", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,130,86,81,57,55,65,74,79,84,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79", "endOffsets": "254,332,410,488,586,675,775,894,977,1042,1135,1205,1264,1354,1418,1487,1545,1614,1674,1738,1850,1909,1968,2023,2098,2221,2301,2385,2518,2600,2681,2812,2899,2981,3039,3095,3161,3236,3316,3401,3480,3547,3622,3699,3763,3870,3964,4034,4123,4216,4290,4365,4455,4511,4590,4657,4741,4825,4887,4951,5014,5080,5180,5287,5381,5489,5551,5611,5691"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3494,3572,3650,3728,3826,7740,7840,7959,14493,14637,14853,15097,15156,15246,15310,15379,15437,15506,15566,15630,15742,15801,15860,15915,15990,20079,20159,20243,20376,20458,20539,20670,20757,20839,20897,20953,21019,21094,21174,21259,21338,21405,21480,21557,21621,21728,21822,21892,21981,22074,22148,22223,22313,22369,22448,22515,22599,22683,22745,22809,22872,22938,23038,23145,23239,23347,23409,23777", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,130,86,81,57,55,65,74,79,84,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79", "endOffsets": "304,3567,3645,3723,3821,3910,7835,7954,8037,14553,14725,14918,15151,15241,15305,15374,15432,15501,15561,15625,15737,15796,15855,15910,15985,16108,20154,20238,20371,20453,20534,20665,20752,20834,20892,20948,21014,21089,21169,21254,21333,21400,21475,21552,21616,21723,21817,21887,21976,22069,22143,22218,22308,22364,22443,22510,22594,22678,22740,22804,22867,22933,23033,23140,23234,23342,23404,23464,23852"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,276,381,492,604,725,842,946,1038,1213,1378,1545,1716,1879,2048,2216,2333,2424,2533,2632,2726,2820,2914,3031,3146,3234,3318,3401,3504,3605,3697,3801,3904,3988", "endColumns": "122,97,104,110,111,120,116,103,91,174,164,166,170,162,168,167,116,90,108,98,93,93,93,116,114,87,83,82,102,100,91,103,102,83,90", "endOffsets": "173,271,376,487,599,720,837,941,1033,1208,1373,1540,1711,1874,2043,2211,2328,2419,2528,2627,2721,2815,2909,3026,3141,3229,3313,3396,3499,3600,3692,3796,3899,3983,4074"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14730,16178,16276,16381,16492,16604,16725,16842,16946,17038,17213,17378,17545,17716,17879,18048,18216,18333,18424,18533,18632,18726,18820,18914,19031,19146,19234,19318,19401,19504,19605,19697,19801,19904,19988", "endColumns": "122,97,104,110,111,120,116,103,91,174,164,166,170,162,168,167,116,90,108,98,93,93,93,116,114,87,83,82,102,100,91,103,102,83,90", "endOffsets": "14848,16271,16376,16487,16599,16720,16837,16941,17033,17208,17373,17540,17711,17874,18043,18211,18328,18419,18528,18627,18721,18815,18909,19026,19141,19229,19313,19396,19499,19600,19692,19796,19899,19983,20074"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,24111", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,24192"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,972,1039,1122,1207,1282,1357,1423", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,967,1034,1117,1202,1277,1352,1418,1535"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8042,8136,14146,14236,14405,14923,15004,23469,23557,23643,23710,23857,23940,24197,24571,24646,24712", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "8131,8214,14231,14328,14488,14999,15092,23552,23638,23705,23772,23935,24020,24267,24641,24707,24824"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ur/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,342,427,500", "endColumns": "66,76,84,72,67", "endOffsets": "264,341,426,499,567"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5042,6260,6341,6430,6507", "endColumns": "70,80,88,76,71", "endOffsets": "5108,6336,6425,6502,6574"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3915,4013,4115,4217,4321,4424,4522,24359", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "4008,4110,4212,4316,4419,4517,4631,24455"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ur/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9289", "endColumns": "151", "endOffsets": "9436"}}]}]}