{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-sw/values-sw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,24526", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,24604"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,270,373,476,583,709,817,912,999,1182,1373,1565,1766,1962,2158,2349,2471,2569,2671,2767,2860,2949,3040,3159,3280,3365,3448,3538,3634,3740,3835,3933,4042,4127", "endColumns": "121,92,102,102,106,125,107,94,86,182,190,191,200,195,195,190,121,97,101,95,92,88,90,118,120,84,82,89,95,105,94,97,108,84,91", "endOffsets": "172,265,368,471,578,704,812,907,994,1177,1368,1560,1761,1957,2153,2344,2466,2564,2666,2762,2855,2944,3035,3154,3275,3360,3443,3533,3629,3735,3830,3928,4037,4122,4214"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14851,16334,16427,16530,16633,16740,16866,16974,17069,17156,17339,17530,17722,17923,18119,18315,18506,18628,18726,18828,18924,19017,19106,19197,19316,19437,19522,19605,19695,19791,19897,19992,20090,20199,20284", "endColumns": "121,92,102,102,106,125,107,94,86,182,190,191,200,195,195,190,121,97,101,95,92,88,90,118,120,84,82,89,95,105,94,97,108,84,91", "endOffsets": "14968,16422,16525,16628,16735,16861,16969,17064,17151,17334,17525,17717,17918,18114,18310,18501,18623,18721,18823,18919,19012,19101,19192,19311,19432,19517,19600,19690,19786,19892,19987,20085,20194,20279,20371"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-sw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,318,405,483,554,611,748,835,917,1052,1150,1217,1293,1393,1438,1508,1584,1658,1712,1757,1800,1840,1890,1960,2036,2105,2179,2255,2299,2376,2439,2504,2579,2663,2725,2784,2853,2903", "endColumns": "45,63,86,77,70,56,136,86,81,134,97,66,75,99,44,69,75,73,53,44,42,39,49,69,75,68,73,75,43,76,62,64,74,83,61,58,68,49,60", "endOffsets": "253,317,404,482,553,610,747,834,916,1051,1149,1216,1292,1392,1437,1507,1583,1657,1711,1756,1799,1839,1889,1959,2035,2104,2178,2254,2298,2375,2438,2503,2578,2662,2724,2783,2852,2902,2963"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4582,4632,4700,4791,4873,4948,5080,5221,5312,5398,5537,5639,5710,5790,5894,5943,6017,6097,6175,6233,6604,6651,6695,6749,6823,6903,6976,7054,7134,7182,7263,7330,7399,7478,7566,7632,7695,7768,16269", "endColumns": "49,67,90,81,74,60,140,90,85,138,101,70,79,103,48,73,79,77,57,48,46,43,53,73,79,72,77,79,47,80,66,68,78,87,65,62,72,53,64", "endOffsets": "4627,4695,4786,4868,4943,5004,5216,5307,5393,5532,5634,5705,5785,5889,5938,6012,6092,6170,6228,6277,6646,6690,6744,6818,6898,6971,7049,7129,7177,7258,7325,7394,7473,7561,7627,7690,7763,7817,16329"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-sw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9401", "endColumns": "145", "endOffsets": "9542"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2859,2947,3026,3080,3133,3199,3271,3353,3443,3528,3600,3675,3746,3819,3925,4022,4096,4191,4288,4362,4447,4547,4600,4685,4753,4841,4931,4993,5057,5120,5187,5304,5416,5527,5638,5696,5753", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2854,2942,3021,3075,3128,3194,3266,3348,3438,3523,3595,3670,3741,3814,3920,4017,4091,4186,4283,4357,4442,4542,4595,4680,4748,4836,4926,4988,5052,5115,5182,5299,5411,5522,5633,5691,5748,5829"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3450,3526,3600,3673,3770,7822,7921,8050,14612,14759,14973,15228,15291,15377,15439,15502,15567,15635,15698,15752,15884,15941,16003,16057,16131,20376,20457,20537,20669,20754,20841,20982,21070,21149,21203,21256,21322,21394,21476,21566,21651,21723,21798,21869,21942,22048,22145,22219,22314,22411,22485,22570,22670,22723,22808,22876,22964,23054,23116,23180,23243,23310,23427,23539,23650,23761,23819,24190", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "304,3521,3595,3668,3765,3854,7916,8045,8128,14675,14846,15041,15286,15372,15434,15497,15562,15630,15693,15747,15879,15936,15998,16052,16126,16264,20452,20532,20664,20749,20836,20977,21065,21144,21198,21251,21317,21389,21471,21561,21646,21718,21793,21864,21937,22043,22140,22214,22309,22406,22480,22565,22665,22718,22803,22871,22959,23049,23111,23175,23238,23305,23422,23534,23645,23756,23814,23871,24266"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3859,3953,4055,4152,4253,4360,4467,24764", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3948,4050,4147,4248,4355,4462,4577,24860"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,993,1064,1149,1236,1310,1387,1457", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,988,1059,1144,1231,1305,1382,1452,1573"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8133,8227,14248,14349,14526,15046,15127,23876,23967,24049,24119,24271,24356,24609,24975,25052,25122", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "8222,8303,14344,14445,14607,15122,15223,23962,24044,24114,24185,24351,24438,24678,25047,25117,25238"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-sw/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,351,437,512", "endColumns": "66,85,85,74,58", "endOffsets": "264,350,436,511,570"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5009,6282,6372,6462,6541", "endColumns": "70,89,89,78,62", "endOffsets": "5075,6367,6457,6536,6599"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-sw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8386,8490,8646,8771,8880,9043,9168,9287,9547,9712,9818,9972,10099,10249,10408,10476,10551", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "8485,8641,8766,8875,9038,9163,9282,9396,9707,9813,9967,10094,10244,10403,10471,10546,10642"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,390,500,578,674,788,924,1040,1188,1270,1366,1455,1553,1670,1793,1894,2025,2155,2298,2459,2608,2728,2854,2987,3077,3169,3287,3413,3506,3606,3712,3839,3976,4084,4179,4255,4334,4417,4498,4608,4689,4768,4863,4959,5057,5156,5240,5341,5437,5535,5656,5736,5850", "endColumns": "111,110,111,109,77,95,113,135,115,147,81,95,88,97,116,122,100,130,129,142,160,148,119,125,132,89,91,117,125,92,99,105,126,136,107,94,75,78,82,80,109,80,78,94,95,97,98,83,100,95,97,120,79,113,105", "endOffsets": "162,273,385,495,573,669,783,919,1035,1183,1265,1361,1450,1548,1665,1788,1889,2020,2150,2293,2454,2603,2723,2849,2982,3072,3164,3282,3408,3501,3601,3707,3834,3971,4079,4174,4250,4329,4412,4493,4603,4684,4763,4858,4954,5052,5151,5235,5336,5432,5530,5651,5731,5845,5951"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3005,3117,3228,3340,8308,10647,10743,10857,10993,11109,11257,11339,11435,11524,11622,11739,11862,11963,12094,12224,12367,12528,12677,12797,12923,13056,13146,13238,13356,13482,13575,13675,13781,13908,14045,14153,14450,14680,24443,24683,24865,25243,25324,25403,25498,25594,25692,25791,25875,25976,26072,26170,26291,26371,26485", "endColumns": "111,110,111,109,77,95,113,135,115,147,81,95,88,97,116,122,100,130,129,142,160,148,119,125,132,89,91,117,125,92,99,105,126,136,107,94,75,78,82,80,109,80,78,94,95,97,98,83,100,95,97,120,79,113,105", "endOffsets": "3112,3223,3335,3445,8381,10738,10852,10988,11104,11252,11334,11430,11519,11617,11734,11857,11958,12089,12219,12362,12523,12672,12792,12918,13051,13141,13233,13351,13477,13570,13670,13776,13903,14040,14148,14243,14521,14754,24521,24759,24970,25319,25398,25493,25589,25687,25786,25870,25971,26067,26165,26286,26366,26480,26586"}}]}]}