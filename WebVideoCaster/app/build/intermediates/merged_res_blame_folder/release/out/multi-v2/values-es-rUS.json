{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8239,8338,14406,14504,14680,15209,15288,24096,24188,24275,24339,24486,24573,24829,25199,25277,25347", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "8333,8415,14499,14602,14764,15283,15379,24183,24270,24334,24398,24568,24658,24901,25272,25342,25465"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,24746", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,24824"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1103,1198,1268,1331,1424,1488,1560,1623,1697,1761,1817,1935,1993,2055,2111,2191,2325,2414,2495,2636,2717,2797,2948,3038,3115,3171,3227,3293,3369,3451,3539,3628,3701,3778,3848,3925,4031,4120,4194,4288,4390,4462,4543,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5402,5513,5615,5720,5780,5840", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "273,355,435,521,626,722,824,952,1033,1098,1193,1263,1326,1419,1483,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2320,2409,2490,2631,2712,2792,2943,3033,3110,3166,3222,3288,3364,3446,3534,3623,3696,3773,3843,3920,4026,4115,4189,4283,4385,4457,4538,4642,4695,4780,4847,4940,5029,5091,5155,5218,5286,5397,5508,5610,5715,5775,5835,5918"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3515,3597,3677,3763,3868,7928,8030,8158,14769,14911,15139,15384,15447,15540,15604,15676,15739,15813,15877,15933,16051,16109,16171,16227,16307,20581,20670,20751,20892,20973,21053,21204,21294,21371,21427,21483,21549,21625,21707,21795,21884,21957,22034,22104,22181,22287,22376,22450,22544,22646,22718,22799,22903,22956,23041,23108,23201,23290,23352,23416,23479,23547,23658,23769,23871,23976,24036,24403", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "323,3592,3672,3758,3863,3959,8025,8153,8234,14829,15001,15204,15442,15535,15599,15671,15734,15808,15872,15928,16046,16104,16166,16222,16302,16436,20665,20746,20887,20968,21048,21199,21289,21366,21422,21478,21544,21620,21702,21790,21879,21952,22029,22099,22176,22282,22371,22445,22539,22641,22713,22794,22898,22951,23036,23103,23196,23285,23347,23411,23474,23542,23653,23764,23866,23971,24031,24091,24481"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,410,526,604,698,810,949,1063,1211,1292,1390,1483,1581,1695,1814,1914,2046,2175,2310,2482,2609,2725,2844,2968,3062,3154,3271,3400,3497,3598,3709,3839,3976,4083,4183,4256,4333,4416,4501,4608,4686,4766,4863,4965,5061,5156,5240,5351,5448,5547,5666,5744,5847", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "168,286,405,521,599,693,805,944,1058,1206,1287,1385,1478,1576,1690,1809,1909,2041,2170,2305,2477,2604,2720,2839,2963,3057,3149,3266,3395,3492,3593,3704,3834,3971,4078,4178,4251,4328,4411,4496,4603,4681,4761,4858,4960,5056,5151,5235,5346,5443,5542,5661,5739,5842,5937"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3044,3162,3280,3399,8420,10827,10921,11033,11172,11286,11434,11515,11613,11706,11804,11918,12037,12137,12269,12398,12533,12705,12832,12948,13067,13191,13285,13377,13494,13623,13720,13821,13932,14062,14199,14306,14607,14834,24663,24906,25092,25470,25548,25628,25725,25827,25923,26018,26102,26213,26310,26409,26528,26606,26709", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "3157,3275,3394,3510,8493,10916,11028,11167,11281,11429,11510,11608,11701,11799,11913,12032,12132,12264,12393,12528,12700,12827,12943,13062,13186,13280,13372,13489,13618,13715,13816,13927,14057,14194,14301,14401,14675,14906,24741,24986,25194,25543,25623,25720,25822,25918,26013,26097,26208,26305,26404,26523,26601,26704,26799"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3964,4063,4165,4265,4363,4470,4576,24991", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4058,4160,4260,4358,4465,4571,4691,25087"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-es-rUS/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "202,269,361,454,522", "endColumns": "66,91,92,67,64", "endOffsets": "268,360,453,521,586"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5139,6381,6477,6574,6646", "endColumns": "70,95,96,71,68", "endOffsets": "5205,6472,6569,6641,6710"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-es-rUS/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "212,258,326,393,497,579,631,780,862,945,1076,1167,1234,1312,1408,1453,1515,1580,1654,1702,1746,1788,1833,1880,1944,2011,2088,2152,2215,2257,2334,2397,2464,2553,2634,2698,2760,2832,2887", "endColumns": "45,67,66,103,81,51,148,81,82,130,90,66,77,95,44,61,64,73,47,43,41,44,46,63,66,76,63,62,41,76,62,66,88,80,63,61,71,54,67", "endOffsets": "257,325,392,496,578,630,779,861,944,1075,1166,1233,1311,1407,1452,1514,1579,1653,1701,1745,1787,1832,1879,1943,2010,2087,2151,2214,2256,2333,2396,2463,2552,2633,2697,2759,2831,2886,2954"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4696,4746,4818,4889,4997,5083,5210,5363,5449,5536,5671,5766,5837,5919,6019,6068,6134,6203,6281,6333,6715,6761,6810,6861,6929,7000,7081,7149,7216,7262,7343,7410,7481,7574,7659,7727,7793,7869,16441", "endColumns": "49,71,70,107,85,55,152,85,86,134,94,70,81,99,48,65,68,77,51,47,45,48,50,67,70,80,67,66,45,80,66,70,92,84,67,65,75,58,71", "endOffsets": "4741,4813,4884,4992,5078,5134,5358,5444,5531,5666,5761,5832,5914,6014,6063,6129,6198,6276,6328,6376,6756,6805,6856,6924,6995,7076,7144,7211,7257,7338,7405,7476,7569,7654,7722,7788,7864,7923,16508"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-es-rUS/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9533", "endColumns": "145", "endOffsets": "9674"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,287,392,499,610,733,845,946,1034,1214,1399,1585,1770,1947,2138,2321,2446,2545,2655,2752,2848,2943,3037,3153,3281,3364,3452,3537,3641,3758,3851,3959,4070,4157", "endColumns": "132,98,104,106,110,122,111,100,87,179,184,185,184,176,190,182,124,98,109,96,95,94,93,115,127,82,87,84,103,116,92,107,110,86,98", "endOffsets": "183,282,387,494,605,728,840,941,1029,1209,1394,1580,1765,1942,2133,2316,2441,2540,2650,2747,2843,2938,3032,3148,3276,3359,3447,3532,3636,3753,3846,3954,4065,4152,4251"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15006,16513,16612,16717,16824,16935,17058,17170,17271,17359,17539,17724,17910,18095,18272,18463,18646,18771,18870,18980,19077,19173,19268,19362,19478,19606,19689,19777,19862,19966,20083,20176,20284,20395,20482", "endColumns": "132,98,104,106,110,122,111,100,87,179,184,185,184,176,190,182,124,98,109,96,95,94,93,115,127,82,87,84,103,116,92,107,110,86,98", "endOffsets": "15134,16607,16712,16819,16930,17053,17165,17266,17354,17534,17719,17905,18090,18267,18458,18641,18766,18865,18975,19072,19168,19263,19357,19473,19601,19684,19772,19857,19961,20078,20171,20279,20390,20477,20576"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-es-rUS/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8498,8606,8772,8904,9012,9173,9304,9427,9679,9850,9959,10129,10262,10439,10617,10687,10749", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "8601,8767,8899,9007,9168,9299,9422,9528,9845,9954,10124,10257,10434,10612,10682,10744,10822"}}]}]}