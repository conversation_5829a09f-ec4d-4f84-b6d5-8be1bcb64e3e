{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-af/values-af.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,23987", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,24063"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-af/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,258,327,385,471,542,592,735,825,905,1051,1143,1212,1297,1398,1450,1517,1584,1658,1709,1748,1792,1832,1880,1945,2012,2068,2134,2198,2237,2307,2370,2439,2519,2599,2660,2717,2791,2835", "endColumns": "49,68,57,85,70,49,142,89,79,145,91,68,84,100,51,66,66,73,50,38,43,39,47,64,66,55,65,63,38,69,62,68,79,79,60,56,73,43,64", "endOffsets": "257,326,384,470,541,591,734,824,904,1050,1142,1211,1296,1397,1449,1516,1583,1657,1708,1747,1791,1831,1879,1944,2011,2067,2133,2197,2236,2306,2369,2438,2518,2598,2659,2716,2790,2834,2899"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4600,4654,4727,4789,4879,4954,5079,5226,5320,5404,5554,5650,5723,5812,5917,5973,6044,6115,6193,6248,6598,6646,6690,6742,6811,6882,6942,7012,7080,7123,7197,7264,7337,7421,7505,7570,7631,7709,15958", "endColumns": "53,72,61,89,74,53,146,93,83,149,95,72,88,104,55,70,70,77,54,42,47,43,51,68,70,59,69,67,42,73,66,72,83,83,64,60,77,47,68", "endOffsets": "4649,4722,4784,4874,4949,5003,5221,5315,5399,5549,5645,5718,5807,5912,5968,6039,6110,6188,6243,6286,6641,6685,6737,6806,6877,6937,7007,7075,7118,7192,7259,7332,7416,7500,7565,7626,7704,7752,16022"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,274,376,482,588,714,828,927,1014,1195,1373,1552,1734,1908,2092,2275,2398,2494,2601,2700,2794,2887,2980,3102,3217,3305,3388,3470,3569,3667,3760,3865,3972,4059", "endColumns": "121,96,101,105,105,125,113,98,86,180,177,178,181,173,183,182,122,95,106,98,93,92,92,121,114,87,82,81,98,97,92,104,106,86,95", "endOffsets": "172,269,371,477,583,709,823,922,1009,1190,1368,1547,1729,1903,2087,2270,2393,2489,2596,2695,2789,2882,2975,3097,3212,3300,3383,3465,3564,3662,3755,3860,3967,4054,4150"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14609,16027,16124,16226,16332,16438,16564,16678,16777,16864,17045,17223,17402,17584,17758,17942,18125,18248,18344,18451,18550,18644,18737,18830,18952,19067,19155,19238,19320,19419,19517,19610,19715,19822,19909", "endColumns": "121,96,101,105,105,125,113,98,86,180,177,178,181,173,183,182,122,95,106,98,93,92,92,121,114,87,82,81,98,97,92,104,106,86,95", "endOffsets": "14726,16119,16221,16327,16433,16559,16673,16772,16859,17040,17218,17397,17579,17753,17937,18120,18243,18339,18446,18545,18639,18732,18825,18947,19062,19150,19233,19315,19414,19512,19605,19710,19817,19904,20000"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-af/values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1055,1143,1209,1272,1358,1420,1481,1539,1605,1668,1723,1841,1898,1960,2015,2084,2203,2291,2374,2513,2596,2677,2805,2892,2969,3027,3078,3144,3213,3289,3375,3451,3525,3604,3677,3748,3851,3938,4009,4098,4188,4260,4335,4422,4473,4552,4619,4700,4784,4846,4910,4973,5043,5147,5250,5346,5446,5508,5563", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "269,350,430,508,603,691,791,905,986,1050,1138,1204,1267,1353,1415,1476,1534,1600,1663,1718,1836,1893,1955,2010,2079,2198,2286,2369,2508,2591,2672,2800,2887,2964,3022,3073,3139,3208,3284,3370,3446,3520,3599,3672,3743,3846,3933,4004,4093,4183,4255,4330,4417,4468,4547,4614,4695,4779,4841,4905,4968,5038,5142,5245,5341,5441,5503,5558,5635"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3446,3527,3607,3685,3780,7757,7857,7971,14381,14521,14731,14964,15027,15113,15175,15236,15294,15360,15423,15478,15596,15653,15715,15770,15839,20005,20093,20176,20315,20398,20479,20607,20694,20771,20829,20880,20946,21015,21091,21177,21253,21327,21406,21479,21550,21653,21740,21811,21900,21990,22062,22137,22224,22275,22354,22421,22502,22586,22648,22712,22775,22845,22949,23052,23148,23248,23310,23670", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "319,3522,3602,3680,3775,3863,7852,7966,8047,14440,14604,14792,15022,15108,15170,15231,15289,15355,15418,15473,15591,15648,15710,15765,15834,15953,20088,20171,20310,20393,20474,20602,20689,20766,20824,20875,20941,21010,21086,21172,21248,21322,21401,21474,21545,21648,21735,21806,21895,21985,22057,22132,22219,22270,22349,22416,22497,22581,22643,22707,22770,22840,22944,23047,23143,23243,23305,23360,23742"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-af/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8311,8418,8574,8700,8810,8960,9082,9203,9448,9614,9722,9879,10006,10145,10299,10365,10428", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "8413,8569,8695,8805,8955,9077,9198,9300,9609,9717,9874,10001,10140,10294,10360,10423,10502"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,282,393,505,581,681,799,936,1060,1210,1291,1386,1472,1571,1682,1799,1899,2023,2144,2272,2434,2555,2673,2793,2919,3006,3101,3213,3335,3431,3536,3635,3767,3903,4005,4098,4171,4247,4328,4412,4513,4590,4669,4764,4858,4949,5043,5127,5226,5322,5420,5532,5609,5705", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "163,277,388,500,576,676,794,931,1055,1205,1286,1381,1467,1566,1677,1794,1894,2018,2139,2267,2429,2550,2668,2788,2914,3001,3096,3208,3330,3426,3531,3630,3762,3898,4000,4093,4166,4242,4323,4407,4508,4585,4664,4759,4853,4944,5038,5122,5221,5317,5415,5527,5604,5700,5792"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2996,3109,3223,3334,8235,10507,10607,10725,10862,10986,11136,11217,11312,11398,11497,11608,11725,11825,11949,12070,12198,12360,12481,12599,12719,12845,12932,13027,13139,13261,13357,13462,13561,13693,13829,13931,14222,14445,23906,24139,24324,24696,24773,24852,24947,25041,25132,25226,25310,25409,25505,25603,25715,25792,25888", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "3104,3218,3329,3441,8306,10602,10720,10857,10981,11131,11212,11307,11393,11492,11603,11720,11820,11944,12065,12193,12355,12476,12594,12714,12840,12927,13022,13134,13256,13352,13457,13556,13688,13824,13926,14019,14290,14516,23982,24218,24420,24768,24847,24942,25036,25127,25221,25305,25404,25500,25598,25710,25787,25883,25975"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8052,8148,14024,14121,14295,14797,14873,23365,23455,23541,23605,23747,23825,24068,24425,24506,24576", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "8143,8230,14116,14217,14376,14868,14959,23450,23536,23600,23665,23820,23901,24134,24501,24571,24691"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-af/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,343,425,493", "endColumns": "66,77,81,67,62", "endOffsets": "264,342,424,492,555"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5008,6291,6373,6459,6531", "endColumns": "70,81,85,71,66", "endOffsets": "5074,6368,6454,6526,6593"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3868,3966,4068,4166,4264,4371,4480,24223", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3961,4063,4161,4259,4366,4475,4595,24319"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-af/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9305", "endColumns": "142", "endOffsets": "9443"}}]}]}