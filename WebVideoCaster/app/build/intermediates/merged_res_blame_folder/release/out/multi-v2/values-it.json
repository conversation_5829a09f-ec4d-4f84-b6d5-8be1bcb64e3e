{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-it/values-it.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-it/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8413,8518,8669,8794,8902,9060,9188,9308,9548,9705,9812,9966,10093,10249,10430,10497,10558", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "8513,8664,8789,8897,9055,9183,9303,9407,9700,9807,9961,10088,10244,10425,10492,10553,10630"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-it/values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3929,4027,4129,4228,4330,4439,4546,24920", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "4022,4124,4223,4325,4434,4541,4671,25016"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-it/values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,187,285,388,495,604,733,855,957,1044,1222,1407,1600,1790,1972,2168,2359,2483,2587,2708,2805,2901,2996,3089,3211,3343,3435,3522,3610,3718,3823,3917,4026,4137,4224", "endColumns": "131,97,102,106,108,128,121,101,86,177,184,192,189,181,195,190,123,103,120,96,95,94,92,121,131,91,86,87,107,104,93,108,110,86,97", "endOffsets": "182,280,383,490,599,728,850,952,1039,1217,1402,1595,1785,1967,2163,2354,2478,2582,2703,2800,2896,2991,3084,3206,3338,3430,3517,3605,3713,3818,3912,4021,4132,4219,4317"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14879,16396,16494,16597,16704,16813,16942,17064,17166,17253,17431,17616,17809,17999,18181,18377,18568,18692,18796,18917,19014,19110,19205,19298,19420,19552,19644,19731,19819,19927,20032,20126,20235,20346,20433", "endColumns": "131,97,102,106,108,128,121,101,86,177,184,192,189,181,195,190,123,103,120,96,95,94,92,121,131,91,86,87,107,104,93,108,110,86,97", "endOffsets": "15006,16489,16592,16699,16808,16937,17059,17161,17248,17426,17611,17804,17994,18176,18372,18563,18687,18791,18912,19009,19105,19200,19293,19415,19547,19639,19726,19814,19922,20027,20121,20230,20341,20428,20526"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-it/values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1092,1187,1272,1334,1422,1484,1553,1616,1689,1752,1806,1927,1984,2046,2100,2177,2314,2399,2481,2616,2697,2778,2924,3015,3105,3160,3211,3277,3350,3430,3521,3601,3676,3753,3822,3899,4004,4092,4181,4274,4367,4441,4521,4615,4666,4750,4816,4900,4988,5050,5114,5177,5245,5360,5474,5580,5689,5748,5803", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,145,90,89,54,50,65,72,79,90,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79", "endOffsets": "263,347,428,505,604,699,798,938,1021,1087,1182,1267,1329,1417,1479,1548,1611,1684,1747,1801,1922,1979,2041,2095,2172,2309,2394,2476,2611,2692,2773,2919,3010,3100,3155,3206,3272,3345,3425,3516,3596,3671,3748,3817,3894,3999,4087,4176,4269,4362,4436,4516,4610,4661,4745,4811,4895,4983,5045,5109,5172,5240,5355,5469,5575,5684,5743,5798,5878"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3493,3577,3658,3735,3834,7816,7915,8055,14633,14784,15011,15281,15343,15431,15493,15562,15625,15698,15761,15815,15936,15993,16055,16109,16186,20531,20616,20698,20833,20914,20995,21141,21232,21322,21377,21428,21494,21567,21647,21738,21818,21893,21970,22039,22116,22221,22309,22398,22491,22584,22658,22738,22832,22883,22967,23033,23117,23205,23267,23331,23394,23462,23577,23691,23797,23906,23965,24337", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,145,90,89,54,50,65,72,79,90,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79", "endOffsets": "313,3572,3653,3730,3829,3924,7910,8050,8133,14694,14874,15091,15338,15426,15488,15557,15620,15693,15756,15810,15931,15988,16050,16104,16181,16318,20611,20693,20828,20909,20990,21136,21227,21317,21372,21423,21489,21562,21642,21733,21813,21888,21965,22034,22111,22216,22304,22393,22486,22579,22653,22733,22827,22878,22962,23028,23112,23200,23262,23326,23389,23457,23572,23686,23792,23901,23960,24015,24412"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-it/values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,24671", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,24748"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-it/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,341,421,489", "endColumns": "66,75,79,67,64", "endOffsets": "264,340,420,488,553"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5100,6324,6404,6488,6560", "endColumns": "70,79,83,71,68", "endOffsets": "5166,6399,6483,6555,6624"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-it/values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1015,1079,1163,1251,1336,1414,1483", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1010,1074,1158,1246,1331,1409,1478,1599"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8138,8238,14263,14361,14546,15096,15175,24020,24113,24208,24273,24417,24501,24753,25120,25198,25267", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "8233,8320,14356,14456,14628,15170,15276,24108,24203,24268,24332,24496,24584,24833,25193,25262,25383"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-it/values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,298,416,536,624,716,826,965,1080,1234,1314,1412,1502,1598,1713,1831,1944,2082,2218,2357,2529,2660,2776,2896,3023,3114,3207,3330,3464,3560,3666,3768,3906,4050,4156,4252,4337,4422,4504,4586,4685,4761,4840,4935,5034,5121,5215,5299,5405,5501,5599,5713,5789,5899", "endColumns": "122,119,117,119,87,91,109,138,114,153,79,97,89,95,114,117,112,137,135,138,171,130,115,119,126,90,92,122,133,95,105,101,137,143,105,95,84,84,81,81,98,75,78,94,98,86,93,83,105,95,97,113,75,109,102", "endOffsets": "173,293,411,531,619,711,821,960,1075,1229,1309,1407,1497,1593,1708,1826,1939,2077,2213,2352,2524,2655,2771,2891,3018,3109,3202,3325,3459,3555,3661,3763,3901,4045,4151,4247,4332,4417,4499,4581,4680,4756,4835,4930,5029,5116,5210,5294,5400,5496,5594,5708,5784,5894,5997"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,3135,3255,3373,8325,10635,10727,10837,10976,11091,11245,11325,11423,11513,11609,11724,11842,11955,12093,12229,12368,12540,12671,12787,12907,13034,13125,13218,13341,13475,13571,13677,13779,13917,14061,14167,14461,14699,24589,24838,25021,25388,25464,25543,25638,25737,25824,25918,26002,26108,26204,26302,26416,26492,26602", "endColumns": "122,119,117,119,87,91,109,138,114,153,79,97,89,95,114,117,112,137,135,138,171,130,115,119,126,90,92,122,133,95,105,101,137,143,105,95,84,84,81,81,98,75,78,94,98,86,93,83,105,95,97,113,75,109,102", "endOffsets": "3130,3250,3368,3488,8408,10722,10832,10971,11086,11240,11320,11418,11508,11604,11719,11837,11950,12088,12224,12363,12535,12666,12782,12902,13029,13120,13213,13336,13470,13566,13672,13774,13912,14056,14162,14258,14541,14779,24666,24915,25115,25459,25538,25633,25732,25819,25913,25997,26103,26199,26297,26411,26487,26597,26700"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-it/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9412", "endColumns": "135", "endOffsets": "9543"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-it/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,322,390,486,556,608,744,827,903,1029,1112,1182,1260,1357,1401,1465,1530,1604,1655,1705,1746,1790,1835,1900,1962,2023,2091,2159,2204,2273,2336,2402,2492,2575,2639,2696,2769,2820", "endColumns": "46,66,67,95,69,51,135,82,75,125,82,69,77,96,43,63,64,73,50,49,40,43,44,64,61,60,67,67,44,68,62,65,89,82,63,56,72,50,68", "endOffsets": "254,321,389,485,555,607,743,826,902,1028,1111,1181,1259,1356,1400,1464,1529,1603,1654,1704,1745,1789,1834,1899,1961,2022,2090,2158,2203,2272,2335,2401,2491,2574,2638,2695,2768,2819,2888"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4676,4727,4798,4870,4970,5044,5171,5311,5398,5478,5608,5695,5769,5851,5952,6000,6068,6137,6215,6270,6629,6674,6722,6771,6840,6906,6971,7043,7115,7164,7237,7304,7374,7468,7555,7623,7684,7761,16323", "endColumns": "50,70,71,99,73,55,139,86,79,129,86,73,81,100,47,67,68,77,54,53,44,47,48,68,65,64,71,71,48,72,66,69,93,86,67,60,76,54,72", "endOffsets": "4722,4793,4865,4965,5039,5095,5306,5393,5473,5603,5690,5764,5846,5947,5995,6063,6132,6210,6265,6319,6669,6717,6766,6835,6901,6966,7038,7110,7159,7232,7299,7369,7463,7550,7618,7679,7756,7811,16391"}}]}]}