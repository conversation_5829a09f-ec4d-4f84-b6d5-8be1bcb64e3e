{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7176,7252,12244,12332,12492,12945,13019,20402,20480,20554,20617,20749,20822,21054,21384,21459,21524", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "7247,7321,12327,12418,12565,13014,13091,20475,20549,20612,20675,20817,20892,21116,21454,21519,21635"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,609,711,812,913,1025,1103,1195,1275,1359,1460,1569,1666,1770,1872,1976,2088,2189,2287,2389,2492,2573,2664,2765,2870,2956,3054,3148,3252,3362,3458,3544,3613,3684,3762,3839,3924,4000,4078,4171,4261,4350,4439,4519,4611,4703,4793,4897,4973,5061", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "150,249,349,447,519,604,706,807,908,1020,1098,1190,1270,1354,1455,1564,1661,1765,1867,1971,2083,2184,2282,2384,2487,2568,2659,2760,2865,2951,3049,3143,3247,3357,3453,3539,3608,3679,3757,3834,3919,3995,4073,4166,4256,4345,4434,4514,4606,4698,4788,4892,4968,5056,5142"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2858,2958,3057,3157,7326,9224,9309,9411,9512,9613,9725,9803,9895,9975,10059,10160,10269,10366,10470,10572,10676,10788,10889,10987,11089,11192,11273,11364,11465,11570,11656,11754,11848,11952,12062,12158,12423,12632,20897,21121,21299,21640,21716,21794,21887,21977,22066,22155,22235,22327,22419,22509,22613,22689,22777", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "2953,3052,3152,3250,7393,9304,9406,9507,9608,9720,9798,9890,9970,10054,10155,10264,10361,10465,10567,10671,10783,10884,10982,11084,11187,11268,11359,11460,11565,11651,11749,11843,11947,12057,12153,12239,12487,12698,20970,21193,21379,21711,21789,21882,21972,22061,22150,22230,22322,22414,22504,22608,22684,22772,22858"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7398,7499,7627,7742,7844,7951,8067,8169,8370,8480,8581,8710,8825,8932,9040,9095,9152", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "7494,7622,7737,7839,7946,8062,8164,8257,8475,8576,8705,8820,8927,9035,9090,9147,9219"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "202,269,340,413,481", "endColumns": "66,70,72,67,57", "endOffsets": "268,339,412,480,538"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4601,5618,5693,5770,5842", "endColumns": "70,74,76,71,61", "endOffsets": "4667,5688,5765,5837,5899"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,251,341,433,528,633,730,816,895,1064,1188,1314,1443,1566,1694,1820,1921,2008,2102,2194,2284,2372,2460,2558,2656,2736,2815,2894,2983,3074,3164,3254,3351,3433", "endColumns": "104,90,89,91,94,104,96,85,78,168,123,125,128,122,127,125,100,86,93,91,89,87,87,97,97,79,78,78,88,90,89,89,96,81,88", "endOffsets": "155,246,336,428,523,628,725,811,890,1059,1183,1309,1438,1561,1689,1815,1916,2003,2097,2189,2279,2367,2455,2553,2651,2731,2810,2889,2978,3069,3159,3249,3346,3428,3517"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12781,14052,14143,14233,14325,14420,14525,14622,14708,14787,14956,15080,15206,15335,15458,15586,15712,15813,15900,15994,16086,16176,16264,16352,16450,16548,16628,16707,16786,16875,16966,17056,17146,17243,17325", "endColumns": "104,90,89,91,94,104,96,85,78,168,123,125,128,122,127,125,100,86,93,91,89,87,87,97,97,79,78,78,88,90,89,89,96,81,88", "endOffsets": "12881,14138,14228,14320,14415,14520,14617,14703,14782,14951,15075,15201,15330,15453,15581,15707,15808,15895,15989,16081,16171,16259,16347,16445,16543,16623,16702,16781,16870,16961,17051,17141,17238,17320,17409"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "212,253,311,362,429,491,536,622,694,765,850,926,989,1057,1136,1176,1224,1272,1346,1389,1426,1464,1501,1540,1587,1634,1679,1726,1773,1810,1863,1923,1984,2057,2135,2194,2251,2315,2356", "endColumns": "40,57,50,66,61,44,85,71,70,84,75,62,67,78,39,47,47,73,42,36,37,36,38,46,46,44,46,46,36,52,59,60,72,77,58,56,63,40,58", "endOffsets": "252,310,361,428,490,535,621,693,764,849,925,988,1056,1135,1175,1223,1271,1345,1388,1425,1463,1500,1539,1586,1633,1678,1725,1772,1809,1862,1922,1983,2056,2134,2193,2250,2314,2355,2414"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4253,4298,4360,4415,4486,4552,4672,4762,4838,4913,5002,5082,5149,5221,5304,5348,5400,5452,5530,5577,5904,5946,5987,6030,6081,6132,6181,6232,6283,6324,6381,6445,6510,6587,6669,6732,6793,6861,13989", "endColumns": "44,61,54,70,65,48,89,75,74,88,79,66,71,82,43,51,51,77,46,40,41,40,42,50,50,48,50,50,40,56,63,64,76,81,62,60,67,44,62", "endOffsets": "4293,4355,4410,4481,4547,4596,4757,4833,4908,4997,5077,5144,5216,5299,5343,5395,5447,5525,5572,5613,5941,5982,6025,6076,6127,6176,6227,6278,6319,6376,6440,6505,6582,6664,6727,6788,6856,6901,14047"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "8262", "endColumns": "107", "endOffsets": "8365"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3592,3684,3783,3877,3971,4064,4157,21198", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3679,3778,3872,3966,4059,4152,4248,21294"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,20975", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,21049"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2443,2520,2594,2644,2695,2761,2825,2893,2968,3040,3101,3172,3239,3299,3387,3467,3530,3613,3698,3772,3837,3913,3961,4035,4099,4175,4253,4315,4379,4442,4508,4588,4668,4744,4825,4879,4934", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2438,2515,2589,2639,2690,2756,2820,2888,2963,3035,3096,3167,3234,3294,3382,3462,3525,3608,3693,3767,3832,3908,3956,4030,4094,4170,4248,4310,4374,4437,4503,4583,4663,4739,4820,4874,4929,4998"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3255,3318,3379,3446,3515,6906,6996,7103,12570,12703,12886,13096,13154,13232,13293,13350,13406,13465,13523,13577,13663,13719,13777,13831,13896,17414,17488,17566,17686,17749,17812,17911,17988,18062,18112,18163,18229,18293,18361,18436,18508,18569,18640,18707,18767,18855,18935,18998,19081,19166,19240,19305,19381,19429,19503,19567,19643,19721,19783,19847,19910,19976,20056,20136,20212,20293,20347,20680", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "292,3313,3374,3441,3510,3587,6991,7098,7171,12627,12776,12940,13149,13227,13288,13345,13401,13460,13518,13572,13658,13714,13772,13826,13891,13984,17483,17561,17681,17744,17807,17906,17983,18057,18107,18158,18224,18288,18356,18431,18503,18564,18635,18702,18762,18850,18930,18993,19076,19161,19235,19300,19376,19424,19498,19562,19638,19716,19778,19842,19905,19971,20051,20131,20207,20288,20342,20397,20744"}}]}]}