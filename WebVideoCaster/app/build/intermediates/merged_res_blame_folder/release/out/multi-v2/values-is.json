{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-is/values-is.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-is/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8184,8290,8444,8569,8678,8819,8944,9053,9287,9441,9547,9704,9830,9972,10126,10190,10253", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "8285,8439,8564,8673,8814,8939,9048,9153,9436,9542,9699,9825,9967,10121,10185,10248,10327"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-is/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9158", "endColumns": "128", "endOffsets": "9282"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,23663", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,23739"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,279,379,482,587,708,815,911,997,1167,1325,1485,1653,1814,1976,2134,2245,2339,2442,2537,2636,2728,2820,2934,3042,3128,3211,3295,3397,3500,3593,3692,3792,3877", "endColumns": "126,96,99,102,104,120,106,95,85,169,157,159,167,160,161,157,110,93,102,94,98,91,91,113,107,85,82,83,101,102,92,98,99,84,90", "endOffsets": "177,274,374,477,582,703,810,906,992,1162,1320,1480,1648,1809,1971,2129,2240,2334,2437,2532,2631,2723,2815,2929,3037,3123,3206,3290,3392,3495,3588,3687,3787,3872,3963"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14411,15878,15975,16075,16178,16283,16404,16511,16607,16693,16863,17021,17181,17349,17510,17672,17830,17941,18035,18138,18233,18332,18424,18516,18630,18738,18824,18907,18991,19093,19196,19289,19388,19488,19573", "endColumns": "126,96,99,102,104,120,106,95,85,169,157,159,167,160,161,157,110,93,102,94,98,91,91,113,107,85,82,83,101,102,92,98,99,84,90", "endOffsets": "14533,15970,16070,16173,16278,16399,16506,16602,16688,16858,17016,17176,17344,17505,17667,17825,17936,18030,18133,18228,18327,18419,18511,18625,18733,18819,18902,18986,19088,19191,19284,19383,19483,19568,19659"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-is/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,256,320,376,460,527,578,714,797,875,1000,1089,1154,1235,1339,1387,1449,1515,1589,1638,1689,1733,1773,1824,1891,1962,2022,2089,2156,2197,2269,2332,2400,2478,2557,2619,2680,2752,2803", "endColumns": "47,63,55,83,66,50,135,82,77,124,88,64,80,103,47,61,65,73,48,50,43,39,50,66,70,59,66,66,40,71,62,67,77,78,61,60,71,50,64", "endOffsets": "255,319,375,459,526,577,713,796,874,999,1088,1153,1234,1338,1386,1448,1514,1588,1637,1688,1732,1772,1823,1890,1961,2021,2088,2155,2196,2268,2331,2399,2477,2556,2618,2679,2751,2802,2867"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4527,4579,4647,4707,4795,4866,4992,5132,5219,5301,5430,5523,5592,5677,5785,5837,5903,5973,6051,6104,6456,6504,6548,6603,6674,6749,6813,6884,6955,7000,7076,7143,7215,7297,7380,7446,7511,7587,15809", "endColumns": "51,67,59,87,70,54,139,86,81,128,92,68,84,107,51,65,69,77,52,54,47,43,54,70,74,63,70,70,44,75,66,71,81,82,65,64,75,54,68", "endOffsets": "4574,4642,4702,4790,4861,4916,5127,5214,5296,5425,5518,5587,5672,5780,5832,5898,5968,6046,6099,6154,6499,6543,6598,6669,6744,6808,6879,6950,6995,7071,7138,7210,7292,7375,7441,7506,7582,7637,15873"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,376,484,560,655,768,905,1028,1159,1245,1342,1435,1531,1642,1764,1866,1987,2107,2245,2412,2531,2643,2759,2878,2972,3066,3171,3289,3393,3497,3596,3721,3852,3956,4056,4128,4203,4284,4365,4470,4546,4633,4730,4827,4918,5022,5106,5207,5304,5405,5521,5597,5695", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "160,268,371,479,555,650,763,900,1023,1154,1240,1337,1430,1526,1637,1759,1861,1982,2102,2240,2407,2526,2638,2754,2873,2967,3061,3166,3284,3388,3492,3591,3716,3847,3951,4051,4123,4198,4279,4360,4465,4541,4628,4725,4822,4913,5017,5101,5202,5299,5400,5516,5592,5690,5782"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2994,3104,3212,3315,8108,10332,10427,10540,10677,10800,10931,11017,11114,11207,11303,11414,11536,11638,11759,11879,12017,12184,12303,12415,12531,12650,12744,12838,12943,13061,13165,13269,13368,13493,13624,13728,14026,14248,23582,23814,23996,24366,24442,24529,24626,24723,24814,24918,25002,25103,25200,25301,25417,25493,25591", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "3099,3207,3310,3418,8179,10422,10535,10672,10795,10926,11012,11109,11202,11298,11409,11531,11633,11754,11874,12012,12179,12298,12410,12526,12645,12739,12833,12938,13056,13160,13264,13363,13488,13619,13723,13823,14093,14318,23658,23890,24096,24437,24524,24621,24718,24809,24913,24997,25098,25195,25296,25412,25488,25586,25678"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-is/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,340,414,482", "endColumns": "66,74,73,67,63", "endOffsets": "264,339,413,481,545"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4921,6159,6238,6316,6388", "endColumns": "70,78,77,71,67", "endOffsets": "4987,6233,6311,6383,6451"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,974,1042,1120,1203,1273,1350,1418", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,969,1037,1115,1198,1268,1345,1413,1533"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7936,8027,13828,13927,14098,14602,14682,23034,23123,23205,23273,23421,23499,23744,24101,24178,24246", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "8022,8103,13922,14021,14178,14677,14772,23118,23200,23268,23336,23494,23577,23809,24173,24241,24361"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-is/values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1016,1104,1168,1229,1319,1383,1446,1508,1576,1640,1696,1819,1884,1946,2002,2073,2200,2284,2368,2504,2581,2658,2774,2861,2940,2997,3052,3118,3194,3274,3363,3439,3506,3580,3650,3716,3818,3904,3974,4065,4155,4229,4302,4391,4442,4523,4595,4676,4762,4824,4888,4951,5020,5134,5240,5348,5450,5511,5570", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "265,339,411,490,572,652,749,864,946,1011,1099,1163,1224,1314,1378,1441,1503,1571,1635,1691,1814,1879,1941,1997,2068,2195,2279,2363,2499,2576,2653,2769,2856,2935,2992,3047,3113,3189,3269,3358,3434,3501,3575,3645,3711,3813,3899,3969,4060,4150,4224,4297,4386,4437,4518,4590,4671,4757,4819,4883,4946,5015,5129,5235,5343,5445,5506,5565,5645"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3423,3497,3569,3648,3730,7642,7739,7854,14183,14323,14538,14777,14838,14928,14992,15055,15117,15185,15249,15305,15428,15493,15555,15611,15682,19664,19748,19832,19968,20045,20122,20238,20325,20404,20461,20516,20582,20658,20738,20827,20903,20970,21044,21114,21180,21282,21368,21438,21529,21619,21693,21766,21855,21906,21987,22059,22140,22226,22288,22352,22415,22484,22598,22704,22812,22914,22975,23341", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "315,3492,3564,3643,3725,3805,7734,7849,7931,14243,14406,14597,14833,14923,14987,15050,15112,15180,15244,15300,15423,15488,15550,15606,15677,15804,19743,19827,19963,20040,20117,20233,20320,20399,20456,20511,20577,20653,20733,20822,20898,20965,21039,21109,21175,21277,21363,21433,21524,21614,21688,21761,21850,21901,21982,22054,22135,22221,22283,22347,22410,22479,22593,22699,22807,22909,22970,23029,23416"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3810,3905,4012,4109,4209,4312,4416,23895", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3900,4007,4104,4204,4307,4411,4522,23991"}}]}]}