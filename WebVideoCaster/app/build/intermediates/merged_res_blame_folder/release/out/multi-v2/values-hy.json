{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-hy/values-hy.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3882,3982,4087,4185,4284,4389,4491,24583", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3977,4082,4180,4279,4384,4486,4597,24679"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-hy/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8308,8414,8579,8713,8821,8975,9111,9238,9502,9669,9777,9945,10081,10243,10409,10474,10541", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "8409,8574,8708,8816,8970,9106,9233,9346,9664,9772,9940,10076,10238,10404,10469,10536,10618"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-hy/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9351", "endColumns": "150", "endOffsets": "9497"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-hy/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,324,386,475,543,591,728,816,895,1016,1105,1171,1253,1352,1401,1466,1535,1609,1660,1708,1754,1798,1845,1908,1975,2042,2104,2166,2211,2287,2351,2418,2494,2578,2642,2705,2775,2825", "endColumns": "44,70,61,88,67,47,136,87,78,120,88,65,81,98,48,64,68,73,50,47,45,43,46,62,66,66,61,61,44,75,63,66,75,83,63,62,69,49,60", "endOffsets": "252,323,385,474,542,590,727,815,894,1015,1104,1170,1252,1351,1400,1465,1534,1608,1659,1707,1753,1797,1844,1907,1974,2041,2103,2165,2210,2286,2350,2417,2493,2577,2641,2704,2774,2824,2885"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4602,4651,4726,4792,4885,4957,5080,5221,5313,5396,5521,5614,5684,5770,5873,5926,5995,6068,6146,6201,6555,6605,6653,6704,6771,6842,6913,6979,7045,7094,7174,7242,7313,7393,7481,7549,7616,7690,16237", "endColumns": "48,74,65,92,71,51,140,91,82,124,92,69,85,102,52,68,72,77,54,51,49,47,50,66,70,70,65,65,48,79,67,70,79,87,67,66,73,53,64", "endOffsets": "4646,4721,4787,4880,4952,5004,5216,5308,5391,5516,5609,5679,5765,5868,5921,5990,6063,6141,6196,6248,6600,6648,6699,6766,6837,6908,6974,7040,7089,7169,7237,7308,7388,7476,7544,7611,7685,7739,16297"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,24345", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,24423"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1047,1144,1229,1291,1378,1440,1504,1565,1632,1693,1747,1869,1926,1986,2040,2121,2256,2340,2425,2561,2636,2711,2854,2949,3029,3085,3138,3204,3278,3357,3443,3526,3597,3673,3749,3826,3932,4020,4100,4196,4292,4366,4444,4544,4595,4679,4748,4835,4926,4988,5052,5115,5186,5291,5397,5497,5600,5660,5717", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "260,336,412,492,584,672,767,897,978,1042,1139,1224,1286,1373,1435,1499,1560,1627,1688,1742,1864,1921,1981,2035,2116,2251,2335,2420,2556,2631,2706,2849,2944,3024,3080,3133,3199,3273,3352,3438,3521,3592,3668,3744,3821,3927,4015,4095,4191,4287,4361,4439,4539,4590,4674,4743,4830,4921,4983,5047,5110,5181,5286,5392,5492,5595,5655,5712,5797"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3470,3546,3622,3702,3794,7744,7839,7969,14602,14744,14957,15210,15272,15359,15421,15485,15546,15613,15674,15728,15850,15907,15967,16021,16102,20222,20306,20391,20527,20602,20677,20820,20915,20995,21051,21104,21170,21244,21323,21409,21492,21563,21639,21715,21792,21898,21986,22066,22162,22258,22332,22410,22510,22561,22645,22714,22801,22892,22954,23018,23081,23152,23257,23363,23463,23566,23626,24003", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "310,3541,3617,3697,3789,3877,7834,7964,8045,14661,14836,15037,15267,15354,15416,15480,15541,15608,15669,15723,15845,15902,15962,16016,16097,16232,20301,20386,20522,20597,20672,20815,20910,20990,21046,21099,21165,21239,21318,21404,21487,21558,21634,21710,21787,21893,21981,22061,22157,22253,22327,22405,22505,22556,22640,22709,22796,22887,22949,23013,23076,23147,23252,23358,23458,23561,23621,23678,24083"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,389,501,578,676,792,934,1053,1205,1288,1398,1489,1584,1702,1820,1923,2059,2193,2322,2495,2620,2732,2848,2968,3060,3154,3273,3410,3512,3613,3717,3851,3991,4096,4193,4280,4358,4442,4523,4633,4709,4788,4883,4980,5067,5159,5241,5341,5435,5530,5643,5719,5820", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "162,273,384,496,573,671,787,929,1048,1200,1283,1393,1484,1579,1697,1815,1918,2054,2188,2317,2490,2615,2727,2843,2963,3055,3149,3268,3405,3507,3608,3712,3846,3986,4091,4188,4275,4353,4437,4518,4628,4704,4783,4878,4975,5062,5154,5236,5336,5430,5525,5638,5714,5815,5905"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3136,3247,3358,8231,10623,10721,10837,10979,11098,11250,11333,11443,11534,11629,11747,11865,11968,12104,12238,12367,12540,12665,12777,12893,13013,13105,13199,13318,13455,13557,13658,13762,13896,14036,14141,14432,14666,24261,24502,24684,25062,25138,25217,25312,25409,25496,25588,25670,25770,25864,25959,26072,26148,26249", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "3131,3242,3353,3465,8303,10716,10832,10974,11093,11245,11328,11438,11529,11624,11742,11860,11963,12099,12233,12362,12535,12660,12772,12888,13008,13100,13194,13313,13450,13552,13653,13757,13891,14031,14136,14233,14514,14739,24340,24578,24789,25133,25212,25307,25404,25491,25583,25665,25765,25859,25954,26067,26143,26244,26334"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,980,1051,1136,1224,1298,1379,1448", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,975,1046,1131,1219,1293,1374,1443,1561"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8050,8149,14238,14332,14519,15042,15124,23683,23778,23860,23932,24088,24173,24428,24794,24875,24944", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "8144,8226,14327,14427,14597,15119,15205,23773,23855,23927,23998,24168,24256,24497,24870,24939,25057"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-hy/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,343,422,490", "endColumns": "66,77,78,67,60", "endOffsets": "264,342,421,489,550"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5009,6253,6335,6418,6490", "endColumns": "70,81,82,71,64", "endOffsets": "5075,6330,6413,6485,6550"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,270,374,480,588,714,820,915,1002,1180,1348,1519,1691,1865,2035,2208,2322,2417,2523,2619,2712,2803,2896,3006,3118,3206,3293,3381,3489,3604,3697,3797,3910,3998", "endColumns": "115,98,103,105,107,125,105,94,86,177,167,170,171,173,169,172,113,94,105,95,92,90,92,109,111,87,86,87,107,114,92,99,112,87,92", "endOffsets": "166,265,369,475,583,709,815,910,997,1175,1343,1514,1686,1860,2030,2203,2317,2412,2518,2614,2707,2798,2891,3001,3113,3201,3288,3376,3484,3599,3692,3792,3905,3993,4086"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14841,16302,16401,16505,16611,16719,16845,16951,17046,17133,17311,17479,17650,17822,17996,18166,18339,18453,18548,18654,18750,18843,18934,19027,19137,19249,19337,19424,19512,19620,19735,19828,19928,20041,20129", "endColumns": "115,98,103,105,107,125,105,94,86,177,167,170,171,173,169,172,113,94,105,95,92,90,92,109,111,87,86,87,107,114,92,99,112,87,92", "endOffsets": "14952,16396,16500,16606,16714,16840,16946,17041,17128,17306,17474,17645,17817,17991,18161,18334,18448,18543,18649,18745,18838,18929,19022,19132,19244,19332,19419,19507,19615,19730,19823,19923,20036,20124,20217"}}]}]}