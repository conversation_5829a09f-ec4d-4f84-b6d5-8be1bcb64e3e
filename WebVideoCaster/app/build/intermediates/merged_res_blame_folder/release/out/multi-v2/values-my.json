{"logs": [{"outputFile": "com.webvideocaster.app-mergeReleaseResources-58:/values-my/values-my.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-my/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,353,437,514", "endColumns": "66,87,83,76,71", "endOffsets": "264,352,436,513,585"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5199,6445,6537,6625,6706", "endColumns": "70,91,87,80,75", "endOffsets": "5265,6532,6620,6701,6777"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-my/values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1121,1211,1286,1345,1436,1499,1564,1623,1694,1756,1813,1932,1990,2051,2106,2179,2311,2402,2491,2632,2710,2787,2910,3002,3079,3137,3188,3254,3326,3408,3490,3568,3643,3717,3789,3868,3976,4073,4154,4240,4332,4406,4485,4571,4625,4701,4769,4852,4933,4995,5059,5122,5190,5302,5413,5517,5630,5691,5746", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "280,381,480,556,647,731,837,966,1051,1116,1206,1281,1340,1431,1494,1559,1618,1689,1751,1808,1927,1985,2046,2101,2174,2306,2397,2486,2627,2705,2782,2905,2997,3074,3132,3183,3249,3321,3403,3485,3563,3638,3712,3784,3863,3971,4068,4149,4235,4327,4401,4480,4566,4620,4696,4764,4847,4928,4990,5054,5117,5185,5297,5408,5512,5625,5686,5741,5823"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3596,3697,3796,3872,3963,8014,8120,8249,14872,15015,15227,15485,15544,15635,15698,15763,15822,15893,15955,16012,16131,16189,16250,16305,16378,20524,20615,20704,20845,20923,21000,21123,21215,21292,21350,21401,21467,21539,21621,21703,21781,21856,21930,22002,22081,22189,22286,22367,22453,22545,22619,22698,22784,22838,22914,22982,23065,23146,23208,23272,23335,23403,23515,23626,23730,23843,23904,24263", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "330,3692,3791,3867,3958,4042,8115,8244,8329,14932,15100,15297,15539,15630,15693,15758,15817,15888,15950,16007,16126,16184,16245,16300,16373,16505,20610,20699,20840,20918,20995,21118,21210,21287,21345,21396,21462,21534,21616,21698,21776,21851,21925,21997,22076,22184,22281,22362,22448,22540,22614,22693,22779,22833,22909,22977,23060,23141,23203,23267,23330,23398,23510,23621,23725,23838,23899,23954,24340"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,270,374,478,585,704,816,915,1011,1190,1360,1534,1711,1881,2055,2228,2339,2433,2547,2645,2744,2848,2941,3053,3161,3247,3332,3416,3512,3631,3724,3830,3941,4025", "endColumns": "121,92,103,103,106,118,111,98,95,178,169,173,176,169,173,172,110,93,113,97,98,103,92,111,107,85,84,83,95,118,92,105,110,83,93", "endOffsets": "172,265,369,473,580,699,811,910,1006,1185,1355,1529,1706,1876,2050,2223,2334,2428,2542,2640,2739,2843,2936,3048,3156,3242,3327,3411,3507,3626,3719,3825,3936,4020,4114"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15105,16582,16675,16779,16883,16990,17109,17221,17320,17416,17595,17765,17939,18116,18286,18460,18633,18744,18838,18952,19050,19149,19253,19346,19458,19566,19652,19737,19821,19917,20036,20129,20235,20346,20430", "endColumns": "121,92,103,103,106,118,111,98,95,178,169,173,176,169,173,172,110,93,113,97,98,103,92,111,107,85,84,83,95,118,92,105,110,83,93", "endOffsets": "15222,16670,16774,16878,16985,17104,17216,17315,17411,17590,17760,17934,18111,18281,18455,18628,18739,18833,18947,19045,19144,19248,19341,19453,19561,19647,19732,19816,19912,20031,20124,20230,20341,20425,20519"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,24604", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,24685"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,998,1065,1152,1238,1313,1394,1460", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,993,1060,1147,1233,1308,1389,1455,1581"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8334,8428,14504,14608,14789,15302,15386,23959,24048,24130,24196,24345,24432,24690,25060,25141,25207", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "8423,8511,14603,14707,14867,15381,15480,24043,24125,24191,24258,24427,24513,24760,25136,25202,25328"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4047,4150,4254,4357,4459,4564,4670,24847", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "4145,4249,4352,4454,4559,4665,4784,24943"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-my/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,249,321,381,459,536,594,712,794,872,996,1091,1160,1244,1343,1395,1462,1533,1607,1669,1713,1756,1798,1850,1921,1996,2078,2140,2211,2251,2332,2393,2461,2543,2626,2689,2746,2822,2873", "endColumns": "40,71,59,77,76,57,117,81,77,123,94,68,83,98,51,66,70,73,61,43,42,41,51,70,74,81,61,70,39,80,60,67,81,82,62,56,75,50,67", "endOffsets": "248,320,380,458,535,593,711,793,871,995,1090,1159,1243,1342,1394,1461,1532,1606,1668,1712,1755,1797,1849,1920,1995,2077,2139,2210,2250,2331,2392,2460,2542,2625,2688,2745,2821,2872,2940"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4789,4834,4910,4974,5056,5137,5270,5392,5478,5560,5688,5787,5860,5948,6051,6107,6178,6253,6331,6397,6782,6829,6875,6931,7006,7085,7171,7237,7312,7356,7441,7506,7578,7664,7751,7818,7879,7959,16510", "endColumns": "44,75,63,81,80,61,121,85,81,127,98,72,87,102,55,70,74,77,65,47,46,45,55,74,78,85,65,74,43,84,64,71,85,86,66,60,79,54,71", "endOffsets": "4829,4905,4969,5051,5132,5194,5387,5473,5555,5683,5782,5855,5943,6046,6102,6173,6248,6326,6392,6440,6824,6870,6926,7001,7080,7166,7232,7307,7351,7436,7501,7573,7659,7746,7813,7874,7954,8009,16577"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-my/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8596,8703,8867,9001,9112,9259,9391,9514,9778,9954,10060,10230,10373,10531,10718,10788,10861", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "8698,8862,8996,9107,9254,9386,9509,9619,9949,10055,10225,10368,10526,10713,10783,10856,10945"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-my/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9624", "endColumns": "153", "endOffsets": "9773"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,318,425,555,635,731,845,990,1110,1269,1351,1446,1535,1633,1749,1872,1972,2094,2221,2361,2525,2643,2756,2872,2996,3086,3179,3306,3439,3537,3645,3746,3867,3992,4091,4189,4266,4344,4430,4512,4624,4700,4780,4876,4974,5066,5160,5243,5344,5439,5535,5652,5728,5847", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "183,313,420,550,630,726,840,985,1105,1264,1346,1441,1530,1628,1744,1867,1967,2089,2216,2356,2520,2638,2751,2867,2991,3081,3174,3301,3434,3532,3640,3741,3862,3987,4086,4184,4261,4339,4425,4507,4619,4695,4775,4871,4969,5061,5155,5238,5339,5434,5530,5647,5723,5842,5956"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3096,3229,3359,3466,8516,10950,11046,11160,11305,11425,11584,11666,11761,11850,11948,12064,12187,12287,12409,12536,12676,12840,12958,13071,13187,13311,13401,13494,13621,13754,13852,13960,14061,14182,14307,14406,14712,14937,24518,24765,24948,25333,25409,25489,25585,25683,25775,25869,25952,26053,26148,26244,26361,26437,26556", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "3224,3354,3461,3591,8591,11041,11155,11300,11420,11579,11661,11756,11845,11943,12059,12182,12282,12404,12531,12671,12835,12953,13066,13182,13306,13396,13489,13616,13749,13847,13955,14056,14177,14302,14401,14499,14784,15010,24599,24842,25055,25404,25484,25580,25678,25770,25864,25947,26048,26143,26239,26356,26432,26551,26665"}}]}]}