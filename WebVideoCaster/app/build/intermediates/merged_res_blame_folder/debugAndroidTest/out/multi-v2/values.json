{"logs": [{"outputFile": "com.webvideocaster.test.app-mergeDebugAndroidTestResources-32:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/8a49a4e061d95551802fa45cfd8e969f/transformed/jetified-activity-1.8.2/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "75,93", "startColumns": "4,4", "startOffsets": "4706,5650", "endColumns": "41,59", "endOffsets": "4743,5705"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b1c55e20baaf2b63e5935b6f6e461b1c/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "95", "startColumns": "4", "startOffsets": "5764", "endColumns": "49", "endOffsets": "5809"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/3ad886af9aca11b1fdd13311c6214d8e/transformed/jetified-core-1.5.0/res/values/values.xml", "from": {"startLines": "4,11", "startColumns": "0,0", "startOffsets": "176,544", "endLines": "10,17", "endColumns": "8,8", "endOffsets": "543,909"}, "to": {"startLines": "145,152", "startColumns": "4,4", "startOffsets": "8743,9115", "endLines": "151,158", "endColumns": "8,8", "endOffsets": "9110,9480"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,121,122,123,124,127,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2312,2371,2430,2490,2550,2610,2670,2730,2790,2850,2910,2970,3030,3089,3149,3209,3269,3329,3389,3449,3509,3569,3629,3689,3748,3808,3868,3927,3986,4045,4104,4163,4222,4296,4354,4409,4460,5814,6531,6596,6650,6716,6817,6875,6927,6987,7049,7103,7139,7173,7223,7277,7394,7441,7477,7567,7679,7790", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,121,122,123,126,129,133", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2366,2425,2485,2545,2605,2665,2725,2785,2845,2905,2965,3025,3084,3144,3204,3264,3324,3384,3444,3504,3564,3624,3684,3743,3803,3863,3922,3981,4040,4099,4158,4217,4291,4349,4404,4455,4510,5862,6591,6645,6711,6812,6870,6922,6982,7044,7098,7134,7168,7218,7272,7318,7436,7472,7562,7674,7785,7980"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,72,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,97,99,100,101,102,103,104,105,120,134,135,139,140,144,159,160,161,167,177,210,231,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,370,435,501,570,633,703,771,843,913,974,1048,1121,1182,1243,1305,1369,1431,1492,1560,1660,1720,1786,1859,1928,1985,2037,2099,2171,2247,4569,4604,4748,4803,4866,4921,4979,5037,5098,5161,5218,5269,5319,5380,5437,5503,5537,5572,5867,6020,6087,6159,6228,6297,6371,6443,7323,7985,8102,8303,8413,8614,9485,9557,9624,9827,10128,11859,12540,13222", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,72,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,97,99,100,101,102,103,104,105,120,134,138,139,143,144,159,160,166,176,209,230,263,269", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,430,496,565,628,698,766,838,908,969,1043,1116,1177,1238,1300,1364,1426,1487,1555,1655,1715,1781,1854,1923,1980,2032,2094,2166,2242,2307,4599,4634,4798,4861,4916,4974,5032,5093,5156,5213,5264,5314,5375,5432,5498,5532,5567,5602,5932,6082,6154,6223,6292,6366,6438,6526,7389,8097,8298,8408,8609,8738,9552,9619,9822,10123,11854,12535,13217,13384"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "5937", "endColumns": "82", "endOffsets": "6015"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a3f1d2edbe65934dfc46afc0cea315bd/transformed/jetified-customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "71,74", "startColumns": "4,4", "startOffsets": "4515,4639", "endColumns": "53,66", "endOffsets": "4564,4701"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/275eb027f39dfb89e7ad38cb505cb864/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "92", "startColumns": "4", "startOffsets": "5607", "endColumns": "42", "endOffsets": "5645"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/c3d51f584632b9a7666f8b6ed78a6331/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "94", "startColumns": "4", "startOffsets": "5710", "endColumns": "53", "endOffsets": "5759"}}]}]}