{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,251,341,433,528,633,730,816,895,1064,1186,1310,1437,1558,1684,1808,1909,1996,2090,2183,2273,2361,2449,2547,2647,2727,2807,2887,2976,3067,3157,3247,3344,3426", "endColumns": "104,90,89,91,94,104,96,85,78,168,121,123,126,120,125,123,100,86,93,92,89,87,87,97,99,79,79,79,88,90,89,89,96,81,88", "endOffsets": "155,246,336,428,523,628,725,811,890,1059,1181,1305,1432,1553,1679,1803,1904,1991,2085,2178,2268,2356,2444,2542,2642,2722,2802,2882,2971,3062,3152,3242,3339,3421,3510"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12743,14013,14104,14194,14286,14381,14486,14583,14669,14748,14917,15039,15163,15290,15411,15537,15661,15762,15849,15943,16036,16126,16214,16302,16400,16500,16580,16660,16740,16829,16920,17010,17100,17197,17279", "endColumns": "104,90,89,91,94,104,96,85,78,168,121,123,126,120,125,123,100,86,93,92,89,87,87,97,99,79,79,79,88,90,89,89,96,81,88", "endOffsets": "12843,14099,14189,14281,14376,14481,14578,14664,14743,14912,15034,15158,15285,15406,15532,15656,15757,15844,15938,16031,16121,16209,16297,16395,16495,16575,16655,16735,16824,16915,17005,17095,17192,17274,17363"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-zh-rCN/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7382,7483,7612,7727,7829,7934,8050,8152,8343,8451,8552,8682,8797,8901,9009,9065,9122", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "7478,7607,7722,7824,7929,8045,8147,8239,8446,8547,8677,8792,8896,9004,9060,9117,9191"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3594,3686,3787,3881,3975,4068,4162,21155", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3681,3782,3876,3970,4063,4157,4253,21251"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-zh-rCN/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "202,269,338,409,477", "endColumns": "66,68,70,67,57", "endOffsets": "268,337,408,476,534"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4598,5611,5684,5759,5831", "endColumns": "70,72,74,71,61", "endOffsets": "4664,5679,5754,5826,5888"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,20930", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,21004"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2445,2524,2598,2648,2699,2765,2829,2898,2976,3047,3108,3179,3246,3306,3392,3471,3538,3621,3706,3780,3845,3921,3969,4042,4106,4182,4260,4322,4386,4449,4514,4594,4670,4748,4824,4878,4933", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2440,2519,2593,2643,2694,2760,2824,2893,2971,3042,3103,3174,3241,3301,3387,3466,3533,3616,3701,3775,3840,3916,3964,4037,4101,4177,4255,4317,4381,4444,4509,4589,4665,4743,4819,4873,4928,4997"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3251,3315,3377,3447,3517,6887,6978,7084,12533,12666,12848,13058,13117,13195,13256,13313,13369,13428,13486,13540,13625,13681,13739,13793,13858,17368,17442,17518,17640,17702,17764,17863,17942,18016,18066,18117,18183,18247,18316,18394,18465,18526,18597,18664,18724,18810,18889,18956,19039,19124,19198,19263,19339,19387,19460,19524,19600,19678,19740,19804,19867,19932,20012,20088,20166,20242,20296,20634", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,3310,3372,3442,3512,3589,6973,7079,7152,12590,12738,12902,13112,13190,13251,13308,13364,13423,13481,13535,13620,13676,13734,13788,13853,13945,17437,17513,17635,17697,17759,17858,17937,18011,18061,18112,18178,18242,18311,18389,18460,18521,18592,18659,18719,18805,18884,18951,19034,19119,19193,19258,19334,19382,19455,19519,19595,19673,19735,19799,19862,19927,20007,20083,20161,20237,20291,20346,20698"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-zh-rCN/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "212,253,310,358,421,483,528,614,685,756,841,915,978,1046,1124,1164,1212,1260,1334,1377,1414,1452,1489,1528,1575,1622,1669,1714,1759,1796,1847,1907,1968,2038,2116,2174,2231,2295,2336", "endColumns": "40,56,47,62,61,44,85,70,70,84,73,62,67,77,39,47,47,73,42,36,37,36,38,46,46,46,44,44,36,50,59,60,69,77,57,56,63,40,58", "endOffsets": "252,309,357,420,482,527,613,684,755,840,914,977,1045,1123,1163,1211,1259,1333,1376,1413,1451,1488,1527,1574,1621,1668,1713,1758,1795,1846,1906,1967,2037,2115,2173,2230,2294,2335,2394"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4258,4303,4364,4416,4483,4549,4669,4759,4834,4909,4998,5076,5143,5215,5297,5341,5393,5445,5523,5570,5893,5935,5976,6019,6070,6121,6172,6221,6270,6311,6366,6430,6495,6569,6651,6713,6774,6842,13950", "endColumns": "44,60,51,66,65,48,89,74,74,88,77,66,71,81,43,51,51,77,46,40,41,40,42,50,50,50,48,48,40,54,63,64,73,81,61,60,67,44,62", "endOffsets": "4298,4359,4411,4478,4544,4593,4754,4829,4904,4993,5071,5138,5210,5292,5336,5388,5440,5518,5565,5606,5930,5971,6014,6065,6116,6167,6216,6265,6306,6361,6425,6490,6564,6646,6708,6769,6837,6882,14008"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-zh-rCN/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "8244", "endColumns": "98", "endOffsets": "8338"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,880,945,1018,1093,1161,1234,1300", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,875,940,1013,1088,1156,1229,1295,1411"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7157,7234,12211,12296,12456,12907,12981,20351,20429,20504,20569,20703,20776,21009,21344,21417,21483", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "7229,7305,12291,12382,12528,12976,13053,20424,20499,20564,20629,20771,20846,21072,21412,21478,21594"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,607,707,811,912,1023,1101,1193,1273,1358,1460,1570,1667,1771,1873,1977,2089,2192,2289,2390,2493,2574,2665,2766,2871,2957,3051,3145,3248,3357,3453,3539,3608,3679,3758,3836,3924,4000,4077,4171,4261,4350,4441,4520,4612,4704,4796,4900,4976,5064", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "150,249,349,447,519,602,702,806,907,1018,1096,1188,1268,1353,1455,1565,1662,1766,1868,1972,2084,2187,2284,2385,2488,2569,2660,2761,2866,2952,3046,3140,3243,3352,3448,3534,3603,3674,3753,3831,3919,3995,4072,4166,4256,4345,4436,4515,4607,4699,4791,4895,4971,5059,5145"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2954,3053,3153,7310,9196,9279,9379,9483,9584,9695,9773,9865,9945,10030,10132,10242,10339,10443,10545,10649,10761,10864,10961,11062,11165,11246,11337,11438,11543,11629,11723,11817,11920,12029,12125,12387,12595,20851,21077,21256,21599,21675,21752,21846,21936,22025,22116,22195,22287,22379,22471,22575,22651,22739", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "2949,3048,3148,3246,7377,9274,9374,9478,9579,9690,9768,9860,9940,10025,10127,10237,10334,10438,10540,10644,10756,10859,10956,11057,11160,11241,11332,11433,11538,11624,11718,11812,11915,12024,12120,12206,12451,12661,20925,21150,21339,21670,21747,21841,21931,22020,22111,22190,22282,22374,22466,22570,22646,22734,22820"}}]}]}