{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-da/values-da.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-da/values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2836,2922,3000,3056,3107,3173,3241,3315,3404,3479,3551,3629,3699,3772,3876,3960,4037,4125,4214,4288,4361,4446,4495,4573,4639,4719,4802,4864,4928,4991,5060,5168,5271,5372,5471,5531,5586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2831,2917,2995,3051,3102,3168,3236,3310,3399,3474,3546,3624,3694,3767,3871,3955,4032,4120,4209,4283,4356,4441,4490,4568,4634,4714,4797,4859,4923,4986,5055,5163,5266,5367,5466,5526,5581,5661"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3408,3486,3562,3640,3737,7652,7752,7901,14258,14397,14605,14844,14904,14991,15055,15117,15179,15247,15312,15368,15486,15544,15605,15661,15736,19939,20025,20105,20246,20324,20404,20526,20612,20690,20746,20797,20863,20931,21005,21094,21169,21241,21319,21389,21462,21566,21650,21727,21815,21904,21978,22051,22136,22185,22263,22329,22409,22492,22554,22618,22681,22750,22858,22961,23062,23161,23221,23577", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "317,3481,3557,3635,3732,3812,7747,7896,7974,14317,14478,14673,14899,14986,15050,15112,15174,15242,15307,15363,15481,15539,15600,15656,15731,15857,20020,20100,20241,20319,20399,20521,20607,20685,20741,20792,20858,20926,21000,21089,21164,21236,21314,21384,21457,21561,21645,21722,21810,21899,21973,22046,22131,22180,22258,22324,22404,22487,22549,22613,22676,22745,22853,22956,23057,23156,23216,23271,23652"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-da/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8226,8332,8492,8619,8728,8871,8996,9116,9348,9504,9610,9772,9899,10044,10222,10288,10350", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "8327,8487,8614,8723,8866,8991,9111,9216,9499,9605,9767,9894,10039,10217,10283,10345,10423"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-da/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9221", "endColumns": "126", "endOffsets": "9343"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,270,369,469,569,703,813,907,991,1176,1361,1552,1743,1932,2123,2309,2436,2530,2632,2726,2819,2921,3012,3139,3254,3344,3428,3510,3604,3706,3800,3901,4009,4095", "endColumns": "121,92,98,99,99,133,109,93,83,184,184,190,190,188,190,185,126,93,101,93,92,101,90,126,114,89,83,81,93,101,93,100,107,85,93", "endOffsets": "172,265,364,464,564,698,808,902,986,1171,1356,1547,1738,1927,2118,2304,2431,2525,2627,2721,2814,2916,3007,3134,3249,3339,3423,3505,3599,3701,3795,3896,4004,4090,4184"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14483,15927,16020,16119,16219,16319,16453,16563,16657,16741,16926,17111,17302,17493,17682,17873,18059,18186,18280,18382,18476,18569,18671,18762,18889,19004,19094,19178,19260,19354,19456,19550,19651,19759,19845", "endColumns": "121,92,98,99,99,133,109,93,83,184,184,190,190,188,190,185,126,93,101,93,92,101,90,126,114,89,83,81,93,101,93,100,107,85,93", "endOffsets": "14600,16015,16114,16214,16314,16448,16558,16652,16736,16921,17106,17297,17488,17677,17868,18054,18181,18275,18377,18471,18564,18666,18757,18884,18999,19089,19173,19255,19349,19451,19545,19646,19754,19840,19934"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,23902", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,23977"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-da/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,317,375,466,550,597,729,815,893,1012,1097,1164,1248,1348,1395,1457,1519,1593,1638,1686,1734,1775,1824,1888,1952,2017,2079,2143,2182,2244,2305,2372,2452,2534,2596,2653,2727,2777", "endColumns": "45,62,57,90,83,46,131,85,77,118,84,66,83,99,46,61,61,73,44,47,47,40,48,63,63,64,61,63,38,61,60,66,79,81,61,56,73,49,60", "endOffsets": "253,316,374,465,549,596,728,814,892,1011,1096,1163,1247,1347,1394,1456,1518,1592,1637,1685,1733,1774,1823,1887,1951,2016,2078,2142,2181,2243,2304,2371,2451,2533,2595,2652,2726,2776,2837"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4544,4594,4661,4723,4818,4906,5026,5162,5252,5334,5457,5546,5617,5705,5809,5860,5926,5992,6070,6119,6489,6541,6586,6639,6707,6775,6844,6910,6978,7021,7087,7152,7223,7307,7393,7459,7520,7598,15862", "endColumns": "49,66,61,94,87,50,135,89,81,122,88,70,87,103,50,65,65,77,48,51,51,44,52,67,67,68,65,67,42,65,64,70,83,85,65,60,77,53,64", "endOffsets": "4589,4656,4718,4813,4901,4952,5157,5247,5329,5452,5541,5612,5700,5804,5855,5921,5987,6065,6114,6166,6536,6581,6634,6702,6770,6839,6905,6973,7016,7082,7147,7218,7302,7388,7454,7515,7593,7647,15922"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7979,8071,13906,14001,14176,14678,14755,23276,23365,23447,23512,23657,23738,23982,24331,24409,24476", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "8066,8146,13996,14095,14253,14750,14839,23360,23442,23507,23572,23733,23817,24047,24404,24471,24591"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,379,489,564,655,764,904,1022,1167,1247,1343,1428,1518,1628,1747,1848,1971,2089,2221,2390,2516,2629,2748,2864,2950,3044,3159,3288,3382,3496,3594,3717,3848,3949,4042,4118,4193,4273,4354,4451,4527,4607,4704,4799,4890,4985,5068,5169,5267,5367,5480,5556,5654", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "162,271,374,484,559,650,759,899,1017,1162,1242,1338,1423,1513,1623,1742,1843,1966,2084,2216,2385,2511,2624,2743,2859,2945,3039,3154,3283,3377,3491,3589,3712,3843,3944,4037,4113,4188,4268,4349,4446,4522,4602,4699,4794,4885,4980,5063,5164,5262,5362,5475,5551,5649,5744"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2974,3086,3195,3298,8151,10428,10519,10628,10768,10886,11031,11111,11207,11292,11382,11492,11611,11712,11835,11953,12085,12254,12380,12493,12612,12728,12814,12908,13023,13152,13246,13360,13458,13581,13712,13813,14100,14322,23822,24052,24234,24596,24672,24752,24849,24944,25035,25130,25213,25314,25412,25512,25625,25701,25799", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "3081,3190,3293,3403,8221,10514,10623,10763,10881,11026,11106,11202,11287,11377,11487,11606,11707,11830,11948,12080,12249,12375,12488,12607,12723,12809,12903,13018,13147,13241,13355,13453,13576,13707,13808,13901,14171,14392,23897,24128,24326,24667,24747,24844,24939,25030,25125,25208,25309,25407,25507,25620,25696,25794,25889"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3817,3913,4015,4112,4210,4317,4426,24133", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3908,4010,4107,4205,4312,4421,4539,24229"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-da/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,263,342,437,505", "endColumns": "64,78,94,67,59", "endOffsets": "262,341,436,504,564"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4957,6171,6254,6353,6425", "endColumns": "68,82,98,71,63", "endOffsets": "5021,6249,6348,6420,6484"}}]}]}