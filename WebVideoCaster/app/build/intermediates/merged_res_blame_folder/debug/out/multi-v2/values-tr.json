{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-tr/values-tr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-tr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8183,8293,8448,8584,8689,8836,8966,9093,9346,9518,9625,9782,9916,10061,10228,10290,10354", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "8288,8443,8579,8684,8831,8961,9088,9194,9513,9620,9777,9911,10056,10223,10285,10349,10429"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-tr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9199", "endColumns": "146", "endOffsets": "9341"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,275,389,500,580,672,782,911,1029,1167,1248,1342,1427,1520,1631,1748,1847,1980,2112,2230,2397,2511,2623,2738,2850,2936,3030,3150,3275,3372,3470,3572,3703,3839,3947,4044,4125,4206,4288,4369,4482,4558,4638,4734,4830,4922,5013,5097,5199,5295,5389,5506,5582,5685", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "161,270,384,495,575,667,777,906,1024,1162,1243,1337,1422,1515,1626,1743,1842,1975,2107,2225,2392,2506,2618,2733,2845,2931,3025,3145,3270,3367,3465,3567,3698,3834,3942,4039,4120,4201,4283,4364,4477,4553,4633,4729,4825,4917,5008,5092,5194,5290,5384,5501,5577,5680,5769"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,3118,3227,3341,8103,10434,10526,10636,10765,10883,11021,11102,11196,11281,11374,11485,11602,11701,11834,11966,12084,12251,12365,12477,12592,12704,12790,12884,13004,13129,13226,13324,13426,13557,13693,13801,14093,14322,23747,23980,24162,24539,24615,24695,24791,24887,24979,25070,25154,25256,25352,25446,25563,25639,25742", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "3113,3222,3336,3447,8178,10521,10631,10760,10878,11016,11097,11191,11276,11369,11480,11597,11696,11829,11961,12079,12246,12360,12472,12587,12699,12785,12879,12999,13124,13221,13319,13421,13552,13688,13796,13893,14169,14398,23824,24056,24270,24610,24690,24786,24882,24974,25065,25149,25251,25347,25441,25558,25634,25737,25826"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-tr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,327,384,467,544,599,724,806,885,992,1075,1144,1220,1320,1367,1427,1489,1563,1609,1654,1698,1738,1783,1841,1899,1974,2032,2089,2130,2196,2257,2321,2399,2480,2540,2600,2672,2716", "endColumns": "44,73,56,82,76,54,124,81,78,106,82,68,75,99,46,59,61,73,45,44,43,39,44,57,57,74,57,56,40,65,60,63,77,80,59,59,71,43,61", "endOffsets": "252,326,383,466,543,598,723,805,884,991,1074,1143,1219,1319,1366,1426,1488,1562,1608,1653,1697,1737,1782,1840,1898,1973,2031,2088,2129,2195,2256,2320,2398,2479,2539,2599,2671,2715,2777"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4582,4631,4709,4770,4857,4938,5068,5197,5283,5366,5477,5564,5637,5717,5821,5872,5936,6002,6080,6130,6502,6550,6594,6643,6705,6767,6846,6908,6969,7014,7084,7149,7217,7299,7384,7448,7512,7588,15883", "endColumns": "48,77,60,86,80,58,128,85,82,110,86,72,79,103,50,63,65,77,49,48,47,43,48,61,61,78,61,60,44,69,64,67,81,84,63,63,75,47,65", "endOffsets": "4626,4704,4765,4852,4933,4992,5192,5278,5361,5472,5559,5632,5712,5816,5867,5931,5997,6075,6125,6174,6545,6589,6638,6700,6762,6841,6903,6964,7009,7079,7144,7212,7294,7379,7443,7507,7583,7631,15944"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,23829", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,23904"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,281,379,483,590,696,806,904,997,1178,1346,1518,1690,1854,2026,2200,2318,2413,2519,2615,2709,2807,2901,3000,3106,3192,3275,3359,3457,3570,3662,3761,3868,3954", "endColumns": "129,95,97,103,106,105,109,97,92,180,167,171,171,163,171,173,117,94,105,95,93,97,93,98,105,85,82,83,97,112,91,98,106,85,94", "endOffsets": "180,276,374,478,585,691,801,899,992,1173,1341,1513,1685,1849,2021,2195,2313,2408,2514,2610,2704,2802,2896,2995,3101,3187,3270,3354,3452,3565,3657,3756,3863,3949,4044"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14494,15949,16045,16143,16247,16354,16460,16570,16668,16761,16942,17110,17282,17454,17618,17790,17964,18082,18177,18283,18379,18473,18571,18665,18764,18870,18956,19039,19123,19221,19334,19426,19525,19632,19718", "endColumns": "129,95,97,103,106,105,109,97,92,180,167,171,171,163,171,173,117,94,105,95,93,97,93,98,105,85,82,83,97,112,91,98,106,85,94", "endOffsets": "14619,16040,16138,16242,16349,16455,16565,16663,16756,16937,17105,17277,17449,17613,17785,17959,18077,18172,18278,18374,18468,18566,18660,18759,18865,18951,19034,19118,19216,19329,19421,19520,19627,19713,19808"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7926,8019,13898,13993,14174,14701,14784,23194,23282,23366,23434,23579,23659,23909,24275,24353,24421", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "8014,8098,13988,14088,14253,14779,14879,23277,23361,23429,23495,23654,23742,23975,24348,24416,24534"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3452,3527,3602,3679,3778,7636,7732,7844,14258,14403,14624,14884,14945,15036,15099,15162,15221,15290,15353,15407,15515,15573,15635,15689,15762,19813,19897,19988,20128,20205,20281,20412,20499,20575,20628,20682,20748,20818,20895,20978,21058,21129,21204,21282,21353,21454,21539,21628,21723,21816,21888,21960,22056,22108,22194,22261,22345,22435,22497,22561,22624,22694,22788,22890,22979,23079,23136,23500", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "310,3522,3597,3674,3773,3864,7727,7839,7921,14317,14489,14696,14940,15031,15094,15157,15216,15285,15348,15402,15510,15568,15630,15684,15757,15878,19892,19983,20123,20200,20276,20407,20494,20570,20623,20677,20743,20813,20890,20973,21053,21124,21199,21277,21348,21449,21534,21623,21718,21811,21883,21955,22051,22103,22189,22256,22340,22430,22492,22556,22619,22689,22783,22885,22974,23074,23131,23189,23574"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-tr/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,349,436,504", "endColumns": "66,83,86,67,67", "endOffsets": "264,348,435,503,571"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4997,6179,6267,6358,6430", "endColumns": "70,87,90,71,71", "endOffsets": "5063,6262,6353,6425,6497"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3869,3966,4068,4166,4263,4365,4471,24061", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3961,4063,4161,4258,4360,4466,4577,24157"}}]}]}