{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-nb/values-nb.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-nb/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,341,418,486", "endColumns": "66,75,76,67,62", "endOffsets": "264,340,417,485,548"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4938,6152,6232,6313,6385", "endColumns": "70,79,80,71,66", "endOffsets": "5004,6227,6308,6380,6447"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,382,491,566,656,764,904,1022,1168,1248,1344,1429,1519,1622,1736,1837,1960,2078,2207,2370,2493,2606,2725,2843,2929,3023,3138,3271,3368,3474,3573,3702,3839,3940,4033,4109,4182,4262,4342,4449,4525,4605,4702,4797,4884,4980,5064,5165,5263,5363,5476,5552,5651", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "163,274,377,486,561,651,759,899,1017,1163,1243,1339,1424,1514,1617,1731,1832,1955,2073,2202,2365,2488,2601,2720,2838,2924,3018,3133,3266,3363,3469,3568,3697,3834,3935,4028,4104,4177,4257,4337,4444,4520,4600,4697,4792,4879,4975,5059,5160,5258,5358,5471,5547,5646,5741"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2943,3056,3167,3270,8133,10422,10512,10620,10760,10878,11024,11104,11200,11285,11375,11478,11592,11693,11816,11934,12063,12226,12349,12462,12581,12699,12785,12879,12994,13127,13224,13330,13429,13558,13695,13796,14086,14314,23698,23928,24109,24480,24556,24636,24733,24828,24915,25011,25095,25196,25294,25394,25507,25583,25682", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "3051,3162,3265,3374,8203,10507,10615,10755,10873,11019,11099,11195,11280,11370,11473,11587,11688,11811,11929,12058,12221,12344,12457,12576,12694,12780,12874,12989,13122,13219,13325,13424,13553,13690,13791,13884,14157,14382,23773,24003,24211,24551,24631,24728,24823,24910,25006,25090,25191,25289,25389,25502,25578,25677,25772"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3784,3878,3980,4077,4176,4284,4390,24008", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3873,3975,4072,4171,4279,4385,4505,24104"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-nb/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,317,390,496,562,612,738,818,897,1023,1109,1179,1261,1359,1406,1468,1530,1604,1652,1699,1748,1791,1840,1904,1968,2033,2106,2182,2222,2291,2352,2417,2512,2592,2654,2711,2785,2834", "endColumns": "45,62,72,105,65,49,125,79,78,125,85,69,81,97,46,61,61,73,47,46,48,42,48,63,63,64,72,75,39,68,60,64,94,79,61,56,73,48,60", "endOffsets": "253,316,389,495,561,611,737,817,896,1022,1108,1178,1260,1358,1405,1467,1529,1603,1651,1698,1747,1790,1839,1903,1967,2032,2105,2181,2221,2290,2351,2416,2511,2591,2653,2710,2784,2833,2894"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4510,4560,4627,4704,4814,4884,5009,5139,5223,5306,5436,5526,5600,5686,5788,5839,5905,5971,6049,6101,6452,6505,6552,6605,6673,6741,6810,6887,6967,7011,7084,7149,7218,7317,7401,7467,7528,7606,15837", "endColumns": "49,66,76,109,69,53,129,83,82,129,89,73,85,101,50,65,65,77,51,50,52,46,52,67,67,68,76,79,43,72,64,68,98,83,65,60,77,52,64", "endOffsets": "4555,4622,4699,4809,4879,4933,5134,5218,5301,5431,5521,5595,5681,5783,5834,5900,5966,6044,6096,6147,6500,6547,6600,6668,6736,6805,6882,6962,7006,7079,7144,7213,7312,7396,7462,7523,7601,7654,15897"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7959,8052,13889,13986,14162,14671,14747,23159,23248,23330,23394,23536,23616,23858,24216,24293,24360", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "8047,8128,13981,14081,14245,14742,14830,23243,23325,23389,23453,23611,23693,23923,24288,24355,24475"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-nb/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8208,8314,8473,8599,8708,8864,8994,9114,9347,9501,9608,9769,9897,10039,10215,10282,10344", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "8309,8468,8594,8703,8859,8989,9109,9212,9496,9603,9764,9892,10034,10210,10277,10339,10417"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-nb/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9217", "endColumns": "129", "endOffsets": "9342"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2769,2857,2935,2989,3040,3106,3174,3248,3338,3414,3485,3563,3633,3703,3803,3892,3970,4058,4148,4220,4292,4376,4427,4505,4571,4652,4735,4797,4861,4924,4993,5093,5197,5290,5390,5448,5503", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2764,2852,2930,2984,3035,3101,3169,3243,3333,3409,3480,3558,3628,3698,3798,3887,3965,4053,4143,4215,4287,4371,4422,4500,4566,4647,4730,4792,4856,4919,4988,5088,5192,5285,5385,5443,5498,5576"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3379,3456,3529,3616,3704,7659,7758,7877,14250,14387,14603,14835,14895,14982,15046,15108,15172,15240,15305,15359,15468,15526,15588,15642,15717,19848,19930,20010,20144,20222,20302,20425,20513,20591,20645,20696,20762,20830,20904,20994,21070,21141,21219,21289,21359,21459,21548,21626,21714,21804,21876,21948,22032,22083,22161,22227,22308,22391,22453,22517,22580,22649,22749,22853,22946,23046,23104,23458", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "306,3451,3524,3611,3699,3779,7753,7872,7954,14309,14474,14666,14890,14977,15041,15103,15167,15235,15300,15354,15463,15521,15583,15637,15712,15832,19925,20005,20139,20217,20297,20420,20508,20586,20640,20691,20757,20825,20899,20989,21065,21136,21214,21284,21354,21454,21543,21621,21709,21799,21871,21943,22027,22078,22156,22222,22303,22386,22448,22512,22575,22644,22744,22848,22941,23041,23099,23154,23531"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,23778", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,23853"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,272,371,472,574,704,813,907,991,1167,1343,1525,1710,1885,2067,2247,2371,2466,2569,2664,2757,2850,2939,3062,3174,3265,3351,3434,3534,3642,3736,3839,3945,4031", "endColumns": "123,92,98,100,101,129,108,93,83,175,175,181,184,174,181,179,123,94,102,94,92,92,88,122,111,90,85,82,99,107,93,102,105,85,93", "endOffsets": "174,267,366,467,569,699,808,902,986,1162,1338,1520,1705,1880,2062,2242,2366,2461,2564,2659,2752,2845,2934,3057,3169,3260,3346,3429,3529,3637,3731,3834,3940,4026,4120"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14479,15902,15995,16094,16195,16297,16427,16536,16630,16714,16890,17066,17248,17433,17608,17790,17970,18094,18189,18292,18387,18480,18573,18662,18785,18897,18988,19074,19157,19257,19365,19459,19562,19668,19754", "endColumns": "123,92,98,100,101,129,108,93,83,175,175,181,184,174,181,179,123,94,102,94,92,92,88,122,111,90,85,82,99,107,93,102,105,85,93", "endOffsets": "14598,15990,16089,16190,16292,16422,16531,16625,16709,16885,17061,17243,17428,17603,17785,17965,18089,18184,18287,18382,18475,18568,18657,18780,18892,18983,19069,19152,19252,19360,19454,19557,19663,19749,19843"}}]}]}