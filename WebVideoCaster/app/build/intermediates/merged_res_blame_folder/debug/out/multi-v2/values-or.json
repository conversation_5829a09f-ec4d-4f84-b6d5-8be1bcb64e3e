{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-or/values-or.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-or/values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1022,1111,1176,1235,1321,1385,1449,1512,1585,1649,1703,1815,1873,1935,1989,2061,2183,2270,2356,2496,2573,2654,2781,2872,2949,3003,3054,3120,3190,3267,3354,3429,3500,3577,3646,3715,3822,3913,3985,4074,4163,4237,4309,4395,4445,4524,4590,4670,4754,4816,4880,4943,5012,5112,5207,5299,5391,5449,5504", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83", "endOffsets": "267,349,427,504,590,674,768,873,952,1017,1106,1171,1230,1316,1380,1444,1507,1580,1644,1698,1810,1868,1930,1984,2056,2178,2265,2351,2491,2568,2649,2776,2867,2944,2998,3049,3115,3185,3262,3349,3424,3495,3572,3641,3710,3817,3908,3980,4069,4158,4232,4304,4390,4440,4519,4585,4665,4749,4811,4875,4938,5007,5107,5202,5294,5386,5444,5499,5583"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3541,3623,3701,3778,3864,7806,7900,8005,14612,14761,14979,15219,15278,15364,15428,15492,15555,15628,15692,15746,15858,15916,15978,16032,16104,20314,20401,20487,20627,20704,20785,20912,21003,21080,21134,21185,21251,21321,21398,21485,21560,21631,21708,21777,21846,21953,22044,22116,22205,22294,22368,22440,22526,22576,22655,22721,22801,22885,22947,23011,23074,23143,23243,23338,23430,23522,23580,23950", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83", "endOffsets": "317,3618,3696,3773,3859,3943,7895,8000,8079,14672,14845,15039,15273,15359,15423,15487,15550,15623,15687,15741,15853,15911,15973,16027,16099,16221,20396,20482,20622,20699,20780,20907,20998,21075,21129,21180,21246,21316,21393,21480,21555,21626,21703,21772,21841,21948,22039,22111,22200,22289,22363,22435,22521,22571,22650,22716,22796,22880,22942,23006,23069,23138,23238,23333,23425,23517,23575,23630,24029"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,441,551,658,744,848,968,1047,1128,1219,1312,1415,1510,1610,1703,1798,1894,1985,2075,2164,2274,2378,2484,2595,2699,2817,2980,24284", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "436,546,653,739,843,963,1042,1123,1214,1307,1410,1505,1605,1698,1793,1889,1980,2070,2159,2269,2373,2479,2590,2694,2812,2975,3081,24369"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8084,8181,14263,14355,14526,15044,15121,23635,23723,23810,23880,24034,24112,24374,24733,24816,24883", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "8176,8263,14350,14450,14607,15116,15214,23718,23805,23875,23945,24107,24189,24439,24811,24878,24997"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,285,394,504,623,744,858,956,1050,1227,1399,1573,1751,1923,2102,2276,2395,2488,2600,2702,2804,2906,3006,3120,3238,3327,3412,3497,3605,3710,3804,3912,4025,4111", "endColumns": "128,100,108,109,118,120,113,97,93,176,171,173,177,171,178,173,118,92,111,101,101,101,99,113,117,88,84,84,107,104,93,107,112,85,94", "endOffsets": "179,280,389,499,618,739,853,951,1045,1222,1394,1568,1746,1918,2097,2271,2390,2483,2595,2697,2799,2901,3001,3115,3233,3322,3407,3492,3600,3705,3799,3907,4020,4106,4201"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14850,16292,16393,16502,16612,16731,16852,16966,17064,17158,17335,17507,17681,17859,18031,18210,18384,18503,18596,18708,18810,18912,19014,19114,19228,19346,19435,19520,19605,19713,19818,19912,20020,20133,20219", "endColumns": "128,100,108,109,118,120,113,97,93,176,171,173,177,171,178,173,118,92,111,101,101,101,99,113,117,88,84,84,107,104,93,107,112,85,94", "endOffsets": "14974,16388,16497,16607,16726,16847,16961,17059,17153,17330,17502,17676,17854,18026,18205,18379,18498,18591,18703,18805,18907,19009,19109,19223,19341,19430,19515,19600,19708,19813,19907,20015,20128,20214,20309"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-or/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,341,422,491", "endColumns": "66,75,80,68,67", "endOffsets": "264,340,421,490,558"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5080,6296,6376,6461,6534", "endColumns": "70,79,84,72,71", "endOffsets": "5146,6371,6456,6529,6601"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3948,4051,4153,4256,4361,4462,4564,24531", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "4046,4148,4251,4356,4457,4559,4678,24627"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-or/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8354,8465,8626,8758,8875,9030,9165,9279,9529,9696,9809,9970,10103,10253,10410,10475,10547", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "8460,8621,8753,8870,9025,9160,9274,9384,9691,9804,9965,10098,10248,10405,10470,10542,10629"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-or/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,321,376,456,526,581,707,789,866,987,1073,1142,1228,1324,1374,1438,1502,1576,1622,1670,1717,1759,1808,1871,1937,1997,2063,2125,2171,2244,2306,2377,2454,2544,2611,2675,2746,2798", "endColumns": "46,65,54,79,69,54,125,81,76,120,85,68,85,95,49,63,63,73,45,47,46,41,48,62,65,59,65,61,45,72,61,70,76,89,66,63,70,51,61", "endOffsets": "254,320,375,455,525,580,706,788,865,986,1072,1141,1227,1323,1373,1437,1501,1575,1621,1669,1716,1758,1807,1870,1936,1996,2062,2124,2170,2243,2305,2376,2453,2543,2610,2674,2745,2797,2859"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4683,4734,4804,4863,4947,5021,5151,5281,5367,5448,5573,5663,5736,5826,5926,5980,6048,6116,6194,6244,6606,6657,6703,6756,6823,6893,6957,7027,7093,7143,7220,7286,7361,7442,7536,7607,7675,7750,16226", "endColumns": "50,69,58,83,73,58,129,85,80,124,89,72,89,99,53,67,67,77,49,51,50,45,52,66,69,63,69,65,49,76,65,74,80,93,70,67,74,55,65", "endOffsets": "4729,4799,4858,4942,5016,5075,5276,5362,5443,5568,5658,5731,5821,5921,5975,6043,6111,6189,6239,6291,6652,6698,6751,6818,6888,6952,7022,7088,7138,7215,7281,7356,7437,7531,7602,7670,7745,7801,16287"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-or/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9389", "endColumns": "139", "endOffsets": "9524"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,395,510,596,692,806,945,1067,1214,1295,1396,1488,1579,1691,1817,1923,2062,2196,2324,2513,2636,2761,2893,3018,3111,3203,3316,3434,3535,3636,3735,3872,4018,4121,4225,4296,4380,4470,4557,4658,4734,4815,4912,5014,5103,5200,5283,5387,5482,5580,5700,5776,5875", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "168,281,390,505,591,687,801,940,1062,1209,1290,1391,1483,1574,1686,1812,1918,2057,2191,2319,2508,2631,2756,2888,3013,3106,3198,3311,3429,3530,3631,3730,3867,4013,4116,4220,4291,4375,4465,4552,4653,4729,4810,4907,5009,5098,5195,5278,5382,5477,5575,5695,5771,5870,5960"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3086,3204,3317,3426,8268,10634,10730,10844,10983,11105,11252,11333,11434,11526,11617,11729,11855,11961,12100,12234,12362,12551,12674,12799,12931,13056,13149,13241,13354,13472,13573,13674,13773,13910,14056,14159,14455,14677,24194,24444,24632,25002,25078,25159,25256,25358,25447,25544,25627,25731,25826,25924,26044,26120,26219", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "3199,3312,3421,3536,8349,10725,10839,10978,11100,11247,11328,11429,11521,11612,11724,11850,11956,12095,12229,12357,12546,12669,12794,12926,13051,13144,13236,13349,13467,13568,13669,13768,13905,14051,14154,14258,14521,14756,24279,24526,24728,25073,25154,25251,25353,25442,25539,25622,25726,25821,25919,26039,26115,26214,26304"}}]}]}