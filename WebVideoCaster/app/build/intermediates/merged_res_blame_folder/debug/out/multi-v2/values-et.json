{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-et/values-et.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-et/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,437,505", "endColumns": "66,84,86,67,69", "endOffsets": "264,349,436,504,574"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5064,6307,6396,6487,6559", "endColumns": "70,88,90,71,73", "endOffsets": "5130,6391,6482,6554,6628"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,289,399,514,592,689,804,933,1049,1193,1276,1373,1463,1558,1670,1793,1893,2024,2153,2280,2451,2573,2688,2806,2925,3016,3109,3225,3355,3456,3555,3656,3782,3913,4017,4115,4188,4266,4349,4430,4532,4608,4690,4787,4887,4977,5077,5162,5267,5364,5466,5579,5655,5755", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "166,284,394,509,587,684,799,928,1044,1188,1271,1368,1458,1553,1665,1788,1888,2019,2148,2275,2446,2568,2683,2801,2920,3011,3104,3220,3350,3451,3550,3651,3777,3908,4012,4110,4183,4261,4344,4425,4527,4603,4685,4782,4882,4972,5072,5157,5262,5359,5461,5574,5650,5750,5845"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3038,3154,3272,3382,8297,10593,10690,10805,10934,11050,11194,11277,11374,11464,11559,11671,11794,11894,12025,12154,12281,12452,12574,12689,12807,12926,13017,13110,13226,13356,13457,13556,13657,13783,13914,14018,14307,14526,23978,24216,24398,24770,24846,24928,25025,25125,25215,25315,25400,25505,25602,25704,25817,25893,25993", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "3149,3267,3377,3492,8370,10685,10800,10929,11045,11189,11272,11369,11459,11554,11666,11789,11889,12020,12149,12276,12447,12569,12684,12802,12921,13012,13105,13221,13351,13452,13551,13652,13778,13909,14013,14111,14375,14599,24056,24292,24495,24841,24923,25020,25120,25210,25310,25395,25500,25597,25699,25812,25888,25988,26083"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-et/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,328,384,466,542,599,738,823,905,1021,1107,1176,1259,1362,1410,1476,1548,1622,1672,1715,1756,1796,1844,1910,1982,2050,2118,2185,2225,2298,2360,2426,2504,2584,2653,2710,2782,2836", "endColumns": "45,73,55,81,75,56,138,84,81,115,85,68,82,102,47,65,71,73,49,42,40,39,47,65,71,67,67,66,39,72,61,65,77,79,68,56,71,53,67", "endOffsets": "253,327,383,465,541,598,737,822,904,1020,1106,1175,1258,1361,1409,1475,1547,1621,1671,1714,1755,1795,1843,1909,1981,2049,2117,2184,2224,2297,2359,2425,2503,2583,2652,2709,2781,2835,2903"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4649,4699,4777,4837,4923,5003,5135,5278,5367,5453,5573,5663,5736,5823,5930,5982,6052,6128,6206,6260,6633,6678,6722,6774,6844,6920,6992,7064,7135,7179,7256,7322,7392,7474,7558,7631,7692,7768,16080", "endColumns": "49,77,59,85,79,60,142,88,85,119,89,72,86,106,51,69,75,77,53,46,44,43,51,69,75,71,71,70,43,76,65,69,81,83,72,60,75,57,71", "endOffsets": "4694,4772,4832,4918,4998,5059,5273,5362,5448,5568,5658,5731,5818,5925,5977,6047,6123,6201,6255,6302,6673,6717,6769,6839,6915,6987,7059,7130,7174,7251,7317,7387,7469,7553,7626,7687,7763,7821,16147"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3920,4015,4117,4215,4318,4424,4529,24297", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "4010,4112,4210,4313,4419,4524,4644,24393"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-et/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9373", "endColumns": "139", "endOffsets": "9508"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-et/values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1051,1136,1204,1268,1355,1419,1483,1542,1614,1678,1732,1851,1911,1972,2026,2099,2232,2316,2409,2547,2627,2706,2832,2920,2999,3054,3105,3171,3244,3323,3409,3488,3561,3636,3710,3782,3895,3983,4060,4151,4243,4315,4389,4480,4534,4616,4685,4768,4854,4916,4980,5043,5111,5214,5317,5414,5515,5574,5629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "261,341,420,505,597,684,783,900,982,1046,1131,1199,1263,1350,1414,1478,1537,1609,1673,1727,1846,1906,1967,2021,2094,2227,2311,2404,2542,2622,2701,2827,2915,2994,3049,3100,3166,3239,3318,3404,3483,3556,3631,3705,3777,3890,3978,4055,4146,4238,4310,4384,4475,4529,4611,4680,4763,4849,4911,4975,5038,5106,5209,5312,5409,5510,5569,5624,5705"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3497,3577,3656,3741,3833,7826,7925,8042,14462,14604,14815,15052,15116,15203,15267,15331,15390,15462,15526,15580,15699,15759,15820,15874,15947,20024,20108,20201,20339,20419,20498,20624,20712,20791,20846,20897,20963,21036,21115,21201,21280,21353,21428,21502,21574,21687,21775,21852,21943,22035,22107,22181,22272,22326,22408,22477,22560,22646,22708,22772,22835,22903,23006,23109,23206,23307,23366,23730", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "311,3572,3651,3736,3828,3915,7920,8037,8119,14521,14684,14878,15111,15198,15262,15326,15385,15457,15521,15575,15694,15754,15815,15869,15942,16075,20103,20196,20334,20414,20493,20619,20707,20786,20841,20892,20958,21031,21110,21196,21275,21348,21423,21497,21569,21682,21770,21847,21938,22030,22102,22176,22267,22321,22403,22472,22555,22641,22703,22767,22830,22898,23001,23104,23201,23302,23361,23416,23806"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,281,387,495,609,718,827,924,1021,1195,1355,1520,1691,1851,2016,2178,2299,2397,2508,2603,2697,2797,2890,2999,3110,3193,3276,3362,3467,3573,3666,3767,3872,3959", "endColumns": "125,99,105,107,113,108,108,96,96,173,159,164,170,159,164,161,120,97,110,94,93,99,92,108,110,82,82,85,104,105,92,100,104,86,93", "endOffsets": "176,276,382,490,604,713,822,919,1016,1190,1350,1515,1686,1846,2011,2173,2294,2392,2503,2598,2692,2792,2885,2994,3105,3188,3271,3357,3462,3568,3661,3762,3867,3954,4048"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14689,16152,16252,16358,16466,16580,16689,16798,16895,16992,17166,17326,17491,17662,17822,17987,18149,18270,18368,18479,18574,18668,18768,18861,18970,19081,19164,19247,19333,19438,19544,19637,19738,19843,19930", "endColumns": "125,99,105,107,113,108,108,96,96,173,159,164,170,159,164,161,120,97,110,94,93,99,92,108,110,82,82,85,104,105,92,100,104,86,93", "endOffsets": "14810,16247,16353,16461,16575,16684,16793,16890,16987,17161,17321,17486,17657,17817,17982,18144,18265,18363,18474,18569,18663,18763,18856,18965,19076,19159,19242,19328,19433,19539,19632,19733,19838,19925,20019"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,24061", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,24139"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8124,8217,14116,14212,14380,14883,14961,23421,23512,23596,23664,23811,23893,24144,24500,24577,24648", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "8212,8292,14207,14302,14457,14956,15047,23507,23591,23659,23725,23888,23973,24211,24572,24643,24765"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-et/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8375,8478,8640,8767,8875,9025,9154,9270,9513,9673,9781,9945,10077,10232,10377,10440,10505", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "8473,8635,8762,8870,9020,9149,9265,9368,9668,9776,9940,10072,10227,10372,10435,10500,10588"}}]}]}