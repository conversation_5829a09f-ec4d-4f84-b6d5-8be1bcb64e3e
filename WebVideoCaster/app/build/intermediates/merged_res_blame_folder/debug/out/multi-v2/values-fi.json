{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-fi/values-fi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,23997", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,24073"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-fi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,333,391,486,560,616,737,819,903,1015,1098,1165,1241,1341,1394,1462,1530,1604,1649,1691,1735,1776,1829,1897,1965,2023,2091,2159,2201,2262,2324,2387,2467,2548,2615,2672,2746,2798", "endColumns": "44,79,57,94,73,55,120,81,83,111,82,66,75,99,52,67,67,73,44,41,43,40,52,67,67,57,67,67,41,60,61,62,79,80,66,56,73,51,65", "endOffsets": "252,332,390,485,559,615,736,818,902,1014,1097,1164,1240,1340,1393,1461,1529,1603,1648,1690,1734,1775,1828,1896,1964,2022,2090,2158,2200,2261,2323,2386,2466,2547,2614,2671,2745,2797,2863"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4562,4611,4695,4757,4856,4934,5065,5190,5276,5364,5480,5567,5638,5718,5822,5879,5951,6023,6101,6150,6521,6569,6614,6671,6743,6815,6877,6949,7021,7067,7132,7198,7265,7349,7434,7505,7566,7644,15954", "endColumns": "48,83,61,98,77,59,124,85,87,115,86,70,79,103,56,71,71,77,48,45,47,44,56,71,71,61,71,71,45,64,65,66,83,84,70,60,77,55,69", "endOffsets": "4606,4690,4752,4851,4929,4989,5185,5271,5359,5475,5562,5633,5713,5817,5874,5946,6018,6096,6145,6191,6564,6609,6666,6738,6810,6872,6944,7016,7062,7127,7193,7260,7344,7429,7500,7561,7639,7695,16019"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-fi/values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2804,2894,2968,3020,3071,3137,3214,3296,3380,3454,3528,3607,3684,3756,3863,3952,4028,4119,4214,4288,4361,4455,4509,4583,4655,4741,4827,4889,4953,5016,5087,5188,5291,5386,5486,5542,5597", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2799,2889,2963,3015,3066,3132,3209,3291,3375,3449,3523,3602,3679,3751,3858,3947,4023,4114,4209,4283,4356,4450,4504,4578,4650,4736,4822,4884,4948,5011,5082,5183,5286,5381,5481,5537,5592,5671"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3424,3500,3574,3657,3746,7700,7796,7904,14336,14480,14699,14944,15009,15097,15162,15228,15286,15357,15423,15477,15587,15647,15711,15765,15838,19974,20058,20139,20272,20357,20442,20575,20665,20739,20791,20842,20908,20985,21067,21151,21225,21299,21378,21455,21527,21634,21723,21799,21890,21985,22059,22132,22226,22280,22354,22426,22512,22598,22660,22724,22787,22858,22959,23062,23157,23257,23313,23675", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "313,3495,3569,3652,3741,3823,7791,7899,7983,14396,14568,14769,15004,15092,15157,15223,15281,15352,15418,15472,15582,15642,15706,15760,15833,15949,20053,20134,20267,20352,20437,20570,20660,20734,20786,20837,20903,20980,21062,21146,21220,21294,21373,21450,21522,21629,21718,21794,21885,21980,22054,22127,22221,22275,22349,22421,22507,22593,22655,22719,22782,22853,22954,23057,23152,23252,23308,23363,23749"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-fi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9240", "endColumns": "149", "endOffsets": "9385"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-fi/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,437,505", "endColumns": "66,84,86,67,68", "endOffsets": "264,349,436,504,573"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4994,6196,6285,6376,6448", "endColumns": "70,88,90,71,72", "endOffsets": "5060,6280,6371,6443,6516"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3828,3924,4026,4124,4229,4334,4446,24232", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3919,4021,4119,4224,4329,4441,4557,24328"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,381,491,571,668,783,916,1034,1182,1268,1360,1454,1552,1666,1785,1882,2010,2138,2263,2426,2544,2664,2788,2907,3002,3097,3214,3332,3431,3538,3642,3776,3916,4020,4121,4200,4279,4359,4441,4537,4613,4694,4787,4886,4979,5077,5163,5267,5366,5469,5583,5659,5760", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "162,271,376,486,566,663,778,911,1029,1177,1263,1355,1449,1547,1661,1780,1877,2005,2133,2258,2421,2539,2659,2783,2902,2997,3092,3209,3327,3426,3533,3637,3771,3911,4015,4116,4195,4274,4354,4436,4532,4608,4689,4782,4881,4974,5072,5158,5262,5361,5464,5578,5654,5755,5850"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2988,3100,3209,3314,8167,10418,10515,10630,10763,10881,11029,11115,11207,11301,11399,11513,11632,11729,11857,11985,12110,12273,12391,12511,12635,12754,12849,12944,13061,13179,13278,13385,13489,13623,13763,13867,14168,14401,23917,24150,24333,24700,24776,24857,24950,25049,25142,25240,25326,25430,25529,25632,25746,25822,25923", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "3095,3204,3309,3419,8242,10510,10625,10758,10876,11024,11110,11202,11296,11394,11508,11627,11724,11852,11980,12105,12268,12386,12506,12630,12749,12844,12939,13056,13174,13273,13380,13484,13618,13758,13862,13963,14242,14475,23992,24227,24424,24771,24852,24945,25044,25137,25235,25321,25425,25524,25627,25741,25817,25918,26013"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7988,8082,13968,14067,14247,14774,14851,23368,23459,23541,23607,23754,23835,24078,24429,24506,24578", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "8077,8162,14062,14163,14331,14846,14939,23454,23536,23602,23670,23830,23912,24145,24501,24573,24695"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-fi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8247,8358,8511,8642,8748,8891,9017,9133,9390,9531,9637,9786,9912,10060,10199,10265,10335", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "8353,8506,8637,8743,8886,9012,9128,9235,9526,9632,9781,9907,10055,10194,10260,10330,10413"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,278,382,488,601,719,827,925,1013,1187,1361,1537,1714,1887,2065,2241,2361,2461,2572,2668,2764,2863,2957,3068,3177,3263,3347,3432,3535,3651,3744,3841,3945,4036", "endColumns": "125,96,103,105,112,117,107,97,87,173,173,175,176,172,177,175,119,99,110,95,95,98,93,110,108,85,83,84,102,115,92,96,103,90,94", "endOffsets": "176,273,377,483,596,714,822,920,1008,1182,1356,1532,1709,1882,2060,2236,2356,2456,2567,2663,2759,2858,2952,3063,3172,3258,3342,3427,3530,3646,3739,3836,3940,4031,4126"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14573,16024,16121,16225,16331,16444,16562,16670,16768,16856,17030,17204,17380,17557,17730,17908,18084,18204,18304,18415,18511,18607,18706,18800,18911,19020,19106,19190,19275,19378,19494,19587,19684,19788,19879", "endColumns": "125,96,103,105,112,117,107,97,87,173,173,175,176,172,177,175,119,99,110,95,95,98,93,110,108,85,83,84,102,115,92,96,103,90,94", "endOffsets": "14694,16116,16220,16326,16439,16557,16665,16763,16851,17025,17199,17375,17552,17725,17903,18079,18199,18299,18410,18506,18602,18701,18795,18906,19015,19101,19185,19270,19373,19489,19582,19679,19783,19874,19969"}}]}]}