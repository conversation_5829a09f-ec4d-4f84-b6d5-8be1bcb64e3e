{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-fa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,251,322,385,475,545,596,726,807,891,1001,1087,1160,1236,1335,1379,1435,1491,1565,1610,1656,1695,1733,1778,1833,1888,1946,2002,2058,2103,2170,2232,2294,2379,2461,2526,2586,2658,2700", "endColumns": "42,70,62,89,69,50,129,80,83,109,85,72,75,98,43,55,55,73,44,45,38,37,44,54,54,57,55,55,44,66,61,61,84,81,64,59,71,41,65", "endOffsets": "250,321,384,474,544,595,725,806,890,1000,1086,1159,1235,1334,1378,1434,1490,1564,1609,1655,1694,1732,1777,1832,1887,1945,2001,2057,2102,2169,2231,2293,2378,2460,2525,2585,2657,2699,2765"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4584,4631,4706,4773,4867,4941,5067,5201,5286,5374,5488,5578,5655,5735,5838,5886,5946,6006,6084,6133,6490,6533,6575,6624,6683,6742,6804,6864,6924,6973,7044,7110,7176,7265,7351,7420,7484,7560,15834", "endColumns": "46,74,66,93,73,54,133,84,87,113,89,76,79,102,47,59,59,77,48,49,42,41,48,58,58,61,59,59,48,70,65,65,88,85,68,63,75,45,69", "endOffsets": "4626,4701,4768,4862,4936,4991,5196,5281,5369,5483,5573,5650,5730,5833,5881,5941,6001,6079,6128,6178,6528,6570,6619,6678,6737,6799,6859,6919,6968,7039,7105,7171,7260,7346,7415,7479,7555,7601,15899"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-fa/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9137", "endColumns": "154", "endOffsets": "9287"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3860,3959,4061,4160,4260,4361,4467,23963", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3954,4056,4155,4255,4356,4462,4579,24059"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,276,379,489,596,715,828,933,1021,1198,1364,1531,1706,1874,2045,2212,2324,2424,2539,2634,2731,2824,2919,3031,3146,3227,3308,3396,3495,3598,3690,3789,3890,3975", "endColumns": "122,97,102,109,106,118,112,104,87,176,165,166,174,167,170,166,111,99,114,94,96,92,94,111,114,80,80,87,98,102,91,98,100,84,95", "endOffsets": "173,271,374,484,591,710,823,928,1016,1193,1359,1526,1701,1869,2040,2207,2319,2419,2534,2629,2726,2819,2914,3026,3141,3222,3303,3391,3490,3593,3685,3784,3885,3970,4066"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14430,15904,16002,16105,16215,16322,16441,16554,16659,16747,16924,17090,17257,17432,17600,17771,17938,18050,18150,18265,18360,18457,18550,18645,18757,18872,18953,19034,19122,19221,19324,19416,19515,19616,19701", "endColumns": "122,97,102,109,106,118,112,104,87,176,165,166,174,167,170,166,111,99,114,94,96,92,94,111,114,80,80,87,98,102,91,98,100,84,95", "endOffsets": "14548,15997,16100,16210,16317,16436,16549,16654,16742,16919,17085,17252,17427,17595,17766,17933,18045,18145,18260,18355,18452,18545,18640,18752,18867,18948,19029,19117,19216,19319,19411,19510,19611,19696,19792"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1046,1136,1205,1265,1356,1419,1483,1542,1609,1671,1726,1849,1907,1968,2023,2095,2232,2313,2395,2525,2599,2673,2805,2891,2968,3019,3073,3139,3210,3287,3368,3447,3520,3594,3664,3738,3839,3925,3999,4088,4180,4254,4327,4416,4467,4547,4614,4697,4781,4843,4907,4970,5039,5133,5234,5327,5425,5480,5538", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,131,85,76,50,53,65,70,76,80,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77", "endOffsets": "254,329,406,488,581,668,765,894,978,1041,1131,1200,1260,1351,1414,1478,1537,1604,1666,1721,1844,1902,1963,2018,2090,2227,2308,2390,2520,2594,2668,2800,2886,2963,3014,3068,3134,3205,3282,3363,3442,3515,3589,3659,3733,3834,3920,3994,4083,4175,4249,4322,4411,4462,4542,4609,4692,4776,4838,4902,4965,5034,5128,5229,5322,5420,5475,5533,5611"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3446,3521,3598,3680,3773,7606,7703,7832,14198,14340,14553,14807,14867,14958,15021,15085,15144,15211,15273,15328,15451,15509,15570,15625,15697,19797,19878,19960,20090,20164,20238,20370,20456,20533,20584,20638,20704,20775,20852,20933,21012,21085,21159,21229,21303,21404,21490,21564,21653,21745,21819,21892,21981,22032,22112,22179,22262,22346,22408,22472,22535,22604,22698,22799,22892,22990,23045,23404", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,131,85,76,50,53,65,70,76,80,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77", "endOffsets": "304,3516,3593,3675,3768,3855,7698,7827,7911,14256,14425,14617,14862,14953,15016,15080,15139,15206,15268,15323,15446,15504,15565,15620,15692,15829,19873,19955,20085,20159,20233,20365,20451,20528,20579,20633,20699,20770,20847,20928,21007,21080,21154,21224,21298,21399,21485,21559,21648,21740,21814,21887,21976,22027,22107,22174,22257,22341,22403,22467,22530,22599,22693,22794,22887,22985,23040,23098,23477"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,23727", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,23804"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,969,1035,1116,1198,1272,1347,1414", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,964,1030,1111,1193,1267,1342,1409,1526"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7916,8003,13844,13938,14112,14622,14704,23103,23188,23271,23338,23482,23563,23809,24159,24234,24301", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "7998,8077,13933,14031,14193,14699,14802,23183,23266,23333,23399,23558,23640,23878,24229,24296,24413"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-fa/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,338,419,492", "endColumns": "66,72,80,72,63", "endOffsets": "264,337,418,491,555"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4996,6183,6260,6345,6422", "endColumns": "70,76,84,76,67", "endOffsets": "5062,6255,6340,6417,6485"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,277,387,502,578,670,781,916,1028,1159,1240,1339,1427,1520,1630,1749,1853,1982,2109,2227,2388,2506,2615,2728,2842,2931,3025,3142,3270,3373,3472,3573,3699,3831,3933,4038,4114,4193,4275,4355,4450,4528,4608,4705,4802,4895,4991,5074,5174,5270,5368,5484,5562,5662", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "162,272,382,497,573,665,776,911,1023,1154,1235,1334,1422,1515,1625,1744,1848,1977,2104,2222,2383,2501,2610,2723,2837,2926,3020,3137,3265,3368,3467,3568,3694,3826,3928,4033,4109,4188,4270,4350,4445,4523,4603,4700,4797,4890,4986,5069,5169,5265,5363,5479,5557,5657,5751"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,3111,3221,3331,8082,10384,10476,10587,10722,10834,10965,11046,11145,11233,11326,11436,11555,11659,11788,11915,12033,12194,12312,12421,12534,12648,12737,12831,12948,13076,13179,13278,13379,13505,13637,13739,14036,14261,23645,23883,24064,24418,24496,24576,24673,24770,24863,24959,25042,25142,25238,25336,25452,25530,25630", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "3106,3216,3326,3441,8153,10471,10582,10717,10829,10960,11041,11140,11228,11321,11431,11550,11654,11783,11910,12028,12189,12307,12416,12529,12643,12732,12826,12943,13071,13174,13273,13374,13500,13632,13734,13839,14107,14335,23722,23958,24154,24491,24571,24668,24765,24858,24954,25037,25137,25233,25331,25447,25525,25625,25719"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-fa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8158,8266,8423,8552,8655,8795,8921,9035,9292,9445,9555,9725,9856,10009,10165,10231,10299", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "8261,8418,8547,8650,8790,8916,9030,9132,9440,9550,9720,9851,10004,10160,10226,10294,10379"}}]}]}