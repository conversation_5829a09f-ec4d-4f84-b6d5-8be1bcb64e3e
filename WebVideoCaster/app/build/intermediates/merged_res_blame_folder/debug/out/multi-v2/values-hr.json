{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-hr/values-hr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-hr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,252,325,378,457,535,588,720,800,884,1002,1089,1158,1238,1333,1380,1444,1511,1585,1631,1679,1723,1770,1814,1875,1939,2014,2081,2149,2192,2260,2328,2395,2470,2551,2613,2674,2743,2792", "endColumns": "43,72,52,78,77,52,131,79,83,117,86,68,79,94,46,63,66,73,45,47,43,46,43,60,63,74,66,67,42,67,67,66,74,80,61,60,68,48,66", "endOffsets": "251,324,377,456,534,587,719,799,883,1001,1088,1157,1237,1332,1379,1443,1510,1584,1630,1678,1722,1769,1813,1874,1938,2013,2080,2148,2191,2259,2327,2394,2469,2550,2612,2673,2742,2791,2858"}, "to": {"startLines": "50,51,52,53,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4704,4752,4829,4886,4969,5051,5179,5315,5399,5487,5609,5700,5773,5857,5956,6007,6075,6146,6224,6274,6650,6698,6749,6797,6862,6930,7009,7080,7152,7199,7271,7343,7414,7493,7578,7644,7709,7782,16165", "endColumns": "47,76,56,82,81,56,135,83,87,121,90,72,83,98,50,67,70,77,49,51,47,50,47,64,67,78,70,71,46,71,71,70,78,84,65,64,72,52,70", "endOffsets": "4747,4824,4881,4964,5046,5103,5310,5394,5482,5604,5695,5768,5852,5951,6002,6070,6141,6219,6269,6321,6693,6744,6792,6857,6925,7004,7075,7147,7194,7266,7338,7409,7488,7573,7639,7704,7777,7830,16231"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "96,97,148,149,151,157,158,250,251,252,253,255,256,259,263,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8145,8250,14152,14246,14426,14947,15026,23650,23745,23830,23902,24053,24134,24395,24756,24835,24905", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "8245,8332,14241,14340,14511,15021,15114,23740,23825,23897,23968,24129,24215,24463,24830,24900,25018"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-hr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "99,100,101,102,103,104,105,106,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8412,8518,8675,8805,8915,9072,9202,9317,9556,9706,9813,9970,10098,10245,10388,10456,10518", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "8513,8670,8800,8910,9067,9197,9312,9419,9701,9808,9965,10093,10240,10383,10451,10513,10594"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-hr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "9424", "endColumns": "131", "endOffsets": "9551"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2945,3036,3114,3170,3225,3291,3365,3443,3531,3613,3685,3762,3842,3916,4023,4116,4189,4281,4377,4451,4527,4623,4675,4757,4824,4911,4998,5060,5124,5187,5257,5363,5479,5576,5690,5750,5809", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2940,3031,3109,3165,3220,3286,3360,3438,3526,3608,3680,3757,3837,3911,4018,4111,4184,4276,4372,4446,4522,4618,4670,4752,4819,4906,4993,5055,5119,5182,5252,5358,5474,5571,5685,5745,5804,5884"}, "to": {"startLines": "2,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3547,3625,3703,3788,3885,7835,7931,8061,14516,14661,14879,15119,15182,15290,15350,15416,15472,15543,15603,15657,15783,15840,15902,15956,16031,20180,20265,20346,20483,20567,20653,20786,20877,20955,21011,21066,21132,21206,21284,21372,21454,21526,21603,21683,21757,21864,21957,22030,22122,22218,22292,22368,22464,22516,22598,22665,22752,22839,22901,22965,23028,23098,23204,23320,23417,23531,23591,23973", "endLines": "6,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "365,3620,3698,3783,3880,3973,7926,8056,8140,14579,14752,14942,15177,15285,15345,15411,15467,15538,15598,15652,15778,15835,15897,15951,16026,16160,20260,20341,20478,20562,20648,20781,20872,20950,21006,21061,21127,21201,21279,21367,21449,21521,21598,21678,21752,21859,21952,22025,22117,22213,22287,22363,22459,22511,22593,22660,22747,22834,22896,22960,23023,23093,23199,23315,23412,23526,23586,23645,24048"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "43,44,45,46,47,48,49,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3978,4076,4183,4280,4379,4483,4587,24550", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "4071,4178,4275,4374,4478,4582,4699,24646"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,404,521,596,686,794,931,1046,1187,1268,1364,1455,1549,1664,1786,1887,2019,2150,2280,2444,2566,2686,2811,2932,3024,3118,3244,3374,3467,3565,3670,3806,3949,4054,4149,4230,4307,4397,4479,4584,4668,4747,4840,4937,5026,5125,5209,5310,5403,5499,5633,5719,5815", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "166,283,399,516,591,681,789,926,1041,1182,1263,1359,1450,1544,1659,1781,1882,2014,2145,2275,2439,2561,2681,2806,2927,3019,3113,3239,3369,3462,3560,3665,3801,3944,4049,4144,4225,4302,4392,4474,4579,4663,4742,4835,4932,5021,5120,5204,5305,5398,5494,5628,5714,5810,5898"}, "to": {"startLines": "34,35,36,37,98,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,150,153,257,260,262,266,267,268,269,270,271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3081,3197,3314,3430,8337,10599,10689,10797,10934,11049,11190,11271,11367,11458,11552,11667,11789,11890,12022,12153,12283,12447,12569,12689,12814,12935,13027,13121,13247,13377,13470,13568,13673,13809,13952,14057,14345,14584,24220,24468,24651,25023,25107,25186,25279,25376,25465,25564,25648,25749,25842,25938,26072,26158,26254", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "3192,3309,3425,3542,8407,10684,10792,10929,11044,11185,11266,11362,11453,11547,11662,11784,11885,12017,12148,12278,12442,12564,12684,12809,12930,13022,13116,13242,13372,13465,13563,13668,13804,13947,14052,14147,14421,14656,24305,24545,24751,25102,25181,25274,25371,25460,25559,25643,25744,25837,25933,26067,26153,26249,26337"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-hr/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,439,507", "endColumns": "66,84,88,67,65", "endOffsets": "264,349,438,506,572"}, "to": {"startLines": "56,71,72,73,74", "startColumns": "4,4,4,4,4", "startOffsets": "5108,6326,6415,6508,6580", "endColumns": "70,88,92,71,69", "endOffsets": "5174,6410,6503,6575,6645"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,24310", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,24390"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,276,380,488,600,722,829,925,1014,1192,1367,1542,1717,1884,2060,2231,2347,2447,2553,2651,2744,2840,2933,3048,3165,3251,3341,3427,3531,3636,3729,3832,3941,4027", "endColumns": "121,98,103,107,111,121,106,95,88,177,174,174,174,166,175,170,115,99,105,97,92,95,92,114,116,85,89,85,103,104,92,102,108,85,93", "endOffsets": "172,271,375,483,595,717,824,920,1009,1187,1362,1537,1712,1879,2055,2226,2342,2442,2548,2646,2739,2835,2928,3043,3160,3246,3336,3422,3526,3631,3724,3827,3936,4022,4116"}, "to": {"startLines": "155,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14757,16236,16335,16439,16547,16659,16781,16888,16984,17073,17251,17426,17601,17776,17943,18119,18290,18406,18506,18612,18710,18803,18899,18992,19107,19224,19310,19400,19486,19590,19695,19788,19891,20000,20086", "endColumns": "121,98,103,107,111,121,106,95,88,177,174,174,174,166,175,170,115,99,105,97,92,95,92,114,116,85,89,85,103,104,92,102,108,85,93", "endOffsets": "14874,16330,16434,16542,16654,16776,16883,16979,17068,17246,17421,17596,17771,17938,18114,18285,18401,18501,18607,18705,18798,18894,18987,19102,19219,19305,19395,19481,19585,19690,19783,19886,19995,20081,20175"}}]}]}