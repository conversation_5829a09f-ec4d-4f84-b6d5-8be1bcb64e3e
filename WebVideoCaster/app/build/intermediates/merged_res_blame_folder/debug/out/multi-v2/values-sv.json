{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-sv/values-sv.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3938,4033,4135,4233,4332,4440,4545,24213", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "4028,4130,4228,4327,4435,4540,4661,24309"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-sv/values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1082,1174,1238,1298,1390,1455,1518,1580,1647,1711,1765,1870,1929,1990,2044,2113,2232,2315,2399,2535,2614,2698,2820,2906,2984,3038,3089,3155,3224,3298,3387,3463,3535,3612,3683,3757,3868,3959,4038,4125,4213,4285,4359,4444,4495,4574,4641,4722,4806,4868,4932,4995,5063,5170,5269,5368,5463,5521,5576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "261,360,452,533,635,715,813,935,1014,1077,1169,1233,1293,1385,1450,1513,1575,1642,1706,1760,1865,1924,1985,2039,2108,2227,2310,2394,2530,2609,2693,2815,2901,2979,3033,3084,3150,3219,3293,3382,3458,3530,3607,3678,3752,3863,3954,4033,4120,4208,4280,4354,4439,4490,4569,4636,4717,4801,4863,4927,4990,5058,5165,5264,5363,5458,5516,5571,5649"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3484,3583,3675,3756,3858,7765,7863,7985,14400,14537,14740,14968,15028,15120,15185,15248,15310,15377,15441,15495,15600,15659,15720,15774,15843,20007,20090,20174,20310,20389,20473,20595,20681,20759,20813,20864,20930,20999,21073,21162,21238,21310,21387,21458,21532,21643,21734,21813,21900,21988,22060,22134,22219,22270,22349,22416,22497,22581,22643,22707,22770,22838,22945,23044,23143,23238,23296,23649", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "311,3578,3670,3751,3853,3933,7858,7980,8059,14458,14624,14799,15023,15115,15180,15243,15305,15372,15436,15490,15595,15654,15715,15769,15838,15957,20085,20169,20305,20384,20468,20590,20676,20754,20808,20859,20925,20994,21068,21157,21233,21305,21382,21453,21527,21638,21729,21808,21895,21983,22055,22129,22214,22265,22344,22411,22492,22576,22638,22702,22765,22833,22940,23039,23138,23233,23291,23346,23722"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8064,8157,14041,14137,14312,14804,14880,23351,23440,23521,23585,23727,23813,24063,24411,24489,24556", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "8152,8240,14132,14231,14395,14875,14963,23435,23516,23580,23644,23808,23898,24127,24484,24551,24671"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-sv/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9319", "endColumns": "147", "endOffsets": "9462"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,260,359,459,564,700,811,910,996,1168,1350,1538,1730,1910,2098,2285,2404,2497,2599,2695,2787,2882,2973,3090,3202,3285,3372,3455,3551,3650,3743,3848,3966,4052", "endColumns": "110,93,98,99,104,135,110,98,85,171,181,187,191,179,187,186,118,92,101,95,91,94,90,116,111,82,86,82,95,98,92,104,117,85,93", "endOffsets": "161,255,354,454,559,695,806,905,991,1163,1345,1533,1725,1905,2093,2280,2399,2492,2594,2690,2782,2877,2968,3085,3197,3280,3367,3450,3546,3645,3738,3843,3961,4047,4141"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14629,16027,16121,16220,16320,16425,16561,16672,16771,16857,17029,17211,17399,17591,17771,17959,18146,18265,18358,18460,18556,18648,18743,18834,18951,19063,19146,19233,19316,19412,19511,19604,19709,19827,19913", "endColumns": "110,93,98,99,104,135,110,98,85,171,181,187,191,179,187,186,118,92,101,95,91,94,90,116,111,82,86,82,95,98,92,104,117,85,93", "endOffsets": "14735,16116,16215,16315,16420,16556,16667,16766,16852,17024,17206,17394,17586,17766,17954,18141,18260,18353,18455,18551,18643,18738,18829,18946,19058,19141,19228,19311,19407,19506,19599,19704,19822,19908,20002"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-sv/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,317,371,457,526,578,709,791,868,984,1066,1134,1215,1314,1358,1424,1494,1568,1613,1663,1704,1748,1799,1866,1937,2002,2065,2133,2174,2243,2305,2370,2450,2530,2591,2648,2721,2771", "endColumns": "44,63,53,85,68,51,130,81,76,115,81,67,80,98,43,65,69,73,44,49,40,43,50,66,70,64,62,67,40,68,61,64,79,79,60,56,72,49,60", "endOffsets": "252,316,370,456,525,577,708,790,867,983,1065,1133,1214,1313,1357,1423,1493,1567,1612,1662,1703,1747,1798,1865,1936,2001,2064,2132,2173,2242,2304,2369,2449,2529,2590,2647,2720,2770,2831"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4666,4715,4783,4841,4931,5004,5136,5271,5357,5438,5558,5644,5716,5801,5904,5952,6022,6096,6174,6223,6585,6630,6678,6733,6804,6879,6948,7015,7087,7132,7205,7271,7340,7424,7508,7573,7634,7711,15962", "endColumns": "48,67,57,89,72,55,134,85,80,119,85,71,84,102,47,69,73,77,48,53,44,47,54,70,74,68,66,71,44,72,65,68,83,83,64,60,76,53,64", "endOffsets": "4710,4778,4836,4926,4999,5055,5266,5352,5433,5553,5639,5711,5796,5899,5947,6017,6091,6169,6218,6272,6625,6673,6728,6799,6874,6943,7010,7082,7127,7200,7266,7335,7419,7503,7568,7629,7706,7760,16022"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,819,932,1009,1084,1177,1272,1367,1461,1563,1658,1755,1853,1949,2042,2122,2228,2327,2423,2528,2631,2733,2887,23983", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,814,927,1004,1079,1172,1267,1362,1456,1558,1653,1750,1848,1944,2037,2117,2223,2322,2418,2523,2626,2728,2882,2984,24058"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,319,422,550,630,722,832,972,1092,1243,1324,1420,1506,1596,1706,1824,1925,2054,2177,2308,2476,2600,2714,2837,2954,3041,3135,3249,3384,3476,3580,3679,3807,3946,4048,4140,4216,4290,4370,4451,4548,4624,4705,4803,4899,4994,5091,5174,5274,5371,5470,5588,5664,5764", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "185,314,417,545,625,717,827,967,1087,1238,1319,1415,1501,1591,1701,1819,1920,2049,2172,2303,2471,2595,2709,2832,2949,3036,3130,3244,3379,3471,3575,3674,3802,3941,4043,4135,4211,4285,4365,4446,4543,4619,4700,4798,4894,4989,5086,5169,5269,5366,5465,5583,5659,5759,5854"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2989,3124,3253,3356,8245,10531,10623,10733,10873,10993,11144,11225,11321,11407,11497,11607,11725,11826,11955,12078,12209,12377,12501,12615,12738,12855,12942,13036,13150,13285,13377,13481,13580,13708,13847,13949,14236,14463,23903,24132,24314,24676,24752,24833,24931,25027,25122,25219,25302,25402,25499,25598,25716,25792,25892", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "3119,3248,3351,3479,8320,10618,10728,10868,10988,11139,11220,11316,11402,11492,11602,11720,11821,11950,12073,12204,12372,12496,12610,12733,12850,12937,13031,13145,13280,13372,13476,13575,13703,13842,13944,14036,14307,14532,23978,24208,24406,24747,24828,24926,25022,25117,25214,25297,25397,25494,25593,25711,25787,25887,25982"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-sv/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8325,8432,8589,8716,8826,8967,9092,9215,9467,9615,9723,9885,10013,10167,10323,10389,10452", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "8427,8584,8711,8821,8962,9087,9210,9314,9610,9718,9880,10008,10162,10318,10384,10447,10526"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-sv/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,270,349,429,497", "endColumns": "71,78,79,67,64", "endOffsets": "269,348,428,496,561"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5060,6277,6360,6444,6516", "endColumns": "75,82,83,71,68", "endOffsets": "5131,6355,6439,6511,6580"}}]}]}