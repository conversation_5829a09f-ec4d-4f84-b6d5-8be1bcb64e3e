{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-in/values-in.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-in/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9259", "endColumns": "131", "endOffsets": "9386"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3878,3973,4075,4172,4269,4375,4493,24344", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3968,4070,4167,4264,4370,4488,4603,24440"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-in/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,252,320,379,465,537,594,721,811,890,1001,1090,1157,1233,1333,1375,1434,1496,1570,1615,1657,1697,1737,1786,1852,1921,1985,2050,2115,2158,2232,2295,2359,2440,2521,2587,2645,2715,2760", "endColumns": "43,67,58,85,71,56,126,89,78,110,88,66,75,99,41,58,61,73,44,41,39,39,48,65,68,63,64,64,42,73,62,63,80,80,65,57,69,44,60", "endOffsets": "251,319,378,464,536,593,720,810,889,1000,1089,1156,1232,1332,1374,1433,1495,1569,1614,1656,1696,1736,1785,1851,1920,1984,2049,2114,2157,2231,2294,2358,2439,2520,2586,2644,2714,2759,2820"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4608,4656,4728,4791,4881,4957,5089,5220,5314,5397,5512,5605,5676,5756,5860,5906,5969,6035,6113,6162,6523,6567,6611,6664,6734,6807,6875,6944,7013,7060,7138,7205,7273,7358,7443,7513,7575,7649,16007", "endColumns": "47,71,62,89,75,60,130,93,82,114,92,70,79,103,45,62,65,77,48,45,43,43,52,69,72,67,68,68,46,77,66,67,84,84,69,61,73,48,64", "endOffsets": "4651,4723,4786,4876,4952,5013,5215,5309,5392,5507,5600,5671,5751,5855,5901,5964,6030,6108,6157,6203,6562,6606,6659,6729,6802,6870,6939,7008,7055,7133,7200,7268,7353,7438,7508,7570,7644,7693,16067"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-in/values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2807,2897,2979,3032,3084,3150,3222,3306,3389,3469,3544,3620,3693,3768,3866,3951,4026,4118,4212,4286,4359,4453,4505,4587,4656,4741,4828,4890,4954,5017,5089,5192,5297,5392,5495,5552,5608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,134,89,81,52,51,65,71,83,82,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2802,2892,2974,3027,3079,3145,3217,3301,3384,3464,3539,3615,3688,3763,3861,3946,4021,4113,4207,4281,4354,4448,4500,4582,4651,4736,4823,4885,4949,5012,5084,5187,5292,5387,5490,5547,5603,5683"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3469,3548,3624,3703,3793,7698,7804,7920,14406,14548,14774,15013,15072,15159,15221,15283,15343,15409,15471,15525,15637,15694,15755,15809,15881,20060,20146,20230,20369,20450,20531,20666,20756,20838,20891,20943,21009,21081,21165,21248,21328,21403,21479,21552,21627,21725,21810,21885,21977,22071,22145,22218,22312,22364,22446,22515,22600,22687,22749,22813,22876,22948,23051,23156,23251,23354,23411,23776", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,134,89,81,52,51,65,71,83,82,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79", "endOffsets": "314,3543,3619,3698,3788,3873,7799,7915,7998,14466,14637,14834,15067,15154,15216,15278,15338,15404,15466,15520,15632,15689,15750,15804,15876,16002,20141,20225,20364,20445,20526,20661,20751,20833,20886,20938,21004,21076,21160,21243,21323,21398,21474,21547,21622,21720,21805,21880,21972,22066,22140,22213,22307,22359,22441,22510,22595,22682,22744,22808,22871,22943,23046,23151,23246,23349,23406,23462,23851"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,187,288,395,507,616,732,842,939,1030,1216,1393,1573,1754,1927,2112,2296,2414,2509,2617,2713,2808,2908,3001,3119,3238,3320,3403,3489,3592,3694,3786,3891,3993,4079", "endColumns": "131,100,106,111,108,115,109,96,90,185,176,179,180,172,184,183,117,94,107,95,94,99,92,117,118,81,82,85,102,101,91,104,101,85,95", "endOffsets": "182,283,390,502,611,727,837,934,1025,1211,1388,1568,1749,1922,2107,2291,2409,2504,2612,2708,2803,2903,2996,3114,3233,3315,3398,3484,3587,3689,3781,3886,3988,4074,4170"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14642,16072,16173,16280,16392,16501,16617,16727,16824,16915,17101,17278,17458,17639,17812,17997,18181,18299,18394,18502,18598,18693,18793,18886,19004,19123,19205,19288,19374,19477,19579,19671,19776,19878,19964", "endColumns": "131,100,106,111,108,115,109,96,90,185,176,179,180,172,184,183,117,94,107,95,94,99,92,117,118,81,82,85,102,101,91,104,101,85,95", "endOffsets": "14769,16168,16275,16387,16496,16612,16722,16819,16910,17096,17273,17453,17634,17807,17992,18176,18294,18389,18497,18593,18688,18788,18881,18999,19118,19200,19283,19369,19472,19574,19666,19771,19873,19959,20055"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,643,730,834,950,1033,1111,1202,1295,1390,1484,1584,1677,1772,1866,1957,2048,2134,2237,2342,2443,2547,2656,2764,2924,24106", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,638,725,829,945,1028,1106,1197,1290,1385,1479,1579,1672,1767,1861,1952,2043,2129,2232,2337,2438,2542,2651,2759,2919,3018,24186"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-in/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8253,8360,8524,8650,8756,8911,9038,9153,9391,9557,9662,9826,9952,10107,10251,10315,10375", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "8355,8519,8645,8751,8906,9033,9148,9254,9552,9657,9821,9947,10102,10246,10310,10370,10449"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8003,8093,14050,14148,14320,14839,14922,23467,23554,23639,23709,23856,23938,24191,24540,24618,24684", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "8088,8170,14143,14243,14401,14917,15008,23549,23634,23704,23771,23933,24016,24258,24613,24679,24798"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,281,389,501,579,681,801,944,1063,1209,1292,1392,1481,1581,1695,1811,1916,2052,2186,2311,2485,2603,2719,2839,2959,3049,3146,3262,3391,3489,3592,3695,3832,3975,4080,4175,4247,4324,4409,4490,4585,4661,4740,4835,4930,5023,5119,5202,5301,5396,5495,5608,5684,5785", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "165,276,384,496,574,676,796,939,1058,1204,1287,1387,1476,1576,1690,1806,1911,2047,2181,2306,2480,2598,2714,2834,2954,3044,3141,3257,3386,3484,3587,3690,3827,3970,4075,4170,4242,4319,4404,4485,4580,4656,4735,4830,4925,5018,5114,5197,5296,5391,5490,5603,5679,5780,5871"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3023,3138,3249,3357,8175,10454,10556,10676,10819,10938,11084,11167,11267,11356,11456,11570,11686,11791,11927,12061,12186,12360,12478,12594,12714,12834,12924,13021,13137,13266,13364,13467,13570,13707,13850,13955,14248,14471,24021,24263,24445,24803,24879,24958,25053,25148,25241,25337,25420,25519,25614,25713,25826,25902,26003", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "3133,3244,3352,3464,8248,10551,10671,10814,10933,11079,11162,11262,11351,11451,11565,11681,11786,11922,12056,12181,12355,12473,12589,12709,12829,12919,13016,13132,13261,13359,13462,13565,13702,13845,13950,14045,14315,14543,24101,24339,24535,24874,24953,25048,25143,25236,25332,25415,25514,25609,25708,25821,25897,25998,26089"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-in/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,343,426,494", "endColumns": "66,77,82,67,69", "endOffsets": "264,342,425,493,563"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5018,6208,6290,6377,6449", "endColumns": "70,81,86,71,73", "endOffsets": "5084,6285,6372,6444,6518"}}]}]}