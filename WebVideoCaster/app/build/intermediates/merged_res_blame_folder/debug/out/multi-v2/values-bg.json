{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-bg/values-bg.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4017,4114,4224,4326,4427,4534,4639,25099", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "4109,4219,4321,4422,4529,4634,4753,25195"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-bg/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8617,8726,8891,9026,9137,9304,9439,9558,9805,9974,10086,10261,10399,10556,10722,10792,10851", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "8721,8886,9021,9132,9299,9434,9553,9662,9969,10081,10256,10394,10551,10717,10787,10846,10919"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-bg/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9667", "endColumns": "137", "endOffsets": "9800"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-bg/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,323,377,458,541,606,741,824,908,1027,1116,1185,1272,1376,1431,1505,1582,1656,1704,1746,1800,1842,1895,1967,2042,2116,2189,2262,2304,2389,2457,2521,2596,2677,2738,2795,2865,2909", "endColumns": "45,68,53,80,82,64,134,82,83,118,88,68,86,103,54,73,76,73,47,41,53,41,52,71,74,73,72,72,41,84,67,63,74,80,60,56,69,43,65", "endOffsets": "253,322,376,457,540,605,740,823,907,1026,1115,1184,1271,1375,1430,1504,1581,1655,1703,1745,1799,1841,1894,1966,2041,2115,2188,2261,2303,2388,2456,2520,2595,2676,2737,2794,2864,2908,2974"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4808,4881,4939,5024,5111,5251,5390,5477,5565,5688,5781,5854,5945,6053,6112,6190,6271,6349,6401,6795,6853,6899,6956,7032,7111,7189,7266,7343,7389,7478,7550,7618,7697,7782,7847,7908,7982,16571", "endColumns": "49,72,57,84,86,68,138,86,87,122,92,72,90,107,58,77,80,77,51,45,57,45,56,75,78,77,76,76,45,88,71,67,78,84,64,60,73,47,69", "endOffsets": "4803,4876,4934,5019,5106,5175,5385,5472,5560,5683,5776,5849,5940,6048,6107,6185,6266,6344,6396,6442,6848,6894,6951,7027,7106,7184,7261,7338,7384,7473,7545,7613,7692,7777,7842,7903,7977,8025,16636"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-bg/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,357,451,519", "endColumns": "66,91,93,67,77", "endOffsets": "264,356,450,518,596"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5180,6447,6543,6641,6713", "endColumns": "70,95,97,71,81", "endOffsets": "5246,6538,6636,6708,6790"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,276,390,506,625,745,858,958,1047,1226,1410,1594,1778,1961,2142,2327,2444,2544,2650,2750,2845,2953,3048,3161,3275,3371,3464,3549,3656,3769,3862,3970,4082,4169", "endColumns": "122,97,113,115,118,119,112,99,88,178,183,183,183,182,180,184,116,99,105,99,94,107,94,112,113,95,92,84,106,112,92,107,111,86,96", "endOffsets": "173,271,385,501,620,740,853,953,1042,1221,1405,1589,1773,1956,2137,2322,2439,2539,2645,2745,2840,2948,3043,3156,3270,3366,3459,3544,3651,3764,3857,3965,4077,4164,4261"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15156,16641,16739,16853,16969,17088,17208,17321,17421,17510,17689,17873,18057,18241,18424,18605,18790,18907,19007,19113,19213,19308,19416,19511,19624,19738,19834,19927,20012,20119,20232,20325,20433,20545,20632", "endColumns": "122,97,113,115,118,119,112,99,88,178,183,183,183,182,180,184,116,99,105,99,94,107,94,112,113,95,92,84,106,112,92,107,111,86,96", "endOffsets": "15274,16734,16848,16964,17083,17203,17316,17416,17505,17684,17868,18052,18236,18419,18600,18785,18902,19002,19108,19208,19303,19411,19506,19619,19733,19829,19922,20007,20114,20227,20320,20428,20540,20627,20724"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-bg/values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1121,1218,1298,1360,1449,1512,1577,1636,1709,1772,1826,1954,2011,2073,2127,2200,2343,2427,2515,2651,2739,2827,2963,3048,3125,3178,3229,3295,3370,3446,3532,3611,3688,3764,3841,3915,4027,4118,4193,4284,4376,4450,4537,4628,4683,4765,4831,4914,5000,5062,5126,5189,5259,5376,5488,5599,5709,5766,5821", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "260,339,418,501,623,733,828,961,1050,1116,1213,1293,1355,1444,1507,1572,1631,1704,1767,1821,1949,2006,2068,2122,2195,2338,2422,2510,2646,2734,2822,2958,3043,3120,3173,3224,3290,3365,3441,3527,3606,3683,3759,3836,3910,4022,4113,4188,4279,4371,4445,4532,4623,4678,4760,4826,4909,4995,5057,5121,5184,5254,5371,5483,5594,5704,5761,5816,5902"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3544,3623,3702,3785,3907,8030,8125,8258,14916,15059,15279,15526,15588,15677,15740,15805,15864,15937,16000,16054,16182,16239,16301,16355,16428,20729,20813,20901,21037,21125,21213,21349,21434,21511,21564,21615,21681,21756,21832,21918,21997,22074,22150,22227,22301,22413,22504,22579,22670,22762,22836,22923,23014,23069,23151,23217,23300,23386,23448,23512,23575,23645,23762,23874,23985,24095,24152,24515", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "310,3618,3697,3780,3902,4012,8120,8253,8342,14977,15151,15354,15583,15672,15735,15800,15859,15932,15995,16049,16177,16234,16296,16350,16423,16566,20808,20896,21032,21120,21208,21344,21429,21506,21559,21610,21676,21751,21827,21913,21992,22069,22145,22222,22296,22408,22499,22574,22665,22757,22831,22918,23009,23064,23146,23212,23295,23381,23443,23507,23570,23640,23757,23869,23980,24090,24147,24202,24596"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,24857", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,24936"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1000,1066,1150,1238,1310,1393,1462", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,995,1061,1145,1233,1305,1388,1457,1578"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8347,8450,14543,14646,14832,15359,15435,24207,24298,24382,24449,24601,24685,24941,25305,25388,25457", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "8445,8538,14641,14744,14911,15430,15521,24293,24377,24444,24510,24680,24768,25008,25383,25452,25573"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,420,535,609,701,811,937,1054,1197,1277,1372,1464,1556,1665,1790,1889,2024,2159,2300,2484,2620,2743,2870,3001,3094,3187,3320,3451,3546,3647,3748,3884,4026,4130,4228,4311,4388,4472,4558,4663,4739,4818,4914,5015,5107,5201,5285,5391,5487,5586,5701,5777,5883", "endColumns": "114,116,132,114,73,91,109,125,116,142,79,94,91,91,108,124,98,134,134,140,183,135,122,126,130,92,92,132,130,94,100,100,135,141,103,97,82,76,83,85,104,75,78,95,100,91,93,83,105,95,98,114,75,105,92", "endOffsets": "165,282,415,530,604,696,806,932,1049,1192,1272,1367,1459,1551,1660,1785,1884,2019,2154,2295,2479,2615,2738,2865,2996,3089,3182,3315,3446,3541,3642,3743,3879,4021,4125,4223,4306,4383,4467,4553,4658,4734,4813,4909,5010,5102,5196,5280,5386,5482,5581,5696,5772,5878,5971"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3064,3179,3296,3429,8543,10924,11016,11126,11252,11369,11512,11592,11687,11779,11871,11980,12105,12204,12339,12474,12615,12799,12935,13058,13185,13316,13409,13502,13635,13766,13861,13962,14063,14199,14341,14445,14749,14982,24773,25013,25200,25578,25654,25733,25829,25930,26022,26116,26200,26306,26402,26501,26616,26692,26798", "endColumns": "114,116,132,114,73,91,109,125,116,142,79,94,91,91,108,124,98,134,134,140,183,135,122,126,130,92,92,132,130,94,100,100,135,141,103,97,82,76,83,85,104,75,78,95,100,91,93,83,105,95,98,114,75,105,92", "endOffsets": "3174,3291,3424,3539,8612,11011,11121,11247,11364,11507,11587,11682,11774,11866,11975,12100,12199,12334,12469,12610,12794,12930,13053,13180,13311,13404,13497,13630,13761,13856,13957,14058,14194,14336,14440,14538,14827,15054,24852,25094,25300,25649,25728,25824,25925,26017,26111,26195,26301,26397,26496,26611,26687,26793,26886"}}]}]}