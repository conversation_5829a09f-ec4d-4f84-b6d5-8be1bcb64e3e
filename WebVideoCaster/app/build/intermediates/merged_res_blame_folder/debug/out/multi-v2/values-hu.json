{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-hu/values-hu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,24722", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,24801"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-hu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8354,8465,8649,8787,8896,9064,9202,9324,9611,9781,9889,10074,10211,10383,10555,10626,10694", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "8460,8644,8782,8891,9059,9197,9319,9429,9776,9884,10069,10206,10378,10550,10621,10689,10777"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-hu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9434", "endColumns": "176", "endOffsets": "9606"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3916,4013,4115,4217,4318,4421,4528,24963", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "4008,4110,4212,4313,4416,4523,4633,25059"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-hu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,321,376,460,543,597,727,813,891,1001,1087,1154,1235,1333,1383,1453,1527,1601,1645,1687,1736,1780,1830,1900,1969,2028,2092,2153,2197,2269,2331,2395,2472,2556,2618,2675,2746,2800", "endColumns": "46,65,54,83,82,53,129,85,77,109,85,66,80,97,49,69,73,73,43,41,48,43,49,69,68,58,63,60,43,71,61,63,76,83,61,56,70,53,64", "endOffsets": "254,320,375,459,542,596,726,812,890,1000,1086,1153,1234,1332,1382,1452,1526,1600,1644,1686,1735,1779,1829,1899,1968,2027,2091,2152,2196,2268,2330,2394,2471,2555,2617,2674,2745,2799,2864"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4638,4689,4759,4818,4906,4993,5122,5256,5346,5428,5542,5632,5703,5788,5890,5944,6018,6096,6174,6222,6604,6657,6705,6759,6833,6906,6969,7037,7102,7150,7226,7292,7360,7441,7529,7595,7656,7731,16415", "endColumns": "50,69,58,87,86,57,133,89,81,113,89,70,84,101,53,73,77,77,47,45,52,47,53,73,72,62,67,64,47,75,65,67,80,87,65,60,74,57,68", "endOffsets": "4684,4754,4813,4901,4988,5046,5251,5341,5423,5537,5627,5698,5783,5885,5939,6013,6091,6169,6217,6263,6652,6700,6754,6828,6901,6964,7032,7097,7145,7221,7287,7355,7436,7524,7590,7651,7726,7784,16479"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1033,1132,1207,1266,1376,1438,1507,1565,1637,1698,1753,1856,1913,1973,2028,2109,2229,2312,2400,2535,2618,2698,2838,2932,3014,3067,3118,3184,3260,3342,3428,3512,3589,3664,3743,3820,3925,4021,4098,4190,4287,4361,4446,4543,4595,4678,4745,4833,4920,4982,5046,5109,5175,5273,5379,5473,5580,5637,5692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "258,339,415,492,582,662,761,881,964,1028,1127,1202,1261,1371,1433,1502,1560,1632,1693,1748,1851,1908,1968,2023,2104,2224,2307,2395,2530,2613,2693,2833,2927,3009,3062,3113,3179,3255,3337,3423,3507,3584,3659,3738,3815,3920,4016,4093,4185,4282,4356,4441,4538,4590,4673,4740,4828,4915,4977,5041,5104,5170,5268,5374,5468,5575,5632,5687,5772"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3512,3593,3669,3746,3836,7789,7888,8008,14771,14911,15140,15393,15452,15562,15624,15693,15751,15823,15884,15939,16042,16099,16159,16214,16295,20615,20698,20786,20921,21004,21084,21224,21318,21400,21453,21504,21570,21646,21728,21814,21898,21975,22050,22129,22206,22311,22407,22484,22576,22673,22747,22832,22929,22981,23064,23131,23219,23306,23368,23432,23495,23561,23659,23765,23859,23966,24023,24382", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "308,3588,3664,3741,3831,3911,7883,8003,8086,14830,15005,15210,15447,15557,15619,15688,15746,15818,15879,15934,16037,16094,16154,16209,16290,16410,20693,20781,20916,20999,21079,21219,21313,21395,21448,21499,21565,21641,21723,21809,21893,21970,22045,22124,22201,22306,22402,22479,22571,22668,22742,22827,22924,22976,23059,23126,23214,23301,23363,23427,23490,23556,23654,23760,23854,23961,24018,24073,24462"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8091,8186,14408,14505,14684,15215,15297,24078,24167,24254,24318,24467,24550,24806,25161,25240,25306", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "8181,8269,14500,14599,14766,15292,15388,24162,24249,24313,24377,24545,24633,24875,25235,25301,25422"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,282,391,498,607,720,828,925,1014,1197,1396,1599,1804,2001,2206,2406,2531,2625,2740,2838,2937,3032,3126,3232,3355,3439,3526,3613,3717,3820,3915,4018,4133,4221", "endColumns": "129,96,108,106,108,112,107,96,88,182,198,202,204,196,204,199,124,93,114,97,98,94,93,105,122,83,86,86,103,102,94,102,114,87,94", "endOffsets": "180,277,386,493,602,715,823,920,1009,1192,1391,1594,1799,1996,2201,2401,2526,2620,2735,2833,2932,3027,3121,3227,3350,3434,3521,3608,3712,3815,3910,4013,4128,4216,4311"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15010,16484,16581,16690,16797,16906,17019,17127,17224,17313,17496,17695,17898,18103,18300,18505,18705,18830,18924,19039,19137,19236,19331,19425,19531,19654,19738,19825,19912,20016,20119,20214,20317,20432,20520", "endColumns": "129,96,108,106,108,112,107,96,88,182,198,202,204,196,204,199,124,93,114,97,98,94,93,105,122,83,86,86,103,102,94,102,114,87,94", "endOffsets": "15135,16576,16685,16792,16901,17014,17122,17219,17308,17491,17690,17893,18098,18295,18500,18700,18825,18919,19034,19132,19231,19326,19420,19526,19649,19733,19820,19907,20011,20114,20209,20312,20427,20515,20610"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,284,388,500,580,673,784,920,1039,1182,1263,1359,1453,1551,1669,1802,1903,2039,2173,2295,2487,2606,2724,2843,2974,3069,3160,3275,3399,3497,3602,3708,3848,3991,4094,4206,4286,4362,4446,4529,4626,4703,4782,4877,4979,5070,5162,5244,5349,5443,5537,5673,5750,5858", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "167,279,383,495,575,668,779,915,1034,1177,1258,1354,1448,1546,1664,1797,1898,2034,2168,2290,2482,2601,2719,2838,2969,3064,3155,3270,3394,3492,3597,3703,3843,3986,4089,4201,4281,4357,4441,4524,4621,4698,4777,4872,4974,5065,5157,5239,5344,5438,5532,5668,5745,5853,5947"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3067,3184,3296,3400,8274,10782,10875,10986,11122,11241,11384,11465,11561,11655,11753,11871,12004,12105,12241,12375,12497,12689,12808,12926,13045,13176,13271,13362,13477,13601,13699,13804,13910,14050,14193,14296,14604,14835,24638,24880,25064,25427,25504,25583,25678,25780,25871,25963,26045,26150,26244,26338,26474,26551,26659", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "3179,3291,3395,3507,8349,10870,10981,11117,11236,11379,11460,11556,11650,11748,11866,11999,12100,12236,12370,12492,12684,12803,12921,13040,13171,13266,13357,13472,13596,13694,13799,13905,14045,14188,14291,14403,14679,14906,24717,24958,25156,25499,25578,25673,25775,25866,25958,26040,26145,26239,26333,26469,26546,26654,26748"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-hu/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,358,452,520", "endColumns": "66,92,93,67,64", "endOffsets": "264,357,451,519,584"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5051,6268,6365,6463,6535", "endColumns": "70,96,97,71,68", "endOffsets": "5117,6360,6458,6530,6599"}}]}]}