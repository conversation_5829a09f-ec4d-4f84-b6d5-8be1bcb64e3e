{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-fr/values-fr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,273,370,470,572,698,811,914,1000,1179,1364,1550,1739,1926,2114,2300,2419,2519,2629,2726,2821,2916,3012,3131,3258,3341,3426,3509,3614,3718,3812,3917,4037,4124", "endColumns": "122,94,96,99,101,125,112,102,85,178,184,185,188,186,187,185,118,99,109,96,94,94,95,118,126,82,84,82,104,103,93,104,119,86,95", "endOffsets": "173,268,365,465,567,693,806,909,995,1174,1359,1545,1734,1921,2109,2295,2414,2514,2624,2721,2816,2911,3007,3126,3253,3336,3421,3504,3609,3713,3807,3912,4032,4119,4215"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15131,16644,16739,16836,16936,17038,17164,17277,17380,17466,17645,17830,18016,18205,18392,18580,18766,18885,18985,19095,19192,19287,19382,19478,19597,19724,19807,19892,19975,20080,20184,20278,20383,20503,20590", "endColumns": "122,94,96,99,101,125,112,102,85,178,184,185,188,186,187,185,118,99,109,96,94,94,95,118,126,82,84,82,104,103,93,104,119,86,95", "endOffsets": "15249,16734,16831,16931,17033,17159,17272,17375,17461,17640,17825,18011,18200,18387,18575,18761,18880,18980,19090,19187,19282,19377,19473,19592,19719,19802,19887,19970,20075,20179,20273,20378,20498,20585,20681"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-fr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8482,8588,8768,8898,9007,9178,9311,9432,9710,9888,10000,10185,10321,10481,10660,10733,10800", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "8583,8763,8893,9002,9173,9306,9427,9540,9883,9995,10180,10316,10476,10655,10728,10795,10879"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2982,3075,3154,3209,3260,3326,3406,3487,3590,3670,3743,3821,3894,3966,4078,4171,4243,4335,4427,4501,4585,4677,4734,4818,4884,4967,5054,5116,5180,5243,5321,5423,5527,5624,5728,5787,5842", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2977,3070,3149,3204,3255,3321,3401,3482,3585,3665,3738,3816,3889,3961,4073,4166,4238,4330,4422,4496,4580,4672,4729,4813,4879,4962,5049,5111,5175,5238,5316,5418,5522,5619,5723,5782,5837,5926"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3540,3620,3701,3784,3893,7907,8005,8135,14891,15034,15254,15508,15574,15676,15741,15816,15872,15951,16011,16065,16187,16246,16308,16362,16444,20686,20778,20862,21006,21085,21166,21307,21400,21479,21534,21585,21651,21731,21812,21915,21995,22068,22146,22219,22291,22403,22496,22568,22660,22752,22826,22910,23002,23059,23143,23209,23292,23379,23441,23505,23568,23646,23748,23852,23949,24053,24112,24485", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "328,3615,3696,3779,3888,3983,8000,8130,8215,14952,15126,15332,15569,15671,15736,15811,15867,15946,16006,16060,16182,16241,16303,16357,16439,16574,20773,20857,21001,21080,21161,21302,21395,21474,21529,21580,21646,21726,21807,21910,21990,22063,22141,22214,22286,22398,22491,22563,22655,22747,22821,22905,22997,23054,23138,23204,23287,23374,23436,23500,23563,23641,23743,23847,23944,24048,24107,24162,24569"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-fr/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,341,420,488", "endColumns": "66,75,78,67,64", "endOffsets": "264,340,419,487,552"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5124,6370,6450,6533,6605", "endColumns": "70,79,82,71,68", "endOffsets": "5190,6445,6528,6600,6669"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,24827", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,24909"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8220,8319,14521,14621,14804,15337,15416,24167,24259,24346,24417,24574,24655,24914,25277,25355,25424", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "8314,8402,14616,14716,14886,15411,15503,24254,24341,24412,24480,24650,24735,24985,25350,25419,25541"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3988,4086,4188,4287,4389,4493,4597,25073", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "4081,4183,4282,4384,4488,4592,4710,25169"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,285,403,520,595,685,794,934,1049,1192,1272,1369,1466,1563,1680,1803,1905,2051,2193,2319,2507,2629,2743,2863,2993,3091,3192,3312,3433,3531,3634,3735,3875,4023,4128,4232,4315,4392,4479,4562,4665,4741,4822,4920,5028,5122,5218,5302,5414,5511,5609,5737,5813,5919", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "166,280,398,515,590,680,789,929,1044,1187,1267,1364,1461,1558,1675,1798,1900,2046,2188,2314,2502,2624,2738,2858,2988,3086,3187,3307,3428,3526,3629,3730,3870,4018,4123,4227,4310,4387,4474,4557,4660,4736,4817,4915,5023,5117,5213,5297,5409,5506,5604,5732,5808,5914,6008"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3075,3191,3305,3423,8407,10884,10974,11083,11223,11338,11481,11561,11658,11755,11852,11969,12092,12194,12340,12482,12608,12796,12918,13032,13152,13282,13380,13481,13601,13722,13820,13923,14024,14164,14312,14417,14721,14957,24740,24990,25174,25546,25622,25703,25801,25909,26003,26099,26183,26295,26392,26490,26618,26694,26800", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "3186,3300,3418,3535,8477,10969,11078,11218,11333,11476,11556,11653,11750,11847,11964,12087,12189,12335,12477,12603,12791,12913,13027,13147,13277,13375,13476,13596,13717,13815,13918,14019,14159,14307,14412,14516,14799,15029,24822,25068,25272,25617,25698,25796,25904,25998,26094,26178,26290,26387,26485,26613,26689,26795,26889"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-fr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,333,390,473,541,593,723,804,888,1019,1110,1179,1261,1369,1414,1477,1540,1614,1664,1712,1757,1796,1847,1916,1985,2053,2120,2189,2231,2308,2380,2446,2525,2623,2685,2747,2820,2873", "endColumns": "45,78,56,82,67,51,129,80,83,130,90,68,81,107,44,62,62,73,49,47,44,38,50,68,68,67,66,68,41,76,71,65,78,97,61,61,72,52,60", "endOffsets": "253,332,389,472,540,592,722,803,887,1018,1109,1178,1260,1368,1413,1476,1539,1613,1663,1711,1756,1795,1846,1915,1984,2052,2119,2188,2230,2307,2379,2445,2524,2622,2684,2746,2819,2872,2933"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4715,4765,4848,4909,4996,5068,5195,5329,5414,5502,5637,5732,5805,5891,6003,6052,6119,6186,6264,6318,6674,6723,6766,6821,6894,6967,7039,7110,7183,7229,7310,7386,7456,7539,7641,7707,7773,7850,16579", "endColumns": "49,82,60,86,71,55,133,84,87,134,94,72,85,111,48,66,66,77,53,51,48,42,54,72,72,71,70,72,45,80,75,69,82,101,65,65,76,56,64", "endOffsets": "4760,4843,4904,4991,5063,5119,5324,5409,5497,5632,5727,5800,5886,5998,6047,6114,6181,6259,6313,6365,6718,6761,6816,6889,6962,7034,7105,7178,7224,7305,7381,7451,7534,7636,7702,7768,7845,7902,16639"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-fr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9545", "endColumns": "164", "endOffsets": "9705"}}]}]}