{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,3010,3103,3182,3237,3288,3354,3433,3514,3605,3685,3757,3835,3910,3982,4093,4190,4267,4365,4463,4541,4622,4722,4779,4863,4929,5012,5099,5161,5225,5288,5364,5466,5573,5670,5776,5835,5890", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,3005,3098,3177,3232,3283,3349,3428,3509,3600,3680,3752,3830,3905,3977,4088,4185,4262,4360,4458,4536,4617,4717,4774,4858,4924,5007,5094,5156,5220,5283,5359,5461,5568,5665,5771,5830,5885,5974"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3629,3736,3844,3926,4027,7990,8090,8212,15008,15151,15378,15632,15694,15786,15853,15927,15988,16067,16131,16185,16301,16360,16422,16476,16558,20841,20933,21017,21161,21240,21321,21468,21561,21640,21695,21746,21812,21891,21972,22063,22143,22215,22293,22368,22440,22551,22648,22725,22823,22921,22999,23080,23180,23237,23321,23387,23470,23557,23619,23683,23746,23822,23924,24031,24128,24234,24293,24666", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "328,3731,3839,3921,4022,4119,8085,8207,8292,15069,15243,15453,15689,15781,15848,15922,15983,16062,16126,16180,16296,16355,16417,16471,16553,16682,20928,21012,21156,21235,21316,21463,21556,21635,21690,21741,21807,21886,21967,22058,22138,22210,22288,22363,22435,22546,22643,22720,22818,22916,22994,23075,23175,23232,23316,23382,23465,23552,23614,23678,23741,23817,23919,24026,24123,24229,24288,24343,24750"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-fr-rCA/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8559,8665,8845,8975,9084,9255,9388,9509,9783,9978,10090,10275,10411,10571,10750,10823,10887", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "8660,8840,8970,9079,9250,9383,9504,9617,9973,10085,10270,10406,10566,10745,10818,10882,10966"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8297,8396,14643,14741,14921,15458,15538,24348,24440,24527,24598,24755,24836,25094,25456,25535,25604", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "8391,8479,14736,14842,15003,15533,15627,24435,24522,24593,24661,24831,24916,25165,25530,25599,25721"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,25007", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,25089"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,339,457,601,676,766,875,1013,1128,1283,1363,1460,1557,1654,1771,1902,2004,2150,2292,2424,2622,2747,2861,2981,3111,3209,3310,3430,3554,3652,3750,3851,3991,4139,4244,4348,4422,4499,4585,4667,4770,4846,4927,5020,5127,5216,5316,5400,5512,5609,5713,5831,5907,6013", "endColumns": "142,140,117,143,74,89,108,137,114,154,79,96,96,96,116,130,101,145,141,131,197,124,113,119,129,97,100,119,123,97,97,100,139,147,104,103,73,76,85,81,102,75,80,92,106,88,99,83,111,96,103,117,75,105,92", "endOffsets": "193,334,452,596,671,761,870,1008,1123,1278,1358,1455,1552,1649,1766,1897,1999,2145,2287,2419,2617,2742,2856,2976,3106,3204,3305,3425,3549,3647,3745,3846,3986,4134,4239,4343,4417,4494,4580,4662,4765,4841,4922,5015,5122,5211,5311,5395,5507,5604,5708,5826,5902,6008,6101"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3083,3226,3367,3485,8484,10971,11061,11170,11308,11423,11578,11658,11755,11852,11949,12066,12197,12299,12445,12587,12719,12917,13042,13156,13276,13406,13504,13605,13725,13849,13947,14045,14146,14286,14434,14539,14847,15074,24921,25170,25353,25726,25802,25883,25976,26083,26172,26272,26356,26468,26565,26669,26787,26863,26969", "endColumns": "142,140,117,143,74,89,108,137,114,154,79,96,96,96,116,130,101,145,141,131,197,124,113,119,129,97,100,119,123,97,97,100,139,147,104,103,73,76,85,81,102,75,80,92,106,88,99,83,111,96,103,117,75,105,92", "endOffsets": "3221,3362,3480,3624,8554,11056,11165,11303,11418,11573,11653,11750,11847,11944,12061,12192,12294,12440,12582,12714,12912,13037,13151,13271,13401,13499,13600,13720,13844,13942,14040,14141,14281,14429,14534,14638,14916,15146,25002,25247,25451,25797,25878,25971,26078,26167,26267,26351,26463,26560,26664,26782,26858,26964,27057"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4124,4222,4324,4423,4525,4629,4733,25252", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4217,4319,4418,4520,4624,4728,4842,25348"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "202,269,345,433,501", "endColumns": "66,75,87,67,64", "endOffsets": "268,344,432,500,565"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5244,6508,6588,6680,6752", "endColumns": "70,79,91,71,68", "endOffsets": "5310,6583,6675,6747,6816"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,282,384,498,605,723,845,957,1046,1225,1411,1597,1786,1970,2158,2344,2464,2564,2683,2780,2875,2970,3066,3177,3291,3374,3456,3541,3646,3761,3855,3960,4087,4174", "endColumns": "129,96,101,113,106,117,121,111,88,178,185,185,188,183,187,185,119,99,118,96,94,94,95,110,113,82,81,84,104,114,93,104,126,86,95", "endOffsets": "180,277,379,493,600,718,840,952,1041,1220,1406,1592,1781,1965,2153,2339,2459,2559,2678,2775,2870,2965,3061,3172,3286,3369,3451,3536,3641,3756,3850,3955,4082,4169,4265"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15248,16756,16853,16955,17069,17176,17294,17416,17528,17617,17796,17982,18168,18357,18541,18729,18915,19035,19135,19254,19351,19446,19541,19637,19748,19862,19945,20027,20112,20217,20332,20426,20531,20658,20745", "endColumns": "129,96,101,113,106,117,121,111,88,178,185,185,188,183,187,185,119,99,118,96,94,94,95,110,113,82,81,84,104,114,93,104,126,86,95", "endOffsets": "15373,16848,16950,17064,17171,17289,17411,17523,17612,17791,17977,18163,18352,18536,18724,18910,19030,19130,19249,19346,19441,19536,19632,19743,19857,19940,20022,20107,20212,20327,20421,20526,20653,20740,20836"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "212,258,325,382,456,533,585,727,809,893,1023,1114,1192,1274,1379,1424,1487,1550,1624,1674,1722,1769,1811,1855,1914,1973,2031,2097,2165,2207,2284,2347,2413,2492,2574,2636,2693,2766,2819", "endColumns": "45,66,56,73,76,51,141,81,83,129,90,77,81,104,44,62,62,73,49,47,46,41,43,58,58,57,65,67,41,76,62,65,78,81,61,56,72,52,64", "endOffsets": "257,324,381,455,532,584,726,808,892,1022,1113,1191,1273,1378,1423,1486,1549,1623,1673,1721,1768,1810,1854,1913,1972,2030,2096,2164,2206,2283,2346,2412,2491,2573,2635,2692,2765,2818,2883"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4847,4897,4968,5029,5107,5188,5315,5461,5547,5635,5769,5864,5946,6032,6141,6190,6257,6324,6402,6456,6821,6872,6918,6966,7029,7092,7154,7224,7296,7342,7423,7490,7560,7643,7729,7795,7856,7933,16687", "endColumns": "49,70,60,77,80,55,145,85,87,133,94,81,85,108,48,66,66,77,53,51,50,45,47,62,62,61,69,71,45,80,66,69,82,85,65,60,76,56,68", "endOffsets": "4892,4963,5024,5102,5183,5239,5456,5542,5630,5764,5859,5941,6027,6136,6185,6252,6319,6397,6451,6503,6867,6913,6961,7024,7087,7149,7219,7291,7337,7418,7485,7555,7638,7724,7790,7851,7928,7985,16751"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9622", "endColumns": "160", "endOffsets": "9778"}}]}]}