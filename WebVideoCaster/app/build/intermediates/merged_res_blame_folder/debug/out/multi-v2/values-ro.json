{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-ro/values-ro.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ro/values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2963,3053,3133,3188,3239,3305,3378,3456,3544,3629,3700,3777,3851,3923,4029,4120,4194,4289,4387,4461,4541,4642,4695,4781,4847,4936,5026,5088,5152,5215,5289,5401,5511,5621,5726,5785,5840", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2958,3048,3128,3183,3234,3300,3373,3451,3539,3624,3695,3772,3846,3918,4024,4115,4189,4284,4382,4456,4536,4637,4690,4776,4842,4931,5021,5083,5147,5210,5284,5396,5506,5616,5721,5780,5835,5914"}, "to": {"startLines": "2,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3601,3693,3781,3868,3964,7959,8060,8181,14729,14869,15085,15324,15384,15468,15530,15596,15654,15727,15790,15846,15965,16022,16083,16139,16213,20455,20541,20625,20758,20840,20923,21069,21159,21239,21294,21345,21411,21484,21562,21650,21735,21806,21883,21957,22029,22135,22226,22300,22395,22493,22567,22647,22748,22801,22887,22953,23042,23132,23194,23258,23321,23395,23507,23617,23727,23832,23891,24260", "endLines": "6,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "366,3688,3776,3863,3959,4049,8055,8176,8260,14790,14959,15154,15379,15463,15525,15591,15649,15722,15785,15841,15960,16017,16078,16134,16208,16353,20536,20620,20753,20835,20918,21064,21154,21234,21289,21340,21406,21479,21557,21645,21730,21801,21878,21952,22024,22130,22221,22295,22390,22488,22562,22642,22743,22796,22882,22948,23037,23127,23189,23253,23316,23390,23502,23612,23722,23827,23886,23941,24334"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,492,596,709,793,897,1018,1103,1183,1274,1367,1462,1556,1656,1749,1844,1938,2029,2121,2204,2316,2424,2524,2638,2744,2850,3014,24599", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "487,591,704,788,892,1013,1098,1178,1269,1362,1457,1551,1651,1744,1839,1933,2024,2116,2199,2311,2419,2519,2633,2739,2845,3009,3112,24678"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,276,385,496,610,737,848,948,1038,1217,1395,1574,1756,1934,2118,2294,2419,2519,2629,2727,2824,2920,3013,3133,3254,3341,3423,3508,3608,3714,3806,3912,4018,4104", "endColumns": "120,99,108,110,113,126,110,99,89,178,177,178,181,177,183,175,124,99,109,97,96,95,92,119,120,86,81,84,99,105,91,105,105,85,97", "endOffsets": "171,271,380,491,605,732,843,943,1033,1212,1390,1569,1751,1929,2113,2289,2414,2514,2624,2722,2819,2915,3008,3128,3249,3336,3418,3503,3603,3709,3801,3907,4013,4099,4197"}, "to": {"startLines": "155,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14964,16429,16529,16638,16749,16863,16990,17101,17201,17291,17470,17648,17827,18009,18187,18371,18547,18672,18772,18882,18980,19077,19173,19266,19386,19507,19594,19676,19761,19861,19967,20059,20165,20271,20357", "endColumns": "120,99,108,110,113,126,110,99,89,178,177,178,181,177,183,175,124,99,109,97,96,95,92,119,120,86,81,84,99,105,91,105,105,85,97", "endOffsets": "15080,16524,16633,16744,16858,16985,17096,17196,17286,17465,17643,17822,18004,18182,18366,18542,18667,18767,18877,18975,19072,19168,19261,19381,19502,19589,19671,19756,19856,19962,20054,20160,20266,20352,20450"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,983,1052,1140,1230,1303,1380,1447", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,978,1047,1135,1225,1298,1375,1442,1557"}, "to": {"startLines": "96,97,148,149,151,157,158,250,251,252,253,255,256,259,263,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8265,8362,14370,14467,14641,15159,15237,23946,24037,24119,24191,24339,24427,24683,25038,25115,25182", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "8357,8441,14462,14564,14724,15232,15319,24032,24114,24186,24255,24422,24512,24751,25110,25177,25292"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ro/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,342,425,493", "endColumns": "66,76,82,67,65", "endOffsets": "264,341,424,492,558"}, "to": {"startLines": "56,71,72,73,74", "startColumns": "4,4,4,4,4", "startOffsets": "5193,6432,6513,6600,6672", "endColumns": "70,80,86,71,69", "endOffsets": "5259,6508,6595,6667,6737"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,304,417,539,616,709,820,953,1069,1208,1288,1383,1474,1568,1684,1807,1907,2040,2171,2308,2480,2613,2728,2848,2971,3063,3155,3278,3415,3511,3612,3719,3854,3996,4104,4203,4275,4349,4431,4515,4612,4690,4769,4864,4964,5055,5155,5238,5345,5441,5550,5671,5749,5860", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "176,299,412,534,611,704,815,948,1064,1203,1283,1378,1469,1563,1679,1802,1902,2035,2166,2303,2475,2608,2723,2843,2966,3058,3150,3273,3410,3506,3607,3714,3849,3991,4099,4198,4270,4344,4426,4510,4607,4685,4764,4859,4959,5050,5150,5233,5340,5436,5545,5666,5744,5855,5955"}, "to": {"startLines": "34,35,36,37,98,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,150,153,257,260,262,266,267,268,269,270,271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3117,3243,3366,3479,8446,10783,10876,10987,11120,11236,11375,11455,11550,11641,11735,11851,11974,12074,12207,12338,12475,12647,12780,12895,13015,13138,13230,13322,13445,13582,13678,13779,13886,14021,14163,14271,14569,14795,24517,24756,24941,25297,25375,25454,25549,25649,25740,25840,25923,26030,26126,26235,26356,26434,26545", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "3238,3361,3474,3596,8518,10871,10982,11115,11231,11370,11450,11545,11636,11730,11846,11969,12069,12202,12333,12470,12642,12775,12890,13010,13133,13225,13317,13440,13577,13673,13774,13881,14016,14158,14266,14365,14636,14864,24594,24835,25033,25370,25449,25544,25644,25735,25835,25918,26025,26121,26230,26351,26429,26540,26640"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ro/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "99,100,101,102,103,104,105,106,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8523,8630,8790,8919,9028,9179,9311,9434,9688,9850,9960,10119,10251,10397,10563,10634,10701", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "8625,8785,8914,9023,9174,9306,9429,9538,9845,9955,10114,10246,10392,10558,10629,10696,10778"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ro/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "9543", "endColumns": "144", "endOffsets": "9683"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ro/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,252,322,378,468,540,596,723,809,893,1014,1095,1164,1243,1338,1393,1462,1534,1608,1653,1708,1753,1792,1845,1912,1982,2053,2119,2186,2228,2294,2366,2433,2511,2606,2670,2727,2799,2853", "endColumns": "43,69,55,89,71,55,126,85,83,120,80,68,78,94,54,68,71,73,44,54,44,38,52,66,69,70,65,66,41,65,71,66,77,94,63,56,71,53,66", "endOffsets": "251,321,377,467,539,595,722,808,892,1013,1094,1163,1242,1337,1392,1461,1533,1607,1652,1707,1752,1791,1844,1911,1981,2052,2118,2185,2227,2293,2365,2432,2510,2605,2669,2726,2798,2852,2919"}, "to": {"startLines": "50,51,52,53,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4781,4829,4903,4963,5057,5133,5264,5395,5485,5573,5698,5783,5856,5939,6038,6097,6170,6246,6324,6373,6742,6791,6834,6891,6962,7036,7111,7181,7252,7298,7368,7444,7515,7597,7696,7764,7825,7901,16358", "endColumns": "47,73,59,93,75,59,130,89,87,124,84,72,82,98,58,72,75,77,48,58,48,42,56,70,73,74,69,70,45,69,75,70,81,98,67,60,75,57,70", "endOffsets": "4824,4898,4958,5052,5128,5188,5390,5480,5568,5693,5778,5851,5934,6033,6092,6165,6241,6319,6368,6427,6786,6829,6886,6957,7031,7106,7176,7247,7293,7363,7439,7510,7592,7691,7759,7820,7896,7954,16424"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "43,44,45,46,47,48,49,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4054,4152,4254,4354,4453,4555,4664,24840", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4147,4249,4349,4448,4550,4659,4776,24936"}}]}]}