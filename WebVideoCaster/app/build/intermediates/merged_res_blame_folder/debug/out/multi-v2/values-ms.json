{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-ms/values-ms.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,978,1045,1130,1216,1288,1364,1430", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,973,1040,1125,1211,1283,1359,1425,1545"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8027,8117,14231,14327,14501,15011,15094,23655,23742,23827,23893,24046,24131,24381,24737,24813,24879", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "8112,8196,14322,14424,14581,15089,15184,23737,23822,23888,23955,24126,24212,24448,24808,24874,24994"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ms/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,346,426,494", "endColumns": "66,80,79,67,71", "endOffsets": "264,345,425,493,565"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5055,6256,6341,6425,6497", "endColumns": "70,84,83,71,75", "endOffsets": "5121,6336,6420,6492,6568"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2838,2926,3004,3057,3108,3174,3245,3323,3406,3485,3558,3634,3707,3778,3885,3977,4050,4140,4233,4307,4378,4469,4521,4601,4669,4753,4838,4900,4964,5027,5099,5203,5311,5407,5513,5570,5625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2833,2921,2999,3052,3103,3169,3240,3318,3401,3480,3553,3629,3702,3773,3880,3972,4045,4135,4228,4302,4373,4464,4516,4596,4664,4748,4833,4895,4959,5022,5094,5198,5306,5402,5508,5565,5620,5706"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3480,3560,3639,3726,3818,7725,7828,7944,14586,14731,14946,15189,15248,15335,15397,15459,15519,15585,15647,15701,15809,15866,15927,15982,16053,20243,20334,20420,20568,20654,20740,20868,20956,21034,21087,21138,21204,21275,21353,21436,21515,21588,21664,21737,21808,21915,22007,22080,22170,22263,22337,22408,22499,22551,22631,22699,22783,22868,22930,22994,23057,23129,23233,23341,23437,23543,23600,23960", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "324,3555,3634,3721,3813,3900,7823,7939,8022,14646,14819,15006,15243,15330,15392,15454,15514,15580,15642,15696,15804,15861,15922,15977,16048,16168,20329,20415,20563,20649,20735,20863,20951,21029,21082,21133,21199,21270,21348,21431,21510,21583,21659,21732,21803,21910,22002,22075,22165,22258,22332,22403,22494,22546,22626,22694,22778,22863,22925,22989,23052,23124,23228,23336,23432,23538,23595,23650,24041"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3905,4000,4102,4199,4309,4415,4533,24538", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3995,4097,4194,4304,4410,4528,4643,24634"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,24300", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,24376"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ms/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,252,318,380,463,532,591,714,801,878,1003,1089,1157,1234,1335,1379,1439,1502,1576,1625,1665,1705,1744,1787,1846,1908,1973,2039,2105,2148,2218,2281,2345,2429,2509,2571,2628,2699,2745", "endColumns": "43,65,61,82,68,58,122,86,76,124,85,67,76,100,43,59,62,73,48,39,39,38,42,58,61,64,65,65,42,69,62,63,83,79,61,56,70,45,60", "endOffsets": "251,317,379,462,531,590,713,800,877,1002,1088,1156,1233,1334,1378,1438,1501,1575,1624,1664,1704,1743,1786,1845,1907,1972,2038,2104,2147,2217,2280,2344,2428,2508,2570,2627,2698,2744,2805"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4648,4696,4766,4832,4919,4992,5126,5253,5344,5425,5554,5644,5716,5797,5902,5950,6014,6081,6159,6212,6573,6617,6660,6707,6770,6836,6905,6975,7045,7092,7166,7233,7301,7389,7473,7539,7600,7675,16173", "endColumns": "47,69,65,86,72,62,126,90,80,128,89,71,80,104,47,63,66,77,52,43,43,42,46,62,65,68,69,69,46,73,66,67,87,83,65,60,74,49,64", "endOffsets": "4691,4761,4827,4914,4987,5050,5248,5339,5420,5549,5639,5711,5792,5897,5945,6009,6076,6154,6207,6251,6612,6655,6702,6765,6831,6900,6970,7040,7087,7161,7228,7296,7384,7468,7534,7595,7670,7720,16233"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ms/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8281,8387,8560,8690,8796,8968,9100,9220,9473,9658,9767,9942,10077,10248,10423,10490,10554", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "8382,8555,8685,8791,8963,9095,9215,9322,9653,9762,9937,10072,10243,10418,10485,10549,10632"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ms/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9327", "endColumns": "145", "endOffsets": "9468"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,272,376,479,594,714,822,917,1002,1187,1371,1558,1745,1924,2113,2303,2419,2512,2616,2712,2809,2911,3007,3120,3235,3317,3399,3485,3589,3696,3792,3892,4002,4088", "endColumns": "121,94,103,102,114,119,107,94,84,184,183,186,186,178,188,189,115,92,103,95,96,101,95,112,114,81,81,85,103,106,95,99,109,85,93", "endOffsets": "172,267,371,474,589,709,817,912,997,1182,1366,1553,1740,1919,2108,2298,2414,2507,2611,2707,2804,2906,3002,3115,3230,3312,3394,3480,3584,3691,3787,3887,3997,4083,4177"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14824,16238,16333,16437,16540,16655,16775,16883,16978,17063,17248,17432,17619,17806,17985,18174,18364,18480,18573,18677,18773,18870,18972,19068,19181,19296,19378,19460,19546,19650,19757,19853,19953,20063,20149", "endColumns": "121,94,103,102,114,119,107,94,84,184,183,186,186,178,188,189,115,92,103,95,96,101,95,112,114,81,81,85,103,106,95,99,109,85,93", "endOffsets": "14941,16328,16432,16535,16650,16770,16878,16973,17058,17243,17427,17614,17801,17980,18169,18359,18475,18568,18672,18768,18865,18967,19063,19176,19291,19373,19455,19541,19645,19752,19848,19948,20058,20144,20238"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,396,511,591,692,811,957,1081,1225,1307,1403,1491,1585,1697,1819,1920,2054,2185,2313,2488,2609,2730,2855,2981,3070,3167,3284,3408,3505,3608,3710,3846,3988,4091,4185,4257,4337,4420,4505,4603,4679,4758,4853,4948,5041,5140,5223,5322,5417,5519,5637,5714,5816", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "166,281,391,506,586,687,806,952,1076,1220,1302,1398,1486,1580,1692,1814,1915,2049,2180,2308,2483,2604,2725,2850,2976,3065,3162,3279,3403,3500,3603,3705,3841,3983,4086,4180,4252,4332,4415,4500,4598,4674,4753,4848,4943,5036,5135,5218,5317,5412,5514,5632,5709,5811,5903"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3140,3255,3365,8201,10637,10738,10857,11003,11127,11271,11353,11449,11537,11631,11743,11865,11966,12100,12231,12359,12534,12655,12776,12901,13027,13116,13213,13330,13454,13551,13654,13756,13892,14034,14137,14429,14651,24217,24453,24639,24999,25075,25154,25249,25344,25437,25536,25619,25718,25813,25915,26033,26110,26212", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "3135,3250,3360,3475,8276,10733,10852,10998,11122,11266,11348,11444,11532,11626,11738,11860,11961,12095,12226,12354,12529,12650,12771,12896,13022,13111,13208,13325,13449,13546,13649,13751,13887,14029,14132,14226,14496,14726,24295,24533,24732,25070,25149,25244,25339,25432,25531,25614,25713,25808,25910,26028,26105,26207,26299"}}]}]}