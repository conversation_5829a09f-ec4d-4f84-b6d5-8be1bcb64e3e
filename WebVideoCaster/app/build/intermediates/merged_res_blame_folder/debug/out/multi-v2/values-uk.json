{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-uk/values-uk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,677,786,920,1032,1175,1255,1351,1440,1532,1644,1764,1865,2002,2135,2266,2451,2574,2694,2820,2938,3027,3124,3246,3372,3466,3567,3672,3812,3959,4063,4158,4229,4307,4389,4472,4567,4643,4725,4820,4920,5011,5107,5192,5295,5387,5485,5599,5675,5776", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "164,276,390,504,581,672,781,915,1027,1170,1250,1346,1435,1527,1639,1759,1860,1997,2130,2261,2446,2569,2689,2815,2933,3022,3119,3241,3367,3461,3562,3667,3807,3954,4058,4153,4224,4302,4384,4467,4562,4638,4720,4815,4915,5006,5102,5187,5290,5382,5480,5594,5670,5771,5873"}, "to": {"startLines": "35,36,37,38,99,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151,154,258,261,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,3262,3374,3488,8457,10760,10851,10960,11094,11206,11349,11429,11525,11614,11706,11818,11938,12039,12176,12309,12440,12625,12748,12868,12994,13112,13201,13298,13420,13546,13640,13741,13846,13986,14133,14237,14535,14757,24448,24684,24868,25236,25312,25394,25489,25589,25680,25776,25861,25964,26056,26154,26268,26344,26445", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "3257,3369,3483,3597,8529,10846,10955,11089,11201,11344,11424,11520,11609,11701,11813,11933,12034,12171,12304,12435,12620,12743,12863,12989,13107,13196,13293,13415,13541,13635,13736,13841,13981,14128,14232,14327,14601,14830,24525,24762,24958,25307,25389,25484,25584,25675,25771,25856,25959,26051,26149,26263,26339,26440,26542"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "44,45,46,47,48,49,50,262", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4045,4145,4247,4348,4449,4554,4659,24767", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "4140,4242,4343,4444,4549,4654,4767,24863"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-uk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,322,386,463,542,593,738,818,903,1024,1112,1183,1268,1370,1425,1496,1567,1641,1692,1740,1787,1832,1885,1954,2023,2091,2158,2227,2270,2342,2405,2473,2549,2635,2698,2755,2825,2876", "endColumns": "45,67,63,76,78,50,144,79,84,120,87,70,84,101,54,70,70,73,50,47,46,44,52,68,68,67,66,68,42,71,62,67,75,85,62,56,69,50,67", "endOffsets": "253,321,385,462,541,592,737,817,902,1023,1111,1182,1267,1369,1424,1495,1566,1640,1691,1739,1786,1831,1884,1953,2022,2090,2157,2226,2269,2341,2404,2472,2548,2634,2697,2754,2824,2875,2943"}, "to": {"startLines": "51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4772,4822,4894,4962,5043,5126,5252,5401,5485,5574,5699,5791,5866,5955,6061,6120,6195,6270,6348,6403,6777,6828,6877,6934,7007,7080,7152,7223,7296,7343,7419,7486,7558,7638,7728,7795,7856,7930,16301", "endColumns": "49,71,67,80,82,54,148,83,88,124,91,74,88,105,58,74,74,77,54,51,50,48,56,72,72,71,70,72,46,75,66,71,79,89,66,60,73,54,71", "endOffsets": "4817,4889,4957,5038,5121,5176,5396,5480,5569,5694,5786,5861,5950,6056,6115,6190,6265,6343,6398,6450,6823,6872,6929,7002,7075,7147,7218,7291,7338,7414,7481,7553,7633,7723,7790,7851,7925,7980,16368"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-uk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "9545", "endColumns": "145", "endOffsets": "9686"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1133,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,24530", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1128,1207,1298,1391,1486,1580,1680,1773,1868,1963,2054,2145,2244,2350,2456,2554,2661,2768,2873,3043,3143,24607"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "97,98,149,150,152,158,159,251,252,253,254,256,257,260,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8280,8373,14332,14434,14606,15125,15207,23882,23970,24052,24122,24275,24360,24612,24963,25043,25113", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "8368,8452,14429,14530,14685,15202,15291,23965,24047,24117,24188,24355,24443,24679,25038,25108,25231"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,288,394,502,611,724,830,925,1016,1191,1381,1570,1760,1948,2140,2330,2449,2550,2655,2753,2849,2943,3039,3140,3253,3342,3430,3516,3621,3725,3829,3930,4047,4134", "endColumns": "132,99,105,107,108,112,105,94,90,174,189,188,189,187,191,189,118,100,104,97,95,93,95,100,112,88,87,85,104,103,103,100,116,86,94", "endOffsets": "183,283,389,497,606,719,825,920,1011,1186,1376,1565,1755,1943,2135,2325,2444,2545,2650,2748,2844,2938,3034,3135,3248,3337,3425,3511,3616,3720,3824,3925,4042,4129,4224"}, "to": {"startLines": "156,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14926,16373,16473,16579,16687,16796,16909,17015,17110,17201,17376,17566,17755,17945,18133,18325,18515,18634,18735,18840,18938,19034,19128,19224,19325,19438,19527,19615,19701,19806,19910,20014,20115,20232,20319", "endColumns": "132,99,105,107,108,112,105,94,90,174,189,188,189,187,191,189,118,100,104,97,95,93,95,100,112,88,87,85,104,103,103,100,116,86,94", "endOffsets": "15054,16468,16574,16682,16791,16904,17010,17105,17196,17371,17561,17750,17940,18128,18320,18510,18629,18730,18835,18933,19029,19123,19219,19320,19433,19522,19610,19696,19801,19905,20009,20110,20227,20314,20409"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-uk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8534,8642,8805,8932,9042,9196,9325,9440,9691,9859,9965,10127,10252,10399,10541,10611,10672", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "8637,8800,8927,9037,9191,9320,9435,9540,9854,9960,10122,10247,10394,10536,10606,10667,10755"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2954,3042,3120,3177,3228,3294,3366,3442,3532,3615,3688,3765,3846,3920,4029,4119,4198,4289,4385,4459,4540,4635,4689,4771,4837,4924,5010,5072,5136,5199,5272,5379,5489,5587,5693,5754,5809", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2949,3037,3115,3172,3223,3289,3361,3437,3527,3610,3683,3760,3841,3915,4024,4114,4193,4284,4380,4454,4535,4630,4684,4766,4832,4919,5005,5067,5131,5194,5267,5374,5484,5582,5688,5749,5804,5886"}, "to": {"startLines": "2,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3602,3680,3758,3846,3954,7985,8081,8197,14690,14835,15059,15296,15359,15447,15509,15576,15634,15705,15764,15818,15932,15992,16055,16109,16182,20414,20500,20583,20722,20807,20894,21027,21115,21193,21250,21301,21367,21439,21515,21605,21688,21761,21838,21919,21993,22102,22192,22271,22362,22458,22532,22613,22708,22762,22844,22910,22997,23083,23145,23209,23272,23345,23452,23562,23660,23766,23827,24193", "endLines": "7,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "419,3675,3753,3841,3949,4040,8076,8192,8275,14752,14921,15120,15354,15442,15504,15571,15629,15700,15759,15813,15927,15987,16050,16104,16177,16296,20495,20578,20717,20802,20889,21022,21110,21188,21245,21296,21362,21434,21510,21600,21683,21756,21833,21914,21988,22097,22187,22266,22357,22453,22527,22608,22703,22757,22839,22905,22992,23078,23140,23204,23267,23340,23447,23557,23655,23761,23822,23877,24270"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-uk/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,351,439,507", "endColumns": "66,85,87,67,63", "endOffsets": "264,350,438,506,570"}, "to": {"startLines": "57,72,73,74,75", "startColumns": "4,4,4,4,4", "startOffsets": "5181,6455,6545,6637,6709", "endColumns": "70,89,91,71,67", "endOffsets": "5247,6540,6632,6704,6772"}}]}]}