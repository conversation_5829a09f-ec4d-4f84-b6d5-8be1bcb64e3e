{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-hi/values-hi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1084,1178,1247,1306,1391,1454,1517,1575,1640,1701,1762,1868,1926,1986,2045,2115,2231,2310,2390,2524,2599,2675,2812,2909,3007,3064,3119,3185,3255,3332,3418,3503,3571,3647,3728,3806,3907,3993,4080,4177,4276,4350,4420,4524,4578,4665,4732,4822,4914,4976,5040,5103,5169,5274,5384,5485,5592,5653,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "254,332,408,489,596,692,799,931,1014,1079,1173,1242,1301,1386,1449,1512,1570,1635,1696,1757,1863,1921,1981,2040,2110,2226,2305,2385,2519,2594,2670,2807,2904,3002,3059,3114,3180,3250,3327,3413,3498,3566,3642,3723,3801,3902,3988,4075,4172,4271,4345,4415,4519,4573,4660,4727,4817,4909,4971,5035,5098,5164,5269,5379,5480,5587,5648,5707,5786"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3451,3529,3605,3686,3793,7741,7848,7980,14476,14622,14840,15084,15143,15228,15291,15354,15412,15477,15538,15599,15705,15763,15823,15882,15952,20188,20267,20347,20481,20556,20632,20769,20866,20964,21021,21076,21142,21212,21289,21375,21460,21528,21604,21685,21763,21864,21950,22037,22134,22233,22307,22377,22481,22535,22622,22689,22779,22871,22933,22997,23060,23126,23231,23341,23442,23549,23610,23980", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "304,3524,3600,3681,3788,3884,7843,7975,8058,14536,14711,14904,15138,15223,15286,15349,15407,15472,15533,15594,15700,15758,15818,15877,15947,16063,20262,20342,20476,20551,20627,20764,20861,20959,21016,21071,21137,21207,21284,21370,21455,21523,21599,21680,21758,21859,21945,22032,22129,22228,22302,22372,22476,22530,22617,22684,22774,22866,22928,22992,23055,23121,23226,23336,23437,23544,23605,23664,24054"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8063,8158,14124,14217,14387,14909,14987,23669,23758,23843,23911,24059,24140,24389,24750,24831,24897", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "8153,8236,14212,14310,14471,14982,15079,23753,23838,23906,23975,24135,24220,24457,24826,24892,25012"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,278,390,508,625,747,865,968,1061,1239,1417,1598,1782,1960,2145,2332,2450,2543,2657,2756,2853,2951,3052,3167,3282,3365,3448,3534,3635,3756,3849,3952,4055,4141", "endColumns": "123,98,111,117,116,121,117,102,92,177,177,180,183,177,184,186,117,92,113,98,96,97,100,114,114,82,82,85,100,120,92,102,102,85,92", "endOffsets": "174,273,385,503,620,742,860,963,1056,1234,1412,1593,1777,1955,2140,2327,2445,2538,2652,2751,2848,2946,3047,3162,3277,3360,3443,3529,3630,3751,3844,3947,4050,4136,4229"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14716,16133,16232,16344,16462,16579,16701,16819,16922,17015,17193,17371,17552,17736,17914,18099,18286,18404,18497,18611,18710,18807,18905,19006,19121,19236,19319,19402,19488,19589,19710,19803,19906,20009,20095", "endColumns": "123,98,111,117,116,121,117,102,92,177,177,180,183,177,184,186,117,92,113,98,96,97,100,114,114,82,82,85,100,120,92,102,102,85,92", "endOffsets": "14835,16227,16339,16457,16574,16696,16814,16917,17010,17188,17366,17547,17731,17909,18094,18281,18399,18492,18606,18705,18802,18900,19001,19116,19231,19314,19397,19483,19584,19705,19798,19901,20004,20090,20183"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-hi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8323,8431,8591,8717,8829,8980,9110,9222,9480,9637,9746,9912,10042,10183,10336,10399,10466", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "8426,8586,8712,8824,8975,9105,9217,9329,9632,9741,9907,10037,10178,10331,10394,10461,10549"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-hi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9334", "endColumns": "145", "endOffsets": "9475"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-hi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,331,388,472,547,602,736,820,897,1012,1096,1168,1247,1344,1392,1452,1513,1587,1632,1677,1718,1758,1807,1868,1930,2005,2062,2120,2160,2228,2291,2359,2437,2518,2583,2644,2713,2762", "endColumns": "46,75,56,83,74,54,133,83,76,114,83,71,78,96,47,59,60,73,44,44,40,39,48,60,61,74,56,57,39,67,62,67,77,80,64,60,68,48,60", "endOffsets": "254,330,387,471,546,601,735,819,896,1011,1095,1167,1246,1343,1391,1451,1512,1586,1631,1676,1717,1757,1806,1867,1929,2004,2061,2119,2159,2227,2290,2358,2436,2517,2582,2643,2712,2761,2822"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4642,4693,4773,4834,4922,5001,5131,5269,5357,5438,5557,5645,5721,5804,5905,5957,6021,6086,6164,6213,6584,6629,6673,6726,6791,6857,6936,6997,7059,7103,7175,7242,7314,7396,7481,7550,7615,7688,16068", "endColumns": "50,79,60,87,78,58,137,87,80,118,87,75,82,100,51,63,64,77,48,48,44,43,52,64,65,78,60,61,43,71,66,71,81,84,68,64,72,52,64", "endOffsets": "4688,4768,4829,4917,4996,5055,5264,5352,5433,5552,5640,5716,5799,5900,5952,6016,6081,6159,6208,6257,6624,6668,6721,6786,6852,6931,6992,7054,7098,7170,7237,7309,7391,7476,7545,7610,7683,7736,16128"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,403,515,597,689,799,933,1049,1199,1280,1379,1466,1559,1680,1796,1900,2041,2183,2309,2476,2599,2709,2824,2947,3035,3126,3250,3372,3467,3565,3673,3814,3962,4072,4167,4239,4320,4402,4488,4589,4665,4745,4841,4937,5028,5126,5208,5306,5400,5499,5610,5686,5782", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "164,278,398,510,592,684,794,928,1044,1194,1275,1374,1461,1554,1675,1791,1895,2036,2178,2304,2471,2594,2704,2819,2942,3030,3121,3245,3367,3462,3560,3668,3809,3957,4067,4162,4234,4315,4397,4483,4584,4660,4740,4836,4932,5023,5121,5203,5301,5395,5494,5605,5681,5777,5867"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,3105,3219,3339,8241,10554,10646,10756,10890,11006,11156,11237,11336,11423,11516,11637,11753,11857,11998,12140,12266,12433,12556,12666,12781,12904,12992,13083,13207,13329,13424,13522,13630,13771,13919,14029,14315,14541,24225,24462,24649,25017,25093,25173,25269,25365,25456,25554,25636,25734,25828,25927,26038,26114,26210", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "3100,3214,3334,3446,8318,10641,10751,10885,11001,11151,11232,11331,11418,11511,11632,11748,11852,11993,12135,12261,12428,12551,12661,12776,12899,12987,13078,13202,13324,13419,13517,13625,13766,13914,14024,14119,14382,14617,24302,24543,24745,25088,25168,25264,25360,25451,25549,25631,25729,25823,25922,26033,26109,26205,26295"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,24307", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,24384"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3889,3987,4090,4195,4296,4409,4515,24548", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3982,4085,4190,4291,4404,4510,4637,24644"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-hi/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,343,429,503", "endColumns": "66,77,85,73,67", "endOffsets": "264,342,428,502,570"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5060,6262,6344,6434,6512", "endColumns": "70,81,89,77,71", "endOffsets": "5126,6339,6429,6507,6579"}}]}]}