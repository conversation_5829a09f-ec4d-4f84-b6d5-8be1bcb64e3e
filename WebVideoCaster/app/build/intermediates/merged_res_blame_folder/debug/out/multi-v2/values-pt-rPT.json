{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3960,4057,4159,4258,4358,4465,4571,24925", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "4052,4154,4253,4353,4460,4566,4687,25021"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,274,376,480,588,714,828,935,1026,1201,1387,1579,1769,1951,2147,2337,2460,2558,2668,2765,2860,2952,3046,3166,3296,3380,3468,3551,3653,3769,3862,3972,4081,4168", "endColumns": "119,98,101,103,107,125,113,106,90,174,185,191,189,181,195,189,122,97,109,96,94,91,93,119,129,83,87,82,101,115,92,109,108,86,98", "endOffsets": "170,269,371,475,583,709,823,930,1021,1196,1382,1574,1764,1946,2142,2332,2455,2553,2663,2760,2855,2947,3041,3161,3291,3375,3463,3546,3648,3764,3857,3967,4076,4163,4262"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14944,16449,16548,16650,16754,16862,16988,17102,17209,17300,17475,17661,17853,18043,18225,18421,18611,18734,18832,18942,19039,19134,19226,19320,19440,19570,19654,19742,19825,19927,20043,20136,20246,20355,20442", "endColumns": "119,98,101,103,107,125,113,106,90,174,185,191,189,181,195,189,122,97,109,96,94,91,93,119,129,83,87,82,101,115,92,109,108,86,98", "endOffsets": "15059,16543,16645,16749,16857,16983,17097,17204,17295,17470,17656,17848,18038,18220,18416,18606,18729,18827,18937,19034,19129,19221,19315,19435,19565,19649,19737,19820,19922,20038,20131,20241,20350,20437,20536"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8109,8204,14347,14444,14625,15143,15222,24014,24105,24192,24264,24419,24504,24766,25123,25199,25271", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "8199,8282,14439,14538,14706,15217,15314,24100,24187,24259,24328,24499,24589,24837,25194,25266,25388"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,24680", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,24761"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1079,1171,1250,1315,1405,1469,1537,1599,1672,1736,1790,1916,1974,2036,2090,2166,2309,2396,2478,2617,2699,2781,2917,3004,3084,3140,3191,3257,3332,3412,3499,3578,3651,3728,3801,3875,3982,4075,4152,4245,4343,4417,4498,4597,4650,4734,4800,4889,4977,5039,5103,5166,5234,5350,5458,5565,5667,5727,5782", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,135,86,79,55,50,65,74,79,86,78,72,76,72,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85", "endOffsets": "268,349,429,511,610,706,809,929,1010,1074,1166,1245,1310,1400,1464,1532,1594,1667,1731,1785,1911,1969,2031,2085,2161,2304,2391,2473,2612,2694,2776,2912,2999,3079,3135,3186,3252,3327,3407,3494,3573,3646,3723,3796,3870,3977,4070,4147,4240,4338,4412,4493,4592,4645,4729,4795,4884,4972,5034,5098,5161,5229,5345,5453,5560,5662,5722,5777,5863"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3522,3603,3683,3765,3864,7805,7908,8028,14711,14852,15064,15319,15384,15474,15538,15606,15668,15741,15805,15859,15985,16043,16105,16159,16235,20541,20628,20710,20849,20931,21013,21149,21236,21316,21372,21423,21489,21564,21644,21731,21810,21883,21960,22033,22107,22214,22307,22384,22477,22575,22649,22730,22829,22882,22966,23032,23121,23209,23271,23335,23398,23466,23582,23690,23797,23899,23959,24333", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,135,86,79,55,50,65,74,79,86,78,72,76,72,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85", "endOffsets": "318,3598,3678,3760,3859,3955,7903,8023,8104,14770,14939,15138,15379,15469,15533,15601,15663,15736,15800,15854,15980,16038,16100,16154,16230,16373,20623,20705,20844,20926,21008,21144,21231,21311,21367,21418,21484,21559,21639,21726,21805,21878,21955,22028,22102,22209,22302,22379,22472,22570,22644,22725,22824,22877,22961,23027,23116,23204,23266,23330,23393,23461,23577,23685,23792,23894,23954,24009,24414"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8364,8469,8632,8760,8868,9036,9164,9286,9540,9728,9836,10006,10137,10296,10474,10542,10611", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "8464,8627,8755,8863,9031,9159,9281,9390,9723,9831,10001,10132,10291,10469,10537,10606,10693"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,407,523,600,695,808,945,1059,1200,1280,1378,1469,1565,1676,1799,1902,2043,2183,2325,2513,2647,2764,2885,3007,3099,3192,3313,3447,3547,3654,3755,3896,4043,4149,4249,4331,4408,4494,4577,4674,4750,4830,4927,5029,5118,5214,5298,5406,5503,5603,5718,5794,5894", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "167,283,402,518,595,690,803,940,1054,1195,1275,1373,1464,1560,1671,1794,1897,2038,2178,2320,2508,2642,2759,2880,3002,3094,3187,3308,3442,3542,3649,3750,3891,4038,4144,4244,4326,4403,4489,4572,4669,4745,4825,4922,5024,5113,5209,5293,5401,5498,5598,5713,5789,5889,5981"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3054,3171,3287,3406,8287,10698,10793,10906,11043,11157,11298,11378,11476,11567,11663,11774,11897,12000,12141,12281,12423,12611,12745,12862,12983,13105,13197,13290,13411,13545,13645,13752,13853,13994,14141,14247,14543,14775,24594,24842,25026,25393,25469,25549,25646,25748,25837,25933,26017,26125,26222,26322,26437,26513,26613", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "3166,3282,3401,3517,8359,10788,10901,11038,11152,11293,11373,11471,11562,11658,11769,11892,11995,12136,12276,12418,12606,12740,12857,12978,13100,13192,13285,13406,13540,13640,13747,13848,13989,14136,14242,14342,14620,14847,24675,24920,25118,25464,25544,25641,25743,25832,25928,26012,26120,26217,26317,26432,26508,26608,26700"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "202,269,343,419,487", "endColumns": "66,73,75,67,61", "endOffsets": "268,342,418,486,548"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5102,6343,6421,6501,6573", "endColumns": "70,77,79,71,65", "endOffsets": "5168,6416,6496,6568,6634"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "212,258,328,390,480,546,598,750,832,914,1035,1128,1197,1276,1370,1415,1476,1540,1614,1664,1712,1754,1799,1842,1901,1963,2035,2098,2161,2201,2270,2333,2400,2484,2565,2628,2685,2755,2806", "endColumns": "45,69,61,89,65,51,151,81,81,120,92,68,78,93,44,60,63,73,49,47,41,44,42,58,61,71,62,62,39,68,62,66,83,80,62,56,69,50,66", "endOffsets": "257,327,389,479,545,597,749,831,913,1034,1127,1196,1275,1369,1414,1475,1539,1613,1663,1711,1753,1798,1841,1900,1962,2034,2097,2160,2200,2269,2332,2399,2483,2564,2627,2684,2754,2805,2872"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4692,4742,4816,4882,4976,5046,5173,5329,5415,5501,5626,5723,5796,5879,5977,6026,6091,6159,6237,6291,6639,6685,6734,6781,6844,6910,6986,7053,7120,7164,7237,7304,7375,7463,7548,7615,7676,7750,16378", "endColumns": "49,73,65,93,69,55,155,85,85,124,96,72,82,97,48,64,67,77,53,51,45,48,46,62,65,75,66,66,43,72,66,70,87,84,66,60,73,54,70", "endOffsets": "4737,4811,4877,4971,5041,5097,5324,5410,5496,5621,5718,5791,5874,5972,6021,6086,6154,6232,6286,6338,6680,6729,6776,6839,6905,6981,7048,7115,7159,7232,7299,7370,7458,7543,7610,7671,7745,7800,16444"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9395", "endColumns": "144", "endOffsets": "9535"}}]}]}