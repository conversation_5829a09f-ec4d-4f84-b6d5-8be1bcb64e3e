{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-pa/values-pa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3855,3953,4055,4158,4259,4361,4459,24157", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3948,4050,4153,4254,4356,4454,4583,24253"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-pa/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9236", "endColumns": "150", "endOffsets": "9382"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-pa/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,349,441,509", "endColumns": "66,83,91,67,65", "endOffsets": "264,348,440,508,574"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5007,6220,6308,6404,6476", "endColumns": "70,87,95,71,69", "endOffsets": "5073,6303,6399,6471,6541"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,23921", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,23996"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,273,375,491,599,718,832,933,1026,1199,1369,1542,1719,1889,2066,2238,2356,2447,2567,2665,2758,2854,2950,3062,3177,3259,3341,3426,3528,3628,3721,3827,3936,4021", "endColumns": "120,96,101,115,107,118,113,100,92,172,169,172,176,169,176,171,117,90,119,97,92,95,95,111,114,81,81,84,101,99,92,105,108,84,94", "endOffsets": "171,268,370,486,594,713,827,928,1021,1194,1364,1537,1714,1884,2061,2233,2351,2442,2562,2660,2753,2849,2945,3057,3172,3254,3336,3421,3523,3623,3716,3822,3931,4016,4111"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14583,16003,16100,16202,16318,16426,16545,16659,16760,16853,17026,17196,17369,17546,17716,17893,18065,18183,18274,18394,18492,18585,18681,18777,18889,19004,19086,19168,19253,19355,19455,19548,19654,19763,19848", "endColumns": "120,96,101,115,107,118,113,100,92,172,169,172,176,169,176,171,117,90,119,97,92,95,95,111,114,81,81,84,101,99,92,105,108,84,94", "endOffsets": "14699,16095,16197,16313,16421,16540,16654,16755,16848,17021,17191,17364,17541,17711,17888,18060,18178,18269,18389,18487,18580,18676,18772,18884,18999,19081,19163,19248,19350,19450,19543,19649,19758,19843,19938"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2782,2866,2948,3003,3058,3124,3193,3270,3356,3435,3503,3579,3649,3714,3816,3911,3984,4078,4171,4245,4314,4408,4464,4547,4614,4698,4786,4848,4912,4975,5042,5139,5245,5336,5438,5497,5556", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2777,2861,2943,2998,3053,3119,3188,3265,3351,3430,3498,3574,3644,3709,3811,3906,3979,4073,4166,4240,4309,4403,4459,4542,4609,4693,4781,4843,4907,4970,5037,5134,5240,5331,5433,5492,5551,5628"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3430,3507,3586,3667,3766,7667,7775,7887,14344,14491,14704,14949,15008,15093,15156,15218,15276,15340,15401,15455,15569,15627,15687,15741,15811,19943,20024,20103,20238,20314,20391,20520,20604,20686,20741,20796,20862,20931,21008,21094,21173,21241,21317,21387,21452,21554,21649,21722,21816,21909,21983,22052,22146,22202,22285,22352,22436,22524,22586,22650,22713,22780,22877,22983,23074,23176,23235,23603", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "308,3502,3581,3662,3761,3850,7770,7882,7965,14403,14578,14768,15003,15088,15151,15213,15271,15335,15396,15450,15564,15622,15682,15736,15806,15933,20019,20098,20233,20309,20386,20515,20599,20681,20736,20791,20857,20926,21003,21089,21168,21236,21312,21382,21447,21549,21644,21717,21811,21904,21978,22047,22141,22197,22280,22347,22431,22519,22581,22645,22708,22775,22872,22978,23069,23171,23230,23289,23675"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,283,395,510,589,686,801,935,1055,1193,1274,1373,1459,1552,1660,1777,1881,2018,2151,2278,2440,2562,2673,2789,2906,2993,3085,3200,3332,3430,3529,3631,3758,3892,3999,4093,4164,4247,4328,4412,4507,4583,4663,4759,4854,4945,5039,5121,5218,5312,5409,5520,5596,5694", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "163,278,390,505,584,681,796,930,1050,1188,1269,1368,1454,1547,1655,1772,1876,2013,2146,2273,2435,2557,2668,2784,2901,2988,3080,3195,3327,3425,3524,3626,3753,3887,3994,4088,4159,4242,4323,4407,4502,4578,4658,4754,4849,4940,5034,5116,5213,5307,5404,5515,5591,5689,5781"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,3088,3203,3315,8146,10490,10587,10702,10836,10956,11094,11175,11274,11360,11453,11561,11678,11782,11919,12052,12179,12341,12463,12574,12690,12807,12894,12986,13101,13233,13331,13430,13532,13659,13793,13900,14185,14408,23840,24073,24258,24617,24693,24773,24869,24964,25055,25149,25231,25328,25422,25519,25630,25706,25804", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "3083,3198,3310,3425,8220,10582,10697,10831,10951,11089,11170,11269,11355,11448,11556,11673,11777,11914,12047,12174,12336,12458,12569,12685,12802,12889,12981,13096,13228,13326,13425,13527,13654,13788,13895,13989,14251,14486,23916,24152,24348,24688,24768,24864,24959,25050,25144,25226,25323,25417,25514,25625,25701,25799,25891"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-pa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,332,389,469,550,603,744,832,907,1021,1104,1182,1258,1361,1408,1467,1527,1601,1646,1689,1729,1768,1814,1872,1931,1987,2045,2104,2143,2209,2273,2339,2418,2498,2563,2621,2691,2738", "endColumns": "45,77,56,79,80,52,140,87,74,113,82,77,75,102,46,58,59,73,44,42,39,38,45,57,58,55,57,58,38,65,63,65,78,79,64,57,69,46,60", "endOffsets": "253,331,388,468,549,602,743,831,906,1020,1103,1181,1257,1360,1407,1466,1526,1600,1645,1688,1728,1767,1813,1871,1930,1986,2044,2103,2142,2208,2272,2338,2417,2497,2562,2620,2690,2737,2798"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4588,4638,4720,4781,4865,4950,5078,5223,5315,5394,5512,5599,5681,5761,5868,5919,5982,6046,6124,6173,6546,6590,6633,6683,6745,6808,6868,6930,6993,7036,7106,7174,7244,7327,7411,7480,7542,7616,15938", "endColumns": "49,81,60,83,84,56,144,91,78,117,86,81,79,106,50,62,63,77,48,46,43,42,49,61,62,59,61,62,42,69,67,69,82,83,68,61,73,50,64", "endOffsets": "4633,4715,4776,4860,4945,5002,5218,5310,5389,5507,5594,5676,5756,5863,5914,5977,6041,6119,6168,6215,6585,6628,6678,6740,6803,6863,6925,6988,7031,7101,7169,7239,7322,7406,7475,7537,7611,7662,15998"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7970,8063,13994,14086,14256,14773,14851,23294,23382,23466,23534,23680,23759,24001,24353,24433,24499", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "8058,8141,14081,14180,14339,14846,14944,23377,23461,23529,23598,23754,23835,24068,24428,24494,24612"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-pa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8225,8332,8505,8635,8744,8891,9020,9133,9387,9549,9658,9831,9963,10116,10277,10342,10408", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "8327,8500,8630,8739,8886,9015,9128,9231,9544,9653,9826,9958,10111,10272,10337,10403,10485"}}]}]}