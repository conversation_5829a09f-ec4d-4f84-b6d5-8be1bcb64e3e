{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-as/values-as.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,23967", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,24045"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-as/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,347,435,503", "endColumns": "66,81,87,67,72", "endOffsets": "264,346,434,502,575"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5096,6303,6389,6481,6553", "endColumns": "70,85,91,71,76", "endOffsets": "5162,6384,6476,6548,6625"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,279,388,502,622,732,847,949,1040,1214,1383,1552,1725,1892,2066,2236,2353,2446,2560,2659,2758,2855,2952,3055,3177,3261,3347,3430,3533,3634,3725,3834,3946,4033", "endColumns": "125,97,108,113,119,109,114,101,90,173,168,168,172,166,173,169,116,92,113,98,98,96,96,102,121,83,85,82,102,100,90,108,111,86,96", "endOffsets": "176,274,383,497,617,727,842,944,1035,1209,1378,1547,1720,1887,2061,2231,2348,2441,2555,2654,2753,2850,2947,3050,3172,3256,3342,3425,3528,3629,3720,3829,3941,4028,4125"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14596,16045,16143,16252,16366,16486,16596,16711,16813,16904,17078,17247,17416,17589,17756,17930,18100,18217,18310,18424,18523,18622,18719,18816,18919,19041,19125,19211,19294,19397,19498,19589,19698,19810,19897", "endColumns": "125,97,108,113,119,109,114,101,90,173,168,168,172,166,173,169,116,92,113,98,98,96,96,102,121,83,85,82,102,100,90,108,111,86,96", "endOffsets": "14717,16138,16247,16361,16481,16591,16706,16808,16899,17073,17242,17411,17584,17751,17925,18095,18212,18305,18419,18518,18617,18714,18811,18914,19036,19120,19206,19289,19392,19493,19584,19693,19805,19892,19989"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-as/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,329,385,464,544,604,743,825,902,1009,1093,1163,1244,1344,1393,1456,1522,1596,1641,1684,1726,1769,1818,1881,1947,2004,2066,2131,2174,2242,2305,2373,2451,2536,2600,2662,2737,2784", "endColumns": "46,73,55,78,79,59,138,81,76,106,83,69,80,99,48,62,65,73,44,42,41,42,48,62,65,56,61,64,42,67,62,67,77,84,63,61,74,46,65", "endOffsets": "254,328,384,463,543,603,742,824,901,1008,1092,1162,1243,1343,1392,1455,1521,1595,1640,1683,1725,1768,1817,1880,1946,2003,2065,2130,2173,2241,2304,2372,2450,2535,2599,2661,2736,2783,2849"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4676,4727,4805,4865,4948,5032,5167,5310,5396,5477,5588,5676,5750,5835,5939,5992,6059,6129,6207,6256,6630,6676,6723,6776,6843,6913,6974,7040,7109,7156,7228,7295,7367,7449,7538,7606,7672,7751,15975", "endColumns": "50,77,59,82,83,63,142,85,80,110,87,73,84,103,52,66,69,77,48,46,45,46,52,66,69,60,65,68,46,71,66,71,81,88,67,65,78,50,69", "endOffsets": "4722,4800,4860,4943,5027,5091,5305,5391,5472,5583,5671,5745,5830,5934,5987,6054,6124,6202,6251,6298,6671,6718,6771,6838,6908,6969,7035,7104,7151,7223,7290,7362,7444,7533,7601,7667,7746,7797,16040"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,395,509,592,682,790,930,1047,1185,1266,1364,1455,1550,1662,1786,1889,2016,2142,2267,2443,2559,2673,2794,2909,3001,3093,3210,3332,3425,3531,3635,3766,3906,4012,4109,4182,4265,4352,4440,4545,4623,4704,4799,4900,4994,5092,5175,5278,5371,5470,5599,5679,5778", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "165,281,390,504,587,677,785,925,1042,1180,1261,1359,1450,1545,1657,1781,1884,2011,2137,2262,2438,2554,2668,2789,2904,2996,3088,3205,3327,3420,3526,3630,3761,3901,4007,4104,4177,4260,4347,4435,4540,4618,4699,4794,4895,4989,5087,5170,5273,5366,5465,5594,5674,5773,5863"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3065,3180,3296,3405,8286,10491,10581,10689,10829,10946,11084,11165,11263,11354,11449,11561,11685,11788,11915,12041,12166,12342,12458,12572,12693,12808,12900,12992,13109,13231,13324,13430,13534,13665,13805,13911,14199,14424,23880,24129,24318,24682,24760,24841,24936,25037,25131,25229,25312,25415,25508,25607,25736,25816,25915", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "3175,3291,3400,3514,8364,10576,10684,10824,10941,11079,11160,11258,11349,11444,11556,11680,11783,11910,12036,12161,12337,12453,12567,12688,12803,12895,12987,13104,13226,13319,13425,13529,13660,13800,13906,14003,14267,14502,23962,24212,24418,24755,24836,24931,25032,25126,25224,25307,25410,25503,25602,25731,25811,25910,26000"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-as/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8369,8477,8630,8753,8863,8993,9115,9228,9466,9609,9718,9868,9993,10126,10279,10339,10405", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "8472,8625,8748,8858,8988,9110,9223,9335,9604,9713,9863,9988,10121,10274,10334,10400,10486"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1006,1078,1161,1246,1325,1400,1466", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1001,1073,1156,1241,1320,1395,1461,1579"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8106,8201,14008,14101,14272,14787,14884,23312,23401,23491,23559,23712,23795,24050,24423,24498,24564", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "8196,8281,14096,14194,14354,14879,14978,23396,23486,23554,23626,23790,23875,24124,24493,24559,24677"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-as/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9340", "endColumns": "125", "endOffsets": "9461"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-as/values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1048,1137,1202,1261,1347,1411,1475,1538,1608,1672,1726,1831,1889,1951,2005,2077,2194,2281,2364,2504,2581,2662,2789,2880,2957,3011,3062,3128,3198,3275,3362,3437,3508,3585,3654,3723,3830,3921,3993,4082,4171,4245,4317,4403,4453,4532,4598,4678,4762,4824,4888,4951,5020,5120,5215,5307,5399,5457,5512", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "267,349,427,504,590,674,776,899,978,1043,1132,1197,1256,1342,1406,1470,1533,1603,1667,1721,1826,1884,1946,2000,2072,2189,2276,2359,2499,2576,2657,2784,2875,2952,3006,3057,3123,3193,3270,3357,3432,3503,3580,3649,3718,3825,3916,3988,4077,4166,4240,4312,4398,4448,4527,4593,4673,4757,4819,4883,4946,5015,5115,5210,5302,5394,5452,5507,5588"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3519,3601,3679,3756,3842,7802,7904,8027,14359,14507,14722,14983,15042,15128,15192,15256,15319,15389,15453,15507,15612,15670,15732,15786,15858,19994,20081,20164,20304,20381,20462,20589,20680,20757,20811,20862,20928,20998,21075,21162,21237,21308,21385,21454,21523,21630,21721,21793,21882,21971,22045,22117,22203,22253,22332,22398,22478,22562,22624,22688,22751,22820,22920,23015,23107,23199,23257,23631", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "317,3596,3674,3751,3837,3921,7899,8022,8101,14419,14591,14782,15037,15123,15187,15251,15314,15384,15448,15502,15607,15665,15727,15781,15853,15970,20076,20159,20299,20376,20457,20584,20675,20752,20806,20857,20923,20993,21070,21157,21232,21303,21380,21449,21518,21625,21716,21788,21877,21966,22040,22112,22198,22248,22327,22393,22473,22557,22619,22683,22746,22815,22915,23010,23102,23194,23252,23307,23707"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3926,4027,4130,4238,4343,4447,4547,24217", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "4022,4125,4233,4338,4442,4542,4671,24313"}}]}]}