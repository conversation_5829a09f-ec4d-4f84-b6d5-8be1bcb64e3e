{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-te/values-te.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,307,417,543,624,719,832,968,1076,1215,1295,1394,1484,1578,1690,1816,1920,2065,2207,2344,2536,2668,2780,2898,3035,3128,3223,3344,3468,3570,3672,3774,3912,4058,4162,4261,4333,4416,4506,4594,4697,4773,4852,4949,5050,5143,5241,5325,5432,5530,5627,5746,5822,5926", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "175,302,412,538,619,714,827,963,1071,1210,1290,1389,1479,1573,1685,1811,1915,2060,2202,2339,2531,2663,2775,2893,3030,3123,3218,3339,3463,3565,3667,3769,3907,4053,4157,4256,4328,4411,4501,4589,4692,4768,4847,4944,5045,5138,5236,5320,5427,5525,5622,5741,5817,5921,6014"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3080,3205,3332,3442,8506,10779,10874,10987,11123,11231,11370,11450,11549,11639,11733,11845,11971,12075,12220,12362,12499,12691,12823,12935,13053,13190,13283,13378,13499,13623,13725,13827,13929,14067,14213,14317,14613,14840,24578,24828,25017,25383,25459,25538,25635,25736,25829,25927,26011,26118,26216,26313,26432,26508,26612", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "3200,3327,3437,3563,8582,10869,10982,11118,11226,11365,11445,11544,11634,11728,11840,11966,12070,12215,12357,12494,12686,12818,12930,13048,13185,13278,13373,13494,13618,13720,13822,13924,14062,14208,14312,14411,14680,14918,24663,24911,25115,25454,25533,25630,25731,25824,25922,26006,26113,26211,26308,26427,26503,26607,26700"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4018,4120,4228,4330,4431,4537,4644,24916", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "4115,4223,4325,4426,4532,4639,4763,25012"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,287,401,518,640,760,877,978,1075,1257,1426,1597,1773,1941,2116,2287,2409,2504,2621,2720,2815,2916,3012,3125,3237,3324,3411,3492,3596,3700,3794,3900,4010,4097", "endColumns": "128,102,113,116,121,119,116,100,96,181,168,170,175,167,174,170,121,94,116,98,94,100,95,112,111,86,86,80,103,103,93,105,109,86,94", "endOffsets": "179,282,396,513,635,755,872,973,1070,1252,1421,1592,1768,1936,2111,2282,2404,2499,2616,2715,2810,2911,3007,3120,3232,3319,3406,3487,3591,3695,3789,3895,4005,4092,4187"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15017,16507,16610,16724,16841,16963,17083,17200,17301,17398,17580,17749,17920,18096,18264,18439,18610,18732,18827,18944,19043,19138,19239,19335,19448,19560,19647,19734,19815,19919,20023,20117,20223,20333,20420", "endColumns": "128,102,113,116,121,119,116,100,96,181,168,170,175,167,174,170,121,94,116,98,94,100,95,112,111,86,86,80,103,103,93,105,109,86,94", "endOffsets": "15141,16605,16719,16836,16958,17078,17195,17296,17393,17575,17744,17915,18091,18259,18434,18605,18727,18822,18939,19038,19133,19234,19330,19443,19555,19642,19729,19810,19914,20018,20112,20218,20328,20415,20510"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-te/values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1103,1197,1264,1326,1419,1483,1551,1614,1688,1753,1807,1928,1985,2047,2101,2180,2308,2396,2488,2633,2713,2795,2920,3008,3090,3150,3202,3268,3343,3421,3511,3590,3663,3739,3820,3889,4009,4114,4191,4282,4375,4449,4526,4618,4675,4756,4822,4906,4992,5055,5120,5184,5253,5363,5471,5570,5676,5740,5796", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,89,85,97,86,103,115,90,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,91,144,79,81,124,87,81,59,51,65,74,77,89,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82", "endOffsets": "271,360,450,536,634,721,825,941,1032,1098,1192,1259,1321,1414,1478,1546,1609,1683,1748,1802,1923,1980,2042,2096,2175,2303,2391,2483,2628,2708,2790,2915,3003,3085,3145,3197,3263,3338,3416,3506,3585,3658,3734,3815,3884,4004,4109,4186,4277,4370,4444,4521,4613,4670,4751,4817,4901,4987,5050,5115,5179,5248,5358,5466,5565,5671,5735,5791,5874"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3568,3657,3747,3833,3931,8012,8116,8232,14774,14923,15146,15398,15460,15553,15617,15685,15748,15822,15887,15941,16062,16119,16181,16235,16314,20515,20603,20695,20840,20920,21002,21127,21215,21297,21357,21409,21475,21550,21628,21718,21797,21870,21946,22027,22096,22216,22321,22398,22489,22582,22656,22733,22825,22882,22963,23029,23113,23199,23262,23327,23391,23460,23570,23678,23777,23883,23947,24321", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,88,89,85,97,86,103,115,90,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,91,144,79,81,124,87,81,59,51,65,74,77,89,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82", "endOffsets": "321,3652,3742,3828,3926,4013,8111,8227,8318,14835,15012,15208,15455,15548,15612,15680,15743,15817,15882,15936,16057,16114,16176,16230,16309,16437,20598,20690,20835,20915,20997,21122,21210,21292,21352,21404,21470,21545,21623,21713,21792,21865,21941,22022,22091,22211,22316,22393,22484,22577,22651,22728,22820,22877,22958,23024,23108,23194,23257,23322,23386,23455,23565,23673,23772,23878,23942,23998,24399"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,443,555,668,758,863,982,1060,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2163,2277,2381,2480,2595,2700,2815,2977,24668", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "438,550,663,753,858,977,1055,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2158,2272,2376,2475,2590,2695,2810,2972,3075,24746"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-te/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,436,507", "endColumns": "66,84,85,70,72", "endOffsets": "264,349,435,506,579"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5196,6473,6562,6652,6727", "endColumns": "70,88,89,74,76", "endOffsets": "5262,6557,6647,6722,6799"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8323,8419,14416,14513,14685,15213,15302,24003,24091,24175,24248,24404,24488,24751,25120,25197,25266", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "8414,8501,14508,14608,14769,15297,15393,24086,24170,24243,24316,24483,24573,24823,25192,25261,25378"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-te/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,449,575,686,819,940,1041,1137,1282,1390,1539,1667,1814,1973,2033,2099", "endColumns": "105,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,77", "endOffsets": "298,448,574,685,818,939,1040,1136,1281,1389,1538,1666,1813,1972,2032,2098,2176"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8587,8697,8851,8981,9096,9233,9358,9463,9703,9852,9964,10117,10249,10400,10563,10627,10697", "endColumns": "109,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,81", "endOffsets": "8692,8846,8976,9091,9228,9353,9458,9558,9847,9959,10112,10244,10395,10558,10622,10692,10774"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-te/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9563", "endColumns": "139", "endOffsets": "9698"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-te/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,253,325,386,477,552,612,749,842,921,1050,1139,1212,1294,1395,1447,1514,1584,1658,1716,1762,1807,1851,1900,1964,2031,2094,2157,2221,2263,2339,2402,2473,2560,2649,2715,2773,2846,2898", "endColumns": "44,71,60,90,74,59,136,92,78,128,88,72,81,100,51,66,69,73,57,45,44,43,48,63,66,62,62,63,41,75,62,70,86,88,65,57,72,51,60", "endOffsets": "252,324,385,476,551,611,748,841,920,1049,1138,1211,1293,1394,1446,1513,1583,1657,1715,1761,1806,1850,1899,1963,2030,2093,2156,2220,2262,2338,2401,2472,2559,2648,2714,2772,2845,2897,2958"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4768,4817,4893,4958,5053,5132,5267,5408,5505,5588,5721,5814,5891,5977,6082,6138,6209,6283,6361,6423,6804,6853,6901,6954,7022,7093,7160,7227,7295,7341,7421,7488,7563,7654,7747,7817,7879,7956,16442", "endColumns": "48,75,64,94,78,63,140,96,82,132,92,76,85,104,55,70,73,77,61,49,48,47,52,67,70,66,66,67,45,79,66,74,90,92,69,61,76,55,64", "endOffsets": "4812,4888,4953,5048,5127,5191,5403,5500,5583,5716,5809,5886,5972,6077,6133,6204,6278,6356,6418,6468,6848,6896,6949,7017,7088,7155,7222,7290,7336,7416,7483,7558,7649,7742,7812,7874,7951,8007,16502"}}]}]}