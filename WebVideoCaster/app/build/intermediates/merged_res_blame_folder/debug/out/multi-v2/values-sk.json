{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-sk/values-sk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "97,98,149,150,152,158,159,251,252,253,254,256,257,260,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8221,8316,14260,14355,14538,15062,15141,23836,23926,24007,24076,24224,24307,24566,24919,24997,25065", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "8311,8395,14350,14453,14625,15136,15230,23921,24002,24071,24140,24302,24389,24633,24992,25060,25174"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-sk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "9475", "endColumns": "138", "endOffsets": "9609"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,383,494,570,661,770,901,1013,1161,1242,1339,1427,1520,1632,1751,1853,1982,2111,2241,2401,2524,2644,2771,2888,2977,3070,3187,3305,3401,3500,3605,3741,3886,3991,4087,4167,4244,4333,4416,4513,4589,4671,4764,4863,4952,5045,5129,5230,5323,5417,5533,5609,5706", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "161,272,378,489,565,656,765,896,1008,1156,1237,1334,1422,1515,1627,1746,1848,1977,2106,2236,2396,2519,2639,2766,2883,2972,3065,3182,3300,3396,3495,3600,3736,3881,3986,4082,4162,4239,4328,4411,4508,4584,4666,4759,4858,4947,5040,5124,5225,5318,5412,5528,5604,5701,5790"}, "to": {"startLines": "35,36,37,38,99,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151,154,258,261,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3140,3251,3362,3468,8400,10743,10834,10943,11074,11186,11334,11415,11512,11600,11693,11805,11924,12026,12155,12284,12414,12574,12697,12817,12944,13061,13150,13243,13360,13478,13574,13673,13778,13914,14059,14164,14458,14695,24394,24638,24822,25179,25255,25337,25430,25529,25618,25711,25795,25896,25989,26083,26199,26275,26372", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "3246,3357,3463,3574,8471,10829,10938,11069,11181,11329,11410,11507,11595,11688,11800,11919,12021,12150,12279,12409,12569,12692,12812,12939,13056,13145,13238,13355,13473,13569,13668,13773,13909,14054,14159,14255,14533,14767,24478,24716,24914,25250,25332,25425,25524,25613,25706,25790,25891,25984,26078,26194,26270,26367,26456"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,24483", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,24561"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,267,370,475,580,714,826,927,1023,1205,1392,1585,1778,1970,2167,2360,2491,2588,2696,2794,2888,2979,3073,3200,3318,3406,3491,3577,3677,3782,3877,3982,4092,4178", "endColumns": "114,96,102,104,104,133,111,100,95,181,186,192,192,191,196,192,130,96,107,97,93,90,93,126,117,87,84,85,99,104,94,104,109,85,96", "endOffsets": "165,262,365,470,575,709,821,922,1018,1200,1387,1580,1773,1965,2162,2355,2486,2583,2691,2789,2883,2974,3068,3195,3313,3401,3486,3572,3672,3777,3872,3977,4087,4173,4270"}, "to": {"startLines": "156,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14871,16319,16416,16519,16624,16729,16863,16975,17076,17172,17354,17541,17734,17927,18119,18316,18509,18640,18737,18845,18943,19037,19128,19222,19349,19467,19555,19640,19726,19826,19931,20026,20131,20241,20327", "endColumns": "114,96,102,104,104,133,111,100,95,181,186,192,192,191,196,192,130,96,107,97,93,90,93,126,117,87,84,85,99,104,94,104,109,85,96", "endOffsets": "14981,16411,16514,16619,16724,16858,16970,17071,17167,17349,17536,17729,17922,18114,18311,18504,18635,18732,18840,18938,19032,19123,19217,19344,19462,19550,19635,19721,19821,19926,20021,20126,20236,20322,20419"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-sk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,331,391,483,562,610,762,845,926,1046,1133,1203,1284,1379,1433,1503,1575,1649,1696,1742,1788,1830,1880,1946,2014,2072,2139,2214,2257,2326,2388,2453,2535,2619,2682,2739,2808,2856", "endColumns": "45,76,59,91,78,47,151,82,80,119,86,69,80,94,53,69,71,73,46,45,45,41,49,65,67,57,66,74,42,68,61,64,81,83,62,56,68,47,62", "endOffsets": "253,330,390,482,561,609,761,844,925,1045,1132,1202,1283,1378,1432,1502,1574,1648,1695,1741,1787,1829,1879,1945,2013,2071,2138,2213,2256,2325,2387,2452,2534,2618,2681,2738,2807,2855,2918"}, "to": {"startLines": "51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4719,4769,4850,4914,5010,5093,5216,5372,5459,5544,5668,5759,5833,5918,6017,6075,6149,6225,6303,6354,6734,6784,6830,6884,6954,7026,7088,7159,7238,7285,7358,7424,7493,7579,7667,7734,7795,7868,16252", "endColumns": "49,80,63,95,82,51,155,86,84,123,90,73,84,98,57,73,75,77,50,49,49,45,53,69,71,61,70,78,46,72,65,68,85,87,66,60,72,51,66", "endOffsets": "4764,4845,4909,5005,5088,5140,5367,5454,5539,5663,5754,5828,5913,6012,6070,6144,6220,6298,6349,6399,6779,6825,6879,6949,7021,7083,7154,7233,7280,7353,7419,7488,7574,7662,7729,7790,7863,7915,16314"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1147,1246,1322,1387,1477,1541,1607,1661,1730,1790,1844,1961,2021,2083,2137,2209,2339,2426,2518,2657,2726,2804,2935,3023,3103,3157,3208,3274,3346,3423,3506,3588,3660,3737,3810,3881,3986,4074,4146,4238,4334,4408,4482,4578,4630,4712,4779,4866,4953,5015,5079,5142,5210,5316,5423,5521,5638,5696,5751", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "373,448,523,601,693,776,868,996,1077,1142,1241,1317,1382,1472,1536,1602,1656,1725,1785,1839,1956,2016,2078,2132,2204,2334,2421,2513,2652,2721,2799,2930,3018,3098,3152,3203,3269,3341,3418,3501,3583,3655,3732,3805,3876,3981,4069,4141,4233,4329,4403,4477,4573,4625,4707,4774,4861,4948,5010,5074,5137,5205,5311,5418,5516,5633,5691,5746,5825"}, "to": {"startLines": "2,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3579,3654,3729,3807,3899,7920,8012,8140,14630,14772,14986,15235,15300,15390,15454,15520,15574,15643,15703,15757,15874,15934,15996,16050,16122,20424,20511,20603,20742,20811,20889,21020,21108,21188,21242,21293,21359,21431,21508,21591,21673,21745,21822,21895,21966,22071,22159,22231,22323,22419,22493,22567,22663,22715,22797,22864,22951,23038,23100,23164,23227,23295,23401,23508,23606,23723,23781,24145", "endLines": "7,39,40,41,42,43,94,95,96,153,155,157,160,161,162,163,164,165,166,167,168,169,170,171,172,173,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,255", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "423,3649,3724,3802,3894,3977,8007,8135,8216,14690,14866,15057,15295,15385,15449,15515,15569,15638,15698,15752,15869,15929,15991,16045,16117,16247,20506,20598,20737,20806,20884,21015,21103,21183,21237,21288,21354,21426,21503,21586,21668,21740,21817,21890,21961,22066,22154,22226,22318,22414,22488,22562,22658,22710,22792,22859,22946,23033,23095,23159,23222,23290,23396,23503,23601,23718,23776,23831,24219"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-sk/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,352,450,518", "endColumns": "66,86,97,67,60", "endOffsets": "264,351,449,517,578"}, "to": {"startLines": "57,72,73,74,75", "startColumns": "4,4,4,4,4", "startOffsets": "5145,6404,6495,6597,6669", "endColumns": "70,90,101,71,64", "endOffsets": "5211,6490,6592,6664,6729"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-sk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8476,8582,8742,8866,8976,9132,9260,9373,9614,9783,9894,10064,10194,10357,10521,10589,10656", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "8577,8737,8861,8971,9127,9255,9368,9470,9778,9889,10059,10189,10352,10516,10584,10651,10738"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "44,45,46,47,48,49,50,262", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3982,4078,4180,4281,4379,4489,4597,24721", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "4073,4175,4276,4374,4484,4592,4714,24817"}}]}]}