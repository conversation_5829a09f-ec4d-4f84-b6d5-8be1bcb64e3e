{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-bn/values-bn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,322,437,567,652,747,860,998,1115,1253,1334,1436,1526,1623,1748,1873,1980,2101,2220,2350,2528,2650,2766,2885,3015,3106,3197,3326,3469,3562,3663,3769,3891,4019,4128,4224,4302,4384,4471,4557,4656,4732,4813,4910,5010,5100,5201,5284,5386,5481,5584,5698,5774,5870", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "184,317,432,562,647,742,855,993,1110,1248,1329,1431,1521,1618,1743,1868,1975,2096,2215,2345,2523,2645,2761,2880,3010,3101,3192,3321,3464,3557,3658,3764,3886,4014,4123,4219,4297,4379,4466,4552,4651,4727,4808,4905,5005,5095,5196,5279,5381,5476,5579,5693,5769,5865,5955"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3051,3185,3318,3433,8390,10642,10737,10850,10988,11105,11243,11324,11426,11516,11613,11738,11863,11970,12091,12210,12340,12518,12640,12756,12875,13005,13096,13187,13316,13459,13552,13653,13759,13881,14009,14118,14402,14632,24301,24558,24745,25105,25181,25262,25359,25459,25549,25650,25733,25835,25930,26033,26147,26223,26319", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "3180,3313,3428,3558,8470,10732,10845,10983,11100,11238,11319,11421,11511,11608,11733,11858,11965,12086,12205,12335,12513,12635,12751,12870,13000,13091,13182,13311,13454,13547,13648,13754,13876,14004,14113,14209,14475,14709,24383,24639,24839,25176,25257,25354,25454,25544,25645,25728,25830,25925,26028,26142,26218,26314,26404"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-bn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,255,325,384,464,543,599,725,814,894,1014,1098,1166,1248,1334,1380,1445,1513,1587,1632,1676,1721,1761,1813,1880,1950,2010,2074,2141,2181,2250,2312,2381,2465,2553,2623,2685,2760,2807", "endColumns": "46,69,58,79,78,55,125,88,79,119,83,67,81,85,45,64,67,73,44,43,44,39,51,66,69,59,63,66,39,68,61,68,83,87,69,61,74,46,61", "endOffsets": "254,324,383,463,542,598,724,813,893,1013,1097,1165,1247,1333,1379,1444,1512,1586,1631,1675,1720,1760,1812,1879,1949,2009,2073,2140,2180,2249,2311,2380,2464,2552,2622,2684,2759,2806,2868"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4759,4810,4884,4947,5031,5114,5245,5375,5468,5552,5676,5764,5836,5922,6012,6062,6131,6203,6281,6330,6707,6756,6800,6856,6927,7001,7065,7133,7204,7248,7321,7387,7460,7548,7640,7714,7780,7859,16185", "endColumns": "50,73,62,83,82,59,129,92,83,123,87,71,85,89,49,68,71,77,48,47,48,43,55,70,73,63,67,70,43,72,65,72,87,91,73,65,78,50,65", "endOffsets": "4805,4879,4942,5026,5109,5169,5370,5463,5547,5671,5759,5831,5917,6007,6057,6126,6198,6276,6325,6373,6751,6795,6851,6922,6996,7060,7128,7199,7243,7316,7382,7455,7543,7635,7709,7775,7854,7905,16246"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-bn/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9456", "endColumns": "151", "endOffsets": "9603"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4030,4129,4231,4333,4436,4537,4639,24644", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "4124,4226,4328,4431,4532,4634,4754,24740"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8212,8306,14214,14304,14480,14997,15076,23746,23833,23922,23992,24142,24220,24475,24844,24919,24987", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "8301,8385,14299,14397,14561,15071,15177,23828,23917,23987,24057,24215,24296,24553,24914,24982,25100"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-bn/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,346,430,504", "endColumns": "66,80,83,73,73", "endOffsets": "264,345,429,503,577"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5174,6378,6463,6551,6629", "endColumns": "70,84,87,77,77", "endOffsets": "5240,6458,6546,6624,6702"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1106,1197,1263,1324,1414,1478,1545,1606,1675,1737,1791,1898,1957,2018,2072,2146,2266,2351,2435,2570,2641,2711,2843,2930,3013,3071,3127,3193,3266,3346,3441,3523,3592,3668,3748,3817,3926,4021,4104,4194,4289,4363,4437,4530,4584,4669,4736,4822,4907,4969,5033,5096,5162,5264,5363,5456,5555,5617,5677", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "266,364,457,540,641,733,837,954,1035,1101,1192,1258,1319,1409,1473,1540,1601,1670,1732,1786,1893,1952,2013,2067,2141,2261,2346,2430,2565,2636,2706,2838,2925,3008,3066,3122,3188,3261,3341,3436,3518,3587,3663,3743,3812,3921,4016,4099,4189,4284,4358,4432,4525,4579,4664,4731,4817,4902,4964,5028,5091,5157,5259,5358,5451,5550,5612,5672,5752"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3563,3661,3754,3837,3938,7910,8014,8131,14566,14714,14931,15182,15243,15333,15397,15464,15525,15594,15656,15710,15817,15876,15937,15991,16065,20335,20420,20504,20639,20710,20780,20912,20999,21082,21140,21196,21262,21335,21415,21510,21592,21661,21737,21817,21886,21995,22090,22173,22263,22358,22432,22506,22599,22653,22738,22805,22891,22976,23038,23102,23165,23231,23333,23432,23525,23624,23686,24062", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "316,3656,3749,3832,3933,4025,8009,8126,8207,14627,14800,14992,15238,15328,15392,15459,15520,15589,15651,15705,15812,15871,15932,15986,16060,16180,20415,20499,20634,20705,20775,20907,20994,21077,21135,21191,21257,21330,21410,21505,21587,21656,21732,21812,21881,21990,22085,22168,22258,22353,22427,22501,22594,22648,22733,22800,22886,22971,23033,23097,23160,23226,23328,23427,23520,23619,23681,23741,24137"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,24388", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,24470"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,280,391,505,619,729,838,936,1029,1203,1399,1595,1795,1987,2186,2382,2498,2594,2705,2805,2903,3009,3108,3211,3325,3410,3493,3576,3680,3781,3874,3980,4085,4172", "endColumns": "125,98,110,113,113,109,108,97,92,173,195,195,199,191,198,195,115,95,110,99,97,105,98,102,113,84,82,82,103,100,92,105,104,86,92", "endOffsets": "176,275,386,500,614,724,833,931,1024,1198,1394,1590,1790,1982,2181,2377,2493,2589,2700,2800,2898,3004,3103,3206,3320,3405,3488,3571,3675,3776,3869,3975,4080,4167,4260"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14805,16251,16350,16461,16575,16689,16799,16908,17006,17099,17273,17469,17665,17865,18057,18256,18452,18568,18664,18775,18875,18973,19079,19178,19281,19395,19480,19563,19646,19750,19851,19944,20050,20155,20242", "endColumns": "125,98,110,113,113,109,108,97,92,173,195,195,199,191,198,195,115,95,110,99,97,105,98,102,113,84,82,82,103,100,92,105,104,86,92", "endOffsets": "14926,16345,16456,16570,16684,16794,16903,17001,17094,17268,17464,17660,17860,18052,18251,18447,18563,18659,18770,18870,18968,19074,19173,19276,19390,19475,19558,19641,19745,19846,19939,20045,20150,20237,20330"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-bn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8475,8584,8743,8871,8982,9118,9240,9352,9608,9751,9860,10016,10144,10277,10425,10485,10552", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "8579,8738,8866,8977,9113,9235,9347,9451,9746,9855,10011,10139,10272,10420,10480,10547,10637"}}]}]}