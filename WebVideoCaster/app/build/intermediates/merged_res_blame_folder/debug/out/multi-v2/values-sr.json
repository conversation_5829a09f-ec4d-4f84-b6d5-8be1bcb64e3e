{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-sr/values-sr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-sr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "99,100,101,102,103,104,105,106,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8463,8569,8725,8851,8961,9115,9242,9354,9586,9735,9842,10002,10129,10278,10420,10488,10553", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "8564,8720,8846,8956,9110,9237,9349,9451,9730,9837,9997,10124,10273,10415,10483,10548,10628"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-sr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "9456", "endColumns": "129", "endOffsets": "9581"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,275,398,508,588,678,786,918,1033,1173,1254,1350,1441,1535,1647,1768,1869,2006,2142,2271,2447,2568,2684,2806,2925,3017,3111,3224,3350,3446,3544,3649,3786,3931,4036,4134,4207,4287,4372,4456,4559,4635,4714,4807,4906,4995,5089,5172,5276,5369,5466,5595,5671,5772", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "160,270,393,503,583,673,781,913,1028,1168,1249,1345,1436,1530,1642,1763,1864,2001,2137,2266,2442,2563,2679,2801,2920,3012,3106,3219,3345,3441,3539,3644,3781,3926,4031,4129,4202,4282,4367,4451,4554,4630,4709,4802,4901,4990,5084,5167,5271,5364,5461,5590,5666,5767,5860"}, "to": {"startLines": "34,35,36,37,98,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,150,153,257,260,262,266,267,268,269,270,271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3099,3209,3319,3442,8383,10633,10723,10831,10963,11078,11218,11299,11395,11486,11580,11692,11813,11914,12051,12187,12316,12492,12613,12729,12851,12970,13062,13156,13269,13395,13491,13589,13694,13831,13976,14081,14377,14602,24154,24399,24584,24954,25030,25109,25202,25301,25390,25484,25567,25671,25764,25861,25990,26066,26167", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "3204,3314,3437,3547,8458,10718,10826,10958,11073,11213,11294,11390,11481,11575,11687,11808,11909,12046,12182,12311,12487,12608,12724,12846,12965,13057,13151,13264,13390,13486,13584,13689,13826,13971,14076,14174,14445,14677,24234,24478,24682,25025,25104,25197,25296,25385,25479,25562,25666,25759,25856,25985,26061,26162,26255"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-sr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,252,319,380,467,546,599,730,824,902,1013,1096,1165,1245,1343,1389,1460,1534,1608,1654,1701,1747,1787,1852,1921,1993,2068,2134,2202,2258,2325,2387,2452,2535,2617,2679,2741,2810,2858", "endColumns": "43,66,60,86,78,52,130,93,77,110,82,68,79,97,45,70,73,73,45,46,45,39,64,68,71,74,65,67,55,66,61,64,82,81,61,61,68,47,67", "endOffsets": "251,318,379,466,545,598,729,823,901,1012,1095,1164,1244,1342,1388,1459,1533,1607,1653,1700,1746,1786,1851,1920,1992,2067,2133,2201,2257,2324,2386,2451,2534,2616,2678,2740,2809,2857,2925"}, "to": {"startLines": "50,51,52,53,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4710,4758,4829,4894,4985,5068,5196,5331,5429,5511,5626,5713,5786,5870,5972,6022,6097,6175,6253,6303,6664,6714,6758,6827,6900,6976,7055,7125,7197,7257,7328,7394,7463,7550,7636,7702,7768,7841,16155", "endColumns": "47,70,64,90,82,56,134,97,81,114,86,72,83,101,49,74,77,77,49,50,49,43,68,72,75,78,69,71,59,70,65,68,86,85,65,65,72,51,71", "endOffsets": "4753,4824,4889,4980,5063,5120,5326,5424,5506,5621,5708,5781,5865,5967,6017,6092,6170,6248,6298,6349,6709,6753,6822,6895,6971,7050,7120,7192,7252,7323,7389,7458,7545,7631,7697,7763,7836,7888,16222"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1125,1217,1285,1348,1451,1511,1577,1633,1704,1764,1818,1930,1987,2048,2102,2178,2303,2389,2472,2610,2691,2774,2905,2993,3071,3125,3181,3247,3321,3399,3488,3570,3645,3721,3796,3867,3974,4064,4137,4229,4325,4397,4473,4569,4622,4704,4771,4858,4945,5007,5071,5134,5203,5308,5418,5514,5622,5680,5740", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "316,392,468,548,655,748,842,973,1054,1120,1212,1280,1343,1446,1506,1572,1628,1699,1759,1813,1925,1982,2043,2097,2173,2298,2384,2467,2605,2686,2769,2900,2988,3066,3120,3176,3242,3316,3394,3483,3565,3640,3716,3791,3862,3969,4059,4132,4224,4320,4392,4468,4564,4617,4699,4766,4853,4940,5002,5066,5129,5198,5303,5413,5509,5617,5675,5735,5815"}, "to": {"startLines": "2,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3552,3628,3704,3784,3891,7893,7987,8118,14536,14682,14901,15137,15200,15303,15363,15429,15485,15556,15616,15670,15782,15839,15900,15954,16030,20154,20240,20323,20461,20542,20625,20756,20844,20922,20976,21032,21098,21172,21250,21339,21421,21496,21572,21647,21718,21825,21915,21988,22080,22176,22248,22324,22420,22473,22555,22622,22709,22796,22858,22922,22985,23054,23159,23269,23365,23473,23531,23909", "endLines": "6,38,39,40,41,42,93,94,95,152,154,156,159,160,161,162,163,164,165,166,167,168,169,170,171,172,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,254", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "366,3623,3699,3779,3886,3979,7982,8113,8194,14597,14769,14964,15195,15298,15358,15424,15480,15551,15611,15665,15777,15834,15895,15949,16025,16150,20235,20318,20456,20537,20620,20751,20839,20917,20971,21027,21093,21167,21245,21334,21416,21491,21567,21642,21713,21820,21910,21983,22075,22171,22243,22319,22415,22468,22550,22617,22704,22791,22853,22917,22980,23049,23154,23264,23360,23468,23526,23586,23984"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,280,385,492,607,730,837,933,1021,1199,1373,1547,1721,1887,2062,2232,2354,2448,2554,2652,2745,2841,2934,3050,3162,3248,3331,3417,3522,3631,3724,3824,3929,4015", "endColumns": "126,97,104,106,114,122,106,95,87,177,173,173,173,165,174,169,121,93,105,97,92,95,92,115,111,85,82,85,104,108,92,99,104,85,93", "endOffsets": "177,275,380,487,602,725,832,928,1016,1194,1368,1542,1716,1882,2057,2227,2349,2443,2549,2647,2740,2836,2929,3045,3157,3243,3326,3412,3517,3626,3719,3819,3924,4010,4104"}, "to": {"startLines": "155,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14774,16227,16325,16430,16537,16652,16775,16882,16978,17066,17244,17418,17592,17766,17932,18107,18277,18399,18493,18599,18697,18790,18886,18979,19095,19207,19293,19376,19462,19567,19676,19769,19869,19974,20060", "endColumns": "126,97,104,106,114,122,106,95,87,177,173,173,173,165,174,169,121,93,105,97,92,95,92,115,111,85,82,85,104,108,92,99,104,85,93", "endOffsets": "14896,16320,16425,16532,16647,16770,16877,16973,17061,17239,17413,17587,17761,17927,18102,18272,18394,18488,18594,18692,18785,18881,18974,19090,19202,19288,19371,19457,19562,19671,19764,19864,19969,20055,20149"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,24239", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,24321"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "96,97,148,149,151,157,158,250,251,252,253,255,256,259,263,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8199,8296,14179,14276,14450,14969,15046,23591,23683,23768,23839,23989,24069,24326,24687,24766,24836", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "8291,8378,14271,14372,14531,15041,15132,23678,23763,23834,23904,24064,24149,24394,24761,24831,24949"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "43,44,45,46,47,48,49,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3984,4082,4184,4281,4385,4489,4594,24483", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4077,4179,4276,4380,4484,4589,4705,24579"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-sr/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,345,425,493", "endColumns": "66,79,79,67,65", "endOffsets": "264,344,424,492,558"}, "to": {"startLines": "56,71,72,73,74", "startColumns": "4,4,4,4,4", "startOffsets": "5125,6354,6438,6522,6594", "endColumns": "70,83,83,71,69", "endOffsets": "5191,6433,6517,6589,6659"}}]}]}