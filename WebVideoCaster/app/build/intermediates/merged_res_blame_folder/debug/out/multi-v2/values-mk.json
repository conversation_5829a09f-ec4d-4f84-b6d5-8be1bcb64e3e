{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-mk/values-mk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,284,396,510,586,677,786,915,1032,1176,1257,1352,1442,1534,1645,1767,1867,2008,2148,2286,2454,2581,2698,2822,2942,3033,3127,3249,3380,3476,3574,3683,3821,3968,4080,4177,4250,4327,4415,4497,4607,4691,4770,4867,4965,5058,5151,5235,5338,5434,5531,5660,5742,5849", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "165,279,391,505,581,672,781,910,1027,1171,1252,1347,1437,1529,1640,1762,1862,2003,2143,2281,2449,2576,2693,2817,2937,3028,3122,3244,3375,3471,3569,3678,3816,3963,4075,4172,4245,4322,4410,4492,4602,4686,4765,4862,4960,5053,5146,5230,5333,5429,5526,5655,5737,5844,5943"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,3151,3265,3377,8325,10644,10735,10844,10973,11090,11234,11315,11410,11500,11592,11703,11825,11925,12066,12206,12344,12512,12639,12756,12880,13000,13091,13185,13307,13438,13534,13632,13741,13879,14026,14138,14434,14658,24317,24562,24745,25128,25212,25291,25388,25486,25579,25672,25756,25859,25955,26052,26181,26263,26370", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "3146,3260,3372,3486,8396,10730,10839,10968,11085,11229,11310,11405,11495,11587,11698,11820,11920,12061,12201,12339,12507,12634,12751,12875,12995,13086,13180,13302,13433,13529,13627,13736,13874,14021,14133,14230,14502,14730,24400,24639,24850,25207,25286,25383,25481,25574,25667,25751,25854,25950,26047,26176,26258,26365,26464"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3920,4018,4120,4217,4315,4420,4523,24644", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "4013,4115,4212,4310,4415,4518,4634,24740"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,24405", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,24485"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-mk/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,341,422,490", "endColumns": "66,75,80,67,66", "endOffsets": "264,340,421,489,556"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5052,6322,6402,6487,6559", "endColumns": "70,79,84,71,70", "endOffsets": "5118,6397,6482,6554,6625"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,279,383,491,603,724,832,927,1015,1195,1377,1559,1741,1922,2103,2286,2399,2495,2599,2697,2791,2888,2981,3095,3222,3305,3388,3471,3574,3685,3778,3876,3981,4067", "endColumns": "124,98,103,107,111,120,107,94,87,179,181,181,181,180,180,182,112,95,103,97,93,96,92,113,126,82,82,82,102,110,92,97,104,85,91", "endOffsets": "175,274,378,486,598,719,827,922,1010,1190,1372,1554,1736,1917,2098,2281,2394,2490,2594,2692,2786,2883,2976,3090,3217,3300,3383,3466,3569,3680,3773,3871,3976,4062,4154"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14826,16311,16410,16514,16622,16734,16855,16963,17058,17146,17326,17508,17690,17872,18053,18234,18417,18530,18626,18730,18828,18922,19019,19112,19226,19353,19436,19519,19602,19705,19816,19909,20007,20112,20198", "endColumns": "124,98,103,107,111,120,107,94,87,179,181,181,181,180,180,182,112,95,103,97,93,96,92,113,126,82,82,82,102,110,92,97,104,85,91", "endOffsets": "14946,16405,16509,16617,16729,16850,16958,17053,17141,17321,17503,17685,17867,18048,18229,18412,18525,18621,18725,18823,18917,19014,19107,19221,19348,19431,19514,19597,19700,19811,19904,20002,20107,20193,20285"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8131,8235,14235,14331,14507,15021,15098,23742,23834,23918,23989,24144,24228,24490,24855,24936,25007", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "8230,8320,14326,14429,14587,15093,15183,23829,23913,23984,24054,24223,24312,24557,24931,25002,25123"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-mk/values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1073,1164,1234,1298,1401,1464,1529,1589,1657,1720,1775,1903,1960,2022,2077,2152,2292,2379,2462,2595,2677,2762,2908,2995,3072,3126,3181,3247,3320,3396,3485,3563,3636,3712,3787,3857,3966,4054,4129,4221,4313,4387,4461,4553,4606,4688,4755,4838,4925,4987,5051,5114,5184,5298,5413,5515,5627,5685,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "266,347,427,509,606,695,791,915,1002,1068,1159,1229,1293,1396,1459,1524,1584,1652,1715,1770,1898,1955,2017,2072,2147,2287,2374,2457,2590,2672,2757,2903,2990,3067,3121,3176,3242,3315,3391,3480,3558,3631,3707,3782,3852,3961,4049,4124,4216,4308,4382,4456,4548,4601,4683,4750,4833,4920,4982,5046,5109,5179,5293,5408,5510,5622,5680,5739,5824"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3491,3572,3652,3734,3831,7824,7920,8044,14592,14735,14951,15188,15252,15355,15418,15483,15543,15611,15674,15729,15857,15914,15976,16031,16106,20290,20377,20460,20593,20675,20760,20906,20993,21070,21124,21179,21245,21318,21394,21483,21561,21634,21710,21785,21855,21964,22052,22127,22219,22311,22385,22459,22551,22604,22686,22753,22836,22923,22985,23049,23112,23182,23296,23411,23513,23625,23683,24059", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "316,3567,3647,3729,3826,3915,7915,8039,8126,14653,14821,15016,15247,15350,15413,15478,15538,15606,15669,15724,15852,15909,15971,16026,16101,16241,20372,20455,20588,20670,20755,20901,20988,21065,21119,21174,21240,21313,21389,21478,21556,21629,21705,21780,21850,21959,22047,22122,22214,22306,22380,22454,22546,22599,22681,22748,22831,22918,22980,23044,23107,23177,23291,23406,23508,23620,23678,23737,24139"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-mk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,320,383,473,543,597,736,816,898,1021,1107,1178,1266,1370,1425,1497,1571,1645,1693,1740,1781,1821,1874,1944,2016,2083,2148,2216,2256,2328,2391,2456,2541,2622,2684,2745,2814,2862", "endColumns": "45,65,62,89,69,53,138,79,81,122,85,70,87,103,54,71,73,73,47,46,40,39,52,69,71,66,64,67,39,71,62,64,84,80,61,60,68,47,60", "endOffsets": "253,319,382,472,542,596,735,815,897,1020,1106,1177,1265,1369,1424,1496,1570,1644,1692,1739,1780,1820,1873,1943,2015,2082,2147,2215,2255,2327,2390,2455,2540,2621,2683,2744,2813,2861,2922"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4639,4689,4759,4826,4920,4994,5123,5266,5350,5436,5563,5653,5728,5820,5928,5987,6063,6141,6219,6271,6630,6675,6719,6776,6850,6926,6997,7066,7138,7182,7258,7325,7394,7483,7568,7634,7699,7772,16246", "endColumns": "49,69,66,93,73,57,142,83,85,126,89,74,91,107,58,75,77,77,51,50,44,43,56,73,75,70,68,71,43,75,66,68,88,84,65,64,72,51,64", "endOffsets": "4684,4754,4821,4915,4989,5047,5261,5345,5431,5558,5648,5723,5815,5923,5982,6058,6136,6214,6266,6317,6670,6714,6771,6845,6921,6992,7061,7133,7177,7253,7320,7389,7478,7563,7629,7694,7767,7819,16306"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-mk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8401,8508,8669,8802,8912,9057,9190,9310,9557,9714,9821,9987,10120,10273,10432,10501,10565", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "8503,8664,8797,8907,9052,9185,9305,9415,9709,9816,9982,10115,10268,10427,10496,10560,10639"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-mk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9420", "endColumns": "136", "endOffsets": "9552"}}]}]}