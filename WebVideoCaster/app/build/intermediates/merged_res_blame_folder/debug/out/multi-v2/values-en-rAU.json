{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,264,363,464,569,683,795,890,973,1148,1319,1494,1671,1840,2018,2193,2307,2399,2501,2597,2693,2787,2879,2986,3094,3177,3259,3341,3438,3538,3631,3729,3833,3919", "endColumns": "115,92,98,100,104,113,111,94,82,174,170,174,176,168,177,174,113,91,101,95,95,93,91,106,107,82,81,81,96,99,92,97,103,85,93", "endOffsets": "166,259,358,459,564,678,790,885,968,1143,1314,1489,1666,1835,2013,2188,2302,2394,2496,2592,2688,2782,2874,2981,3089,3172,3254,3336,3433,3533,3626,3724,3828,3914,4008"}, "to": {"startLines": "79,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8028,8315,8408,8507,8608,8713,8827,8939,9034,9117,9292,9463,9638,9815,9984,10162,10337,10451,10543,10645,10741,10837,10931,11023,11130,11238,11321,11403,11485,11582,11682,11775,11873,11977,12063", "endColumns": "115,92,98,100,104,113,111,94,82,174,170,174,176,168,177,174,113,91,101,95,95,93,91,106,107,82,81,81,96,99,92,97,103,85,93", "endOffsets": "8139,8403,8502,8603,8708,8822,8934,9029,9112,9287,9458,9633,9810,9979,10157,10332,10446,10538,10640,10736,10832,10926,11018,11125,11233,11316,11398,11480,11577,11677,11770,11868,11972,12058,12152"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "33,34,35,36,37,38,39,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3205,3301,3403,3502,3601,3705,3808,12941", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3296,3398,3497,3596,3700,3803,3919,13037"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,12702", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,12780"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "40,41,74,75,77,80,81,116,117,118,119,120,121,124,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3924,4016,7598,7692,7865,8144,8226,12157,12246,12330,12395,12459,12537,12785,13140,13217,13283", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "4011,4093,7687,7786,7947,8221,8310,12241,12325,12390,12454,12532,12614,12853,13212,13278,13399"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4072,4148,4231,4314,4412,4488,4568,4665,4762,4858,4953,5037,5139,5236,5335,5451,5527,5623", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4067,4143,4226,4309,4407,4483,4563,4660,4757,4853,4948,5032,5134,5231,5330,5446,5522,5618,5709"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,122,125,127,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2762,2876,2988,3095,4098,4176,4267,4376,4508,4620,4752,4832,4927,5014,5107,5222,5343,5443,5566,5685,5809,5967,6084,6196,6316,6438,6526,6620,6733,6853,6946,7044,7142,7267,7402,7504,7791,7952,12619,12858,13042,13404,13480,13560,13657,13754,13850,13945,14029,14131,14228,14327,14443,14519,14615", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "2871,2983,3090,3200,4171,4262,4371,4503,4615,4747,4827,4922,5009,5102,5217,5338,5438,5561,5680,5804,5962,6079,6191,6311,6433,6521,6615,6728,6848,6941,7039,7137,7262,7397,7499,7593,7860,8023,12697,12936,13135,13475,13555,13652,13749,13845,13940,14024,14126,14223,14322,14438,14514,14610,14701"}}]}]}