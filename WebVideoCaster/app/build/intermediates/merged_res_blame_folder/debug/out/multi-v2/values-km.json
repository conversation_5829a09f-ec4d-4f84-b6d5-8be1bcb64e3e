{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-km/values-km.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-km/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,258,321,382,461,533,580,713,808,895,1028,1117,1191,1272,1380,1425,1482,1542,1616,1670,1713,1754,1793,1839,1897,1958,2025,2082,2137,2178,2251,2314,2379,2464,2544,2606,2668,2739,2784", "endColumns": "49,62,60,78,71,46,132,94,86,132,88,73,80,107,44,56,59,73,53,42,40,38,45,57,60,66,56,54,40,72,62,64,84,79,61,61,70,44,62", "endOffsets": "257,320,381,460,532,579,712,807,894,1027,1116,1190,1271,1379,1424,1481,1541,1615,1669,1712,1753,1792,1838,1896,1957,2024,2081,2136,2177,2250,2313,2378,2463,2543,2605,2667,2738,2783,2846"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4617,4684,4749,4832,4908,5030,5167,5266,5357,5494,5587,5665,5750,5862,5911,5972,6036,6114,6172,6522,6567,6610,6660,6722,6787,6858,6919,6978,7023,7100,7167,7236,7325,7409,7475,7541,7616,15953", "endColumns": "53,66,64,82,75,50,136,98,90,136,92,77,84,111,48,60,63,77,57,46,44,42,49,61,64,70,60,58,44,76,66,68,88,83,65,65,74,48,66", "endOffsets": "4612,4679,4744,4827,4903,4954,5162,5261,5352,5489,5582,5660,5745,5857,5906,5967,6031,6109,6167,6214,6562,6605,6655,6717,6782,6853,6914,6973,7018,7095,7162,7231,7320,7404,7470,7536,7611,7660,16015"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7957,8042,13986,14090,14260,14771,14855,23364,23449,23536,23601,23749,23829,24082,24440,24524,24590", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "8037,8117,14085,14183,14343,14850,14933,23444,23531,23596,23661,23824,23909,24153,24519,24585,24703"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-km/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9200", "endColumns": "148", "endOffsets": "9344"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-km/values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2795,2892,2970,3025,3081,3147,3227,3317,3403,3488,3567,3644,3714,3789,3901,3989,4062,4162,4261,4335,4411,4518,4572,4662,4735,4826,4922,4984,5048,5111,5182,5281,5379,5471,5567,5625,5685", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2790,2887,2965,3020,3076,3142,3222,3312,3398,3483,3562,3639,3709,3784,3896,3984,4057,4157,4256,4330,4406,4513,4567,4657,4730,4821,4917,4979,5043,5106,5177,5276,5374,5466,5562,5620,5680,5763"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3450,3528,3604,3684,3763,7665,7765,7877,14348,14491,14701,14938,15000,15087,15150,15215,15274,15339,15400,15457,15576,15634,15695,15752,15823,19876,19962,20040,20178,20253,20324,20474,20571,20649,20704,20760,20826,20906,20996,21082,21167,21246,21323,21393,21468,21580,21668,21741,21841,21940,22014,22090,22197,22251,22341,22414,22505,22601,22663,22727,22790,22861,22960,23058,23150,23246,23304,23666", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "314,3523,3599,3679,3758,3837,7760,7872,7952,14408,14580,14766,14995,15082,15145,15210,15269,15334,15395,15452,15571,15629,15690,15747,15818,15948,19957,20035,20173,20248,20319,20469,20566,20644,20699,20755,20821,20901,20991,21077,21162,21241,21318,21388,21463,21575,21663,21736,21836,21935,22009,22085,22192,22246,22336,22409,22500,22596,22658,22722,22785,22856,22955,23053,23145,23241,23299,23359,23744"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3842,3937,4040,4138,4238,4339,4451,24242", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3932,4035,4133,4233,4334,4446,4558,24338"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,23998", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,24077"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-km/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,342,422,492", "endColumns": "66,76,79,69,59", "endOffsets": "264,341,421,491,551"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "4959,6219,6300,6384,6458", "endColumns": "70,80,83,73,63", "endOffsets": "5025,6295,6379,6453,6517"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,266,367,471,576,688,801,901,986,1165,1334,1506,1679,1850,2023,2194,2305,2415,2523,2617,2711,2801,2893,2998,3112,3195,3276,3357,3451,3553,3646,3744,3846,3934", "endColumns": "115,94,100,103,104,111,112,99,84,178,168,171,172,170,172,170,110,109,107,93,93,89,91,104,113,82,80,80,93,101,92,97,101,87,92", "endOffsets": "166,261,362,466,571,683,796,896,981,1160,1329,1501,1674,1845,2018,2189,2300,2410,2518,2612,2706,2796,2888,2993,3107,3190,3271,3352,3446,3548,3641,3739,3841,3929,4022"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14585,16020,16115,16216,16320,16425,16537,16650,16750,16835,17014,17183,17355,17528,17699,17872,18043,18154,18264,18372,18466,18560,18650,18742,18847,18961,19044,19125,19206,19300,19402,19495,19593,19695,19783", "endColumns": "115,94,100,103,104,111,112,99,84,178,168,171,172,170,172,170,110,109,107,93,93,89,91,104,113,82,80,80,93,101,92,97,101,87,92", "endOffsets": "14696,16110,16211,16315,16420,16532,16645,16745,16830,17009,17178,17350,17523,17694,17867,18038,18149,18259,18367,18461,18555,18645,18737,18842,18956,19039,19120,19201,19295,19397,19490,19588,19690,19778,19871"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,386,498,577,679,799,939,1063,1211,1298,1394,1490,1596,1716,1837,1938,2060,2182,2306,2464,2582,2692,2802,2922,3019,3115,3237,3372,3472,3573,3681,3801,3925,4038,4141,4213,4291,4375,4459,4556,4632,4712,4808,4906,4998,5103,5186,5284,5378,5484,5597,5673,5776", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "164,279,381,493,572,674,794,934,1058,1206,1293,1389,1485,1591,1711,1832,1933,2055,2177,2301,2459,2577,2687,2797,2917,3014,3110,3232,3367,3467,3568,3676,3796,3920,4033,4136,4208,4286,4370,4454,4551,4627,4707,4803,4901,4993,5098,5181,5279,5373,5479,5592,5668,5771,5867"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,3121,3236,3338,8122,10422,10524,10644,10784,10908,11056,11143,11239,11335,11441,11561,11682,11783,11905,12027,12151,12309,12427,12537,12647,12767,12864,12960,13082,13217,13317,13418,13526,13646,13770,13883,14188,14413,23914,24158,24343,24708,24784,24864,24960,25058,25150,25255,25338,25436,25530,25636,25749,25825,25928", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "3116,3231,3333,3445,8196,10519,10639,10779,10903,11051,11138,11234,11330,11436,11556,11677,11778,11900,12022,12146,12304,12422,12532,12642,12762,12859,12955,13077,13212,13312,13413,13521,13641,13765,13878,13981,14255,14486,23993,24237,24435,24779,24859,24955,25053,25145,25250,25333,25431,25525,25631,25744,25820,25923,26019"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-km/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8201,8303,8458,8579,8684,8846,8970,9091,9349,9507,9624,9795,9920,10065,10223,10287,10345", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "8298,8453,8574,8679,8841,8965,9086,9195,9502,9619,9790,9915,10060,10218,10282,10340,10417"}}]}]}