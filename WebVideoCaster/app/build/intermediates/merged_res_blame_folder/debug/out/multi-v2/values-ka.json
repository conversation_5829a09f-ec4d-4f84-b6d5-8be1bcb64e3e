{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-ka/values-ka.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,24477", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,24554"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ka/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,349,444,512", "endColumns": "66,83,94,67,70", "endOffsets": "264,348,443,511,582"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5051,6302,6390,6489,6561", "endColumns": "70,87,98,71,74", "endOffsets": "5117,6385,6484,6556,6631"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,273,383,492,611,731,856,971,1058,1236,1429,1625,1821,2016,2217,2410,2539,2638,2758,2856,2951,3042,3133,3255,3367,3450,3534,3620,3726,3826,3919,4026,4128,4215", "endColumns": "117,99,109,108,118,119,124,114,86,177,192,195,195,194,200,192,128,98,119,97,94,90,90,121,111,82,83,85,105,99,92,106,101,86,99", "endOffsets": "168,268,378,487,606,726,851,966,1053,1231,1424,1620,1816,2011,2212,2405,2534,2633,2753,2851,2946,3037,3128,3250,3362,3445,3529,3615,3721,3821,3914,4021,4123,4210,4310"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14767,16216,16316,16426,16535,16654,16774,16899,17014,17101,17279,17472,17668,17864,18059,18260,18453,18582,18681,18801,18899,18994,19085,19176,19298,19410,19493,19577,19663,19769,19869,19962,20069,20171,20258", "endColumns": "117,99,109,108,118,119,124,114,86,177,192,195,195,194,200,192,128,98,119,97,94,90,90,121,111,82,83,85,105,99,92,106,101,86,99", "endOffsets": "14880,16311,16421,16530,16649,16769,16894,17009,17096,17274,17467,17663,17859,18054,18255,18448,18577,18676,18796,18894,18989,19080,19171,19293,19405,19488,19572,19658,19764,19864,19957,20064,20166,20253,20353"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,399,509,586,681,794,926,1040,1184,1266,1364,1454,1548,1666,1782,1885,2016,2149,2280,2448,2573,2686,2801,2919,3010,3103,3217,3352,3451,3549,3656,3789,3925,4032,4130,4203,4284,4366,4449,4558,4634,4715,4812,4911,5001,5099,5181,5283,5375,5478,5591,5667,5769", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "164,277,394,504,581,676,789,921,1035,1179,1261,1359,1449,1543,1661,1777,1880,2011,2144,2275,2443,2568,2681,2796,2914,3005,3098,3212,3347,3446,3544,3651,3784,3920,4027,4125,4198,4279,4361,4444,4553,4629,4710,4807,4906,4996,5094,5176,5278,5370,5473,5586,5662,5764,5857"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3026,3140,3253,3370,8316,10613,10708,10821,10953,11067,11211,11293,11391,11481,11575,11693,11809,11912,12043,12176,12307,12475,12600,12713,12828,12946,13037,13130,13244,13379,13478,13576,13683,13816,13952,14059,14359,14589,24395,24631,24815,25194,25270,25351,25448,25547,25637,25735,25817,25919,26011,26114,26227,26303,26405", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "3135,3248,3365,3475,8388,10703,10816,10948,11062,11206,11288,11386,11476,11570,11688,11804,11907,12038,12171,12302,12470,12595,12708,12823,12941,13032,13125,13239,13374,13473,13571,13678,13811,13947,14054,14152,14427,14665,24472,24709,24919,25265,25346,25443,25542,25632,25730,25812,25914,26006,26109,26222,26298,26400,26493"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ka/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,321,386,476,560,608,751,837,920,1043,1134,1202,1285,1382,1433,1497,1565,1639,1689,1732,1773,1814,1865,1929,1997,2049,2113,2175,2218,2294,2357,2424,2511,2595,2659,2719,2791,2846", "endColumns": "45,66,64,89,83,47,142,85,82,122,90,67,82,96,50,63,67,73,49,42,40,40,50,63,67,51,63,61,42,75,62,66,86,83,63,59,71,54,67", "endOffsets": "253,320,385,475,559,607,750,836,919,1042,1133,1201,1284,1381,1432,1496,1564,1638,1688,1731,1772,1813,1864,1928,1996,2048,2112,2174,2217,2293,2356,2423,2510,2594,2658,2718,2790,2845,2913"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4627,4677,4748,4817,4911,4999,5122,5269,5359,5446,5573,5668,5740,5827,5928,5983,6051,6123,6201,6255,6636,6681,6726,6781,6849,6921,6977,7045,7111,7158,7238,7305,7376,7467,7555,7623,7687,7763,16144", "endColumns": "49,70,68,93,87,51,146,89,86,126,94,71,86,100,54,67,71,77,53,46,44,44,54,67,71,55,67,65,46,79,66,70,90,87,67,63,75,58,71", "endOffsets": "4672,4743,4812,4906,4994,5046,5264,5354,5441,5568,5663,5735,5822,5923,5978,6046,6118,6196,6250,6297,6676,6721,6776,6844,6916,6972,7040,7106,7153,7233,7300,7371,7462,7550,7618,7682,7758,7817,16211"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1003,1072,1161,1252,1324,1403,1473", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,998,1067,1156,1247,1319,1398,1468,1589"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8134,8230,14157,14256,14432,14954,15034,23818,23908,23995,24066,24215,24304,24559,24924,25003,25073", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "8225,8311,14251,14354,14517,15029,15125,23903,23990,24061,24130,24299,24390,24626,24998,25068,25189"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ka/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9403", "endColumns": "142", "endOffsets": "9541"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ka/values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1075,1172,1241,1304,1391,1455,1521,1581,1650,1711,1765,1880,1939,1999,2053,2125,2255,2343,2427,2565,2643,2719,2858,2952,3032,3088,3142,3208,3281,3359,3445,3529,3602,3680,3758,3833,3943,4033,4108,4202,4300,4374,4451,4551,4604,4688,4756,4845,4934,4996,5061,5124,5194,5301,5401,5501,5597,5657,5715", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "268,346,420,504,597,691,790,915,1003,1070,1167,1236,1299,1386,1450,1516,1576,1645,1706,1760,1875,1934,1994,2048,2120,2250,2338,2422,2560,2638,2714,2853,2947,3027,3083,3137,3203,3276,3354,3440,3524,3597,3675,3753,3828,3938,4028,4103,4197,4295,4369,4446,4546,4599,4683,4751,4840,4929,4991,5056,5119,5189,5296,5396,5496,5592,5652,5710,5790"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3480,3558,3632,3716,3809,7822,7921,8046,14522,14670,14885,15130,15193,15280,15344,15410,15470,15539,15600,15654,15769,15828,15888,15942,16014,20358,20446,20530,20668,20746,20822,20961,21055,21135,21191,21245,21311,21384,21462,21548,21632,21705,21783,21861,21936,22046,22136,22211,22305,22403,22477,22554,22654,22707,22791,22859,22948,23037,23099,23164,23227,23297,23404,23504,23604,23700,23760,24135", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "318,3553,3627,3711,3804,3898,7916,8041,8129,14584,14762,14949,15188,15275,15339,15405,15465,15534,15595,15649,15764,15823,15883,15937,16009,16139,20441,20525,20663,20741,20817,20956,21050,21130,21186,21240,21306,21379,21457,21543,21627,21700,21778,21856,21931,22041,22131,22206,22300,22398,22472,22549,22649,22702,22786,22854,22943,23032,23094,23159,23222,23292,23399,23499,23599,23695,23755,23813,24210"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3903,3999,4101,4200,4299,4405,4509,24714", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3994,4096,4195,4294,4400,4504,4622,24810"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ka/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8393,8498,8647,8775,8885,9039,9173,9295,9546,9719,9827,9982,10110,10271,10410,10476,10537", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "8493,8642,8770,8880,9034,9168,9290,9398,9714,9822,9977,10105,10266,10405,10471,10532,10608"}}]}]}