{"logs": [{"outputFile": "com.webvideocaster.app-mergeDebugResources-61:/values-ky/values-ky.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,24335", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,24412"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/res/values-ky/values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "198,265,350,437,505", "endColumns": "66,84,86,67,61", "endOffsets": "264,349,436,504,566"}, "to": {"startLines": "55,70,71,72,73", "startColumns": "4,4,4,4,4", "startOffsets": "5131,6355,6444,6535,6607", "endColumns": "70,88,90,71,65", "endOffsets": "5197,6439,6530,6602,6668"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,295,409,526,646,758,870,969,1061,1241,1408,1580,1753,1922,2095,2264,2383,2481,2594,2690,2788,2880,2980,3085,3198,3284,3369,3455,3568,3676,3767,3865,3967,4054", "endColumns": "129,109,113,116,119,111,111,98,91,179,166,171,172,168,172,168,118,97,112,95,97,91,99,104,112,85,84,85,112,107,90,97,101,86,95", "endOffsets": "180,290,404,521,641,753,865,964,1056,1236,1403,1575,1748,1917,2090,2259,2378,2476,2589,2685,2783,2875,2975,3080,3193,3279,3364,3450,3563,3671,3762,3860,3962,4049,4145"}, "to": {"startLines": "154,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14801,16273,16383,16497,16614,16734,16846,16958,17057,17149,17329,17496,17668,17841,18010,18183,18352,18471,18569,18682,18778,18876,18968,19068,19173,19286,19372,19457,19543,19656,19764,19855,19953,20055,20142", "endColumns": "129,109,113,116,119,111,111,98,91,179,166,171,172,168,172,168,118,97,112,95,97,91,99,104,112,85,84,85,112,107,90,97,101,86,95", "endOffsets": "14926,16378,16492,16609,16729,16841,16953,17052,17144,17324,17491,17663,17836,18005,18178,18347,18466,18564,18677,18773,18871,18963,19063,19168,19281,19367,19452,19538,19651,19759,19850,19948,20050,20137,20233"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,287,420,541,622,716,828,960,1097,1249,1329,1423,1511,1604,1716,1831,1930,2062,2192,2325,2496,2621,2734,2851,2969,3058,3152,3270,3403,3502,3623,3724,3853,3988,4093,4190,4262,4348,4430,4511,4619,4704,4784,4880,4977,5069,5162,5245,5350,5445,5540,5684,5770,5885", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "169,282,415,536,617,711,823,955,1092,1244,1324,1418,1506,1599,1711,1826,1925,2057,2187,2320,2491,2616,2729,2846,2964,3053,3147,3265,3398,3497,3618,3719,3848,3983,4088,4185,4257,4343,4425,4506,4614,4699,4779,4875,4972,5064,5157,5240,5345,5440,5535,5679,5765,5880,5984"}, "to": {"startLines": "33,34,35,36,97,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149,152,256,259,261,265,266,267,268,269,270,271,272,273,274,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3027,3146,3259,3392,8334,10622,10716,10828,10960,11097,11249,11329,11423,11511,11604,11716,11831,11930,12062,12192,12325,12496,12621,12734,12851,12969,13058,13152,13270,13403,13502,13623,13724,13853,13988,14093,14400,14621,24253,24490,24672,25045,25130,25210,25306,25403,25495,25588,25671,25776,25871,25966,26110,26196,26311", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "3141,3254,3387,3508,8410,10711,10823,10955,11092,11244,11324,11418,11506,11599,11711,11826,11925,12057,12187,12320,12491,12616,12729,12846,12964,13053,13147,13265,13398,13497,13618,13719,13848,13983,14088,14185,14467,14702,24330,24566,24775,25125,25205,25301,25398,25490,25583,25666,25771,25866,25961,26105,26191,26306,26410"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "95,96,147,148,150,156,157,249,250,251,252,254,255,258,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8157,8250,14190,14300,14472,15001,15083,23692,23781,23866,23932,24081,24166,24417,24780,24859,24927", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "8245,8329,14295,14395,14552,15078,15176,23776,23861,23927,23994,24161,24248,24485,24854,24922,25040"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/res/values-ky/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9402", "endColumns": "157", "endOffsets": "9555"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1133,1227,1297,1358,1445,1508,1572,1631,1705,1767,1821,1938,1996,2057,2111,2185,2307,2391,2487,2619,2697,2775,2904,2993,3073,3134,3189,3255,3324,3401,3488,3569,3643,3719,3809,3882,3984,4069,4148,4238,4330,4404,4489,4579,4631,4715,4780,4865,4950,5012,5076,5139,5208,5325,5433,5533,5637,5702,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "260,343,428,513,628,738,839,980,1064,1128,1222,1292,1353,1440,1503,1567,1626,1700,1762,1816,1933,1991,2052,2106,2180,2302,2386,2482,2614,2692,2770,2899,2988,3068,3129,3184,3250,3319,3396,3483,3564,3638,3714,3804,3877,3979,4064,4143,4233,4325,4399,4484,4574,4626,4710,4775,4860,4945,5007,5071,5134,5203,5320,5428,5528,5632,5697,5756,5838"}, "to": {"startLines": "2,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3513,3596,3681,3766,3881,7831,7932,8073,14557,14707,14931,15181,15242,15329,15392,15456,15515,15589,15651,15705,15822,15880,15941,15995,16069,20238,20322,20418,20550,20628,20706,20835,20924,21004,21065,21120,21186,21255,21332,21419,21500,21574,21650,21740,21813,21915,22000,22079,22169,22261,22335,22420,22510,22562,22646,22711,22796,22881,22943,23007,23070,23139,23256,23364,23464,23568,23633,23999", "endLines": "5,37,38,39,40,41,92,93,94,151,153,155,158,159,160,161,162,163,164,165,166,167,168,169,170,171,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,253", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "310,3591,3676,3761,3876,3986,7927,8068,8152,14616,14796,14996,15237,15324,15387,15451,15510,15584,15646,15700,15817,15875,15936,15990,16064,16186,20317,20413,20545,20623,20701,20830,20919,20999,21060,21115,21181,21250,21327,21414,21495,21569,21645,21735,21808,21910,21995,22074,22164,22256,22330,22415,22505,22557,22641,22706,22791,22876,22938,23002,23065,23134,23251,23359,23459,23563,23628,23687,24076"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/res/values-ky/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "208,254,328,390,468,544,593,718,805,887,994,1083,1152,1241,1350,1400,1463,1528,1602,1650,1690,1734,1776,1824,1885,1948,2003,2066,2129,2172,2239,2302,2364,2448,2528,2588,2649,2727,2776", "endColumns": "45,73,61,77,75,48,124,86,81,106,88,68,88,108,49,62,64,73,47,39,43,41,47,60,62,54,62,62,42,66,62,61,83,79,59,60,77,48,77", "endOffsets": "253,327,389,467,543,592,717,804,886,993,1082,1151,1240,1349,1399,1462,1527,1601,1649,1689,1733,1775,1823,1884,1947,2002,2065,2128,2171,2238,2301,2363,2447,2527,2587,2648,2726,2775,2853"}, "to": {"startLines": "49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4722,4772,4850,4916,4998,5078,5202,5331,5422,5508,5619,5712,5785,5878,5991,6045,6112,6181,6259,6311,6673,6721,6767,6819,6884,6951,7010,7077,7144,7191,7262,7329,7395,7483,7567,7631,7696,7778,16191", "endColumns": "49,77,65,81,79,52,128,90,85,110,92,72,92,112,53,66,68,77,51,43,47,45,51,64,66,58,66,66,46,70,66,65,87,83,63,64,81,52,81", "endOffsets": "4767,4845,4911,4993,5073,5126,5326,5417,5503,5614,5707,5780,5873,5986,6040,6107,6176,6254,6306,6350,6716,6762,6814,6879,6946,7005,7072,7139,7186,7257,7324,7390,7478,7562,7626,7691,7773,7826,16268"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/res/values-ky/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1984,2044,2100", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,162,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1983,2043,2099,2173"}, "to": {"startLines": "98,99,100,101,102,103,104,105,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8415,8521,8670,8799,8906,9051,9178,9293,9560,9729,9836,9986,10116,10253,10420,10484,10544", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,166,63,59,77", "endOffsets": "8516,8665,8794,8901,9046,9173,9288,9397,9724,9831,9981,10111,10248,10415,10479,10539,10617"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "42,43,44,45,46,47,48,260", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3991,4091,4193,4296,4403,4507,4611,24571", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "4086,4188,4291,4398,4502,4606,4717,24667"}}]}]}