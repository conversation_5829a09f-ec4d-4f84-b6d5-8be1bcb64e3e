1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.webvideocaster"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:5-67
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:5-76
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:22-73
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:5-79
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:5-80
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:22-77
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:5-77
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:22-74
16    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:5-80
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:22-77
17
18    <queries>
18-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:14:5-16:15
19        <package android:name="com.google.android.gms.policy_cast_dynamite" />
19-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:9-79
19-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:18-76
20    </queries>
21
22    <permission
22-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
23        android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
27
28    <application
28-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:5-36:19
29        android:allowBackup="true"
29-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
31        android:extractNativeLibs="true"
32        android:label="@string/app_name"
32-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:13:9-41
33        android:supportsRtl="true"
33-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:14:9-35
34        android:theme="@style/Theme.WebVideoCaster" >
34-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:15:9-52
35        <meta-data
35-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:17:9-19:75
36            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
36-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:18:13-93
37            android:value="com.webvideocaster.cast.CastOptionsProvider" />
37-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:19:13-72
38
39        <activity
39-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:21:9-24:20
40            android:name="com.webvideocaster.ui.MainActivity"
40-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:22:13-44
41            android:exported="false" >
41-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:23:13-37
42        </activity>
43        <activity
43-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:9-31:20
44            android:name="com.webvideocaster.ui.BrowserActivity"
44-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:19-53
45            android:exported="true" >
45-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:26:13-36
46            <intent-filter>
46-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:27:13-30:29
47                <action android:name="android.intent.action.MAIN" />
47-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:17-69
47-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:17-77
49-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:27-74
50            </intent-filter>
51        </activity>
52        <activity android:name="com.webvideocaster.ui.LocalVideoActivity" />
52-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:9-59
52-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:19-56
53
54        <service
54-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:34:9-35:39
55            android:name="com.webvideocaster.cast.UpnpService"
55-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:34:18-50
56            android:exported="false" />
56-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:13-37
57
58        <receiver
58-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:19:9-21:40
59            android:name="com.google.android.gms.cast.framework.media.MediaIntentReceiver"
59-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:20:13-91
60            android:exported="false" />
60-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:21:13-37
61
62        <service
62-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:23:9-25:40
63            android:name="com.google.android.gms.cast.framework.ReconnectionService"
63-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:24:13-85
64            android:exported="false" />
64-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:25:13-37
65
66        <activity
66-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
67            android:name="com.google.android.gms.common.api.GoogleApiActivity"
67-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
68            android:exported="false"
68-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
69            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
69-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
70
71        <meta-data
71-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
72            android:name="com.google.android.gms.version"
72-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
73            android:value="@integer/google_play_services_version" />
73-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
74
75        <provider
75-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
76            android:name="androidx.startup.InitializationProvider"
76-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
77            android:authorities="com.webvideocaster.androidx-startup"
77-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
78            android:exported="false" >
78-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
79            <meta-data
79-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
80                android:name="androidx.emoji2.text.EmojiCompatInitializer"
80-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
81                android:value="androidx.startup" />
81-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
82            <meta-data
82-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
83                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
83-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
84                android:value="androidx.startup" />
84-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
86                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
86-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
87                android:value="androidx.startup" />
87-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
88        </provider>
89
90        <receiver
90-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
91            android:name="androidx.profileinstaller.ProfileInstallReceiver"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
92            android:directBootAware="false"
92-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
93            android:enabled="true"
93-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
94            android:exported="true"
94-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
95            android:permission="android.permission.DUMP" >
95-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
97                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
97-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
100                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
100-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
103                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
103-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
106                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
106-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
107            </intent-filter>
108        </receiver>
109
110        <service
110-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:29:9-35:19
111            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
111-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:30:13-103
112            android:exported="false" >
112-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:31:13-37
113            <meta-data
113-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:32:13-34:39
114                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
114-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:33:17-94
115                android:value="cct" />
115-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:34:17-36
116        </service>
117        <service
117-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:26:9-30:19
118            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
118-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:27:13-117
119            android:exported="false"
119-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:28:13-37
120            android:permission="android.permission.BIND_JOB_SERVICE" >
120-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:29:13-69
121        </service>
122
123        <receiver
123-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:32:9-34:40
124            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
124-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:33:13-132
125            android:exported="false" />
125-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:34:13-37
126    </application>
127
128</manifest>
