1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.webvideocaster"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:5-67
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:5-76
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:22-73
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:5-79
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:5-80
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:22-77
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:5-75
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:22-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:5-77
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:22-74
17    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
17-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:10:5-80
17-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:10:22-77
18    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
18-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:5-86
18-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:22-83
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:5-68
19-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:22-65
20
21    <queries>
21-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:14:5-16:15
22        <package android:name="com.google.android.gms.policy_cast_dynamite" />
22-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:9-79
22-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:18-76
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
26        android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
30
31    <application
31-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:14:5-39:19
32        android:allowBackup="true"
32-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:15:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
34        android:extractNativeLibs="true"
35        android:label="@string/app_name"
35-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:16:9-41
36        android:supportsRtl="true"
36-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:17:9-35
37        android:theme="@style/Theme.WebVideoCaster" >
37-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:18:9-52
38        <meta-data
38-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:20:9-22:75
39            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
39-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:21:13-93
40            android:value="com.webvideocaster.cast.CastOptionsProvider" />
40-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:22:13-72
41
42        <activity
42-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:24:9-31:20
43            android:name="com.webvideocaster.ui.MainActivity"
43-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:13-44
44            android:exported="true" >
44-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:26:13-36
45            <intent-filter>
45-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:27:13-30:29
46                <action android:name="android.intent.action.MAIN" />
46-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:17-69
46-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:17-77
48-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:27-74
49            </intent-filter>
50        </activity>
51        <activity
51-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:9-34:20
52            android:name="com.webvideocaster.ui.BrowserActivity"
52-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:19-53
53            android:exported="false" >
53-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:33:13-37
54        </activity>
55        <activity android:name="com.webvideocaster.ui.LocalVideoActivity" />
55-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:9-59
55-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:19-56
56
57        <service
57-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:37:9-38:39
58            android:name="com.webvideocaster.cast.UpnpService"
58-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:37:18-50
59            android:exported="false" />
59-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:38:13-37
60
61        <receiver
61-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:19:9-21:40
62            android:name="com.google.android.gms.cast.framework.media.MediaIntentReceiver"
62-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:20:13-91
63            android:exported="false" />
63-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:21:13-37
64
65        <service
65-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:23:9-25:40
66            android:name="com.google.android.gms.cast.framework.ReconnectionService"
66-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:24:13-85
67            android:exported="false" />
67-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:25:13-37
68
69        <activity
69-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
70            android:name="com.google.android.gms.common.api.GoogleApiActivity"
70-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
71            android:exported="false"
71-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
72-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
73
74        <meta-data
74-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
75            android:name="com.google.android.gms.version"
75-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
76            android:value="@integer/google_play_services_version" />
76-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
77
78        <provider
78-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
79            android:name="androidx.startup.InitializationProvider"
79-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
80            android:authorities="com.webvideocaster.androidx-startup"
80-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
81            android:exported="false" >
81-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
83                android:name="androidx.emoji2.text.EmojiCompatInitializer"
83-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
84                android:value="androidx.startup" />
84-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
86                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
86-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
87                android:value="androidx.startup" />
87-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
89                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
90                android:value="androidx.startup" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
91        </provider>
92
93        <receiver
93-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
94            android:name="androidx.profileinstaller.ProfileInstallReceiver"
94-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
95            android:directBootAware="false"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
96            android:enabled="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
97            android:exported="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
98            android:permission="android.permission.DUMP" >
98-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
100                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
100-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
103                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
103-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
106                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
106-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
109                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
109-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
110            </intent-filter>
111        </receiver>
112
113        <service
113-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:29:9-35:19
114            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
114-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:30:13-103
115            android:exported="false" >
115-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:31:13-37
116            <meta-data
116-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:32:13-34:39
117                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
117-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:33:17-94
118                android:value="cct" />
118-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:34:17-36
119        </service>
120        <service
120-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:26:9-30:19
121            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
121-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:27:13-117
122            android:exported="false"
122-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:28:13-37
123            android:permission="android.permission.BIND_JOB_SERVICE" >
123-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:29:13-69
124        </service>
125
126        <receiver
126-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:32:9-34:40
127            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
127-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:33:13-132
128            android:exported="false" />
128-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:34:13-37
129    </application>
130
131</manifest>
