1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.webvideocaster"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:5-67
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:5-76
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:22-73
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:5-79
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:5-80
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:22-77
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:5-75
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:22-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:5-77
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:22-74
17    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
17-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:10:5-80
17-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:10:22-77
18    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
18-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:5-86
18-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:22-83
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:5-68
19-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:22-65
20
21    <queries>
21-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:14:5-16:15
22        <package android:name="com.google.android.gms.policy_cast_dynamite" />
22-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:9-79
22-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:18-76
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
26        android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
30
31    <application
31-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:14:5-39:19
32        android:allowBackup="true"
32-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:15:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
34        android:debuggable="true"
35        android:extractNativeLibs="true"
36        android:label="@string/app_name"
36-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:16:9-41
37        android:supportsRtl="true"
37-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:17:9-35
38        android:theme="@style/Theme.WebVideoCaster" >
38-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:18:9-52
39        <meta-data
39-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:20:9-22:75
40            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
40-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:21:13-93
41            android:value="com.webvideocaster.cast.CastOptionsProvider" />
41-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:22:13-72
42
43        <activity
43-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:24:9-31:20
44            android:name="com.webvideocaster.ui.MainActivity"
44-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:13-44
45            android:exported="true" >
45-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:26:13-36
46            <intent-filter>
46-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:27:13-30:29
47                <action android:name="android.intent.action.MAIN" />
47-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:17-69
47-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:17-77
49-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:27-74
50            </intent-filter>
51        </activity>
52        <activity
52-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:9-34:20
53            android:name="com.webvideocaster.ui.BrowserActivity"
53-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:19-53
54            android:exported="false" >
54-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:33:13-37
55        </activity>
56        <activity android:name="com.webvideocaster.ui.LocalVideoActivity" />
56-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:9-59
56-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:19-56
57
58        <service
58-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:37:9-38:39
59            android:name="com.webvideocaster.cast.UpnpService"
59-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:37:18-50
60            android:exported="false" />
60-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:38:13-37
61
62        <activity
62-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/AndroidManifest.xml:23:9-25:39
63            android:name="androidx.compose.ui.tooling.PreviewActivity"
63-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/AndroidManifest.xml:24:13-71
64            android:exported="true" />
64-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/AndroidManifest.xml:25:13-36
65
66        <receiver
66-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:19:9-21:40
67            android:name="com.google.android.gms.cast.framework.media.MediaIntentReceiver"
67-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:20:13-91
68            android:exported="false" />
68-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:21:13-37
69
70        <service
70-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:23:9-25:40
71            android:name="com.google.android.gms.cast.framework.ReconnectionService"
71-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:24:13-85
72            android:exported="false" />
72-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:25:13-37
73
74        <activity
74-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
75            android:name="com.google.android.gms.common.api.GoogleApiActivity"
75-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
76            android:exported="false"
76-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
77            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
77-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
78
79        <meta-data
79-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
80            android:name="com.google.android.gms.version"
80-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
81            android:value="@integer/google_play_services_version" />
81-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
82
83        <provider
83-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
84            android:name="androidx.startup.InitializationProvider"
84-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
85            android:authorities="com.webvideocaster.androidx-startup"
85-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
86            android:exported="false" >
86-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
87            <meta-data
87-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
88                android:name="androidx.emoji2.text.EmojiCompatInitializer"
88-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
89                android:value="androidx.startup" />
89-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
91                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
91-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
92                android:value="androidx.startup" />
92-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
93            <meta-data
93-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
94                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
94-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
95                android:value="androidx.startup" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
96        </provider>
97
98        <receiver
98-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
99            android:name="androidx.profileinstaller.ProfileInstallReceiver"
99-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
100            android:directBootAware="false"
100-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
101            android:enabled="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
102            android:exported="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
103            android:permission="android.permission.DUMP" >
103-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
105                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
108                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
108-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
111                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
111-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
114                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
114-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
114-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
115            </intent-filter>
116        </receiver>
117
118        <service
118-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:29:9-35:19
119            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
119-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:30:13-103
120            android:exported="false" >
120-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:31:13-37
121            <meta-data
121-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:32:13-34:39
122                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
122-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:33:17-94
123                android:value="cct" />
123-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:34:17-36
124        </service>
125        <service
125-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:26:9-30:19
126            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
126-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:27:13-117
127            android:exported="false"
127-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:28:13-37
128            android:permission="android.permission.BIND_JOB_SERVICE" >
128-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:29:13-69
129        </service>
130
131        <receiver
131-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:32:9-34:40
132            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
132-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:33:13-132
133            android:exported="false" />
133-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:34:13-37
134    </application>
135
136</manifest>
