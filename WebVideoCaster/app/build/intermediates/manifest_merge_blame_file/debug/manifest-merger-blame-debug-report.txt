1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.webvideocaster"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:5-67
11-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:5-76
12-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:5:22-73
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:5-79
13-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:5-80
14-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7:22-77
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:5-77
15-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:8:22-74
16    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:5-80
16-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:9:22-77
17
18    <queries>
18-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:14:5-16:15
19        <package android:name="com.google.android.gms.policy_cast_dynamite" />
19-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:9-79
19-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:15:18-76
20    </queries>
21
22    <permission
22-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
23        android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.webvideocaster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
27
28    <application
28-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11:5-36:19
29        android:allowBackup="true"
29-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:12:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
31        android:debuggable="true"
32        android:extractNativeLibs="true"
33        android:label="@string/app_name"
33-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:13:9-41
34        android:supportsRtl="true"
34-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:14:9-35
35        android:theme="@style/Theme.WebVideoCaster" >
35-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:15:9-52
36        <meta-data
36-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:17:9-19:75
37            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
37-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:18:13-93
38            android:value="com.webvideocaster.cast.CastOptionsProvider" />
38-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:19:13-72
39
40        <activity
40-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:21:9-24:20
41            android:name="com.webvideocaster.ui.MainActivity"
41-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:22:13-44
42            android:exported="false" >
42-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:23:13-37
43        </activity>
44        <activity
44-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:9-31:20
45            android:name="com.webvideocaster.ui.BrowserActivity"
45-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:25:19-53
46            android:exported="true" >
46-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:26:13-36
47            <intent-filter>
47-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:27:13-30:29
48                <action android:name="android.intent.action.MAIN" />
48-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:17-69
48-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:28:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:17-77
50-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:29:27-74
51            </intent-filter>
52        </activity>
53        <activity android:name="com.webvideocaster.ui.LocalVideoActivity" />
53-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:9-59
53-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:32:19-56
54
55        <service
55-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:34:9-35:39
56            android:name="com.webvideocaster.cast.UpnpService"
56-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:34:18-50
57            android:exported="false" />
57-->/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:35:13-37
58
59        <activity
59-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/AndroidManifest.xml:23:9-25:39
60            android:name="androidx.compose.ui.tooling.PreviewActivity"
60-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/AndroidManifest.xml:24:13-71
61            android:exported="true" />
61-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/AndroidManifest.xml:25:13-36
62
63        <receiver
63-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:19:9-21:40
64            android:name="com.google.android.gms.cast.framework.media.MediaIntentReceiver"
64-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:20:13-91
65            android:exported="false" />
65-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:21:13-37
66
67        <service
67-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:23:9-25:40
68            android:name="com.google.android.gms.cast.framework.ReconnectionService"
68-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:24:13-85
69            android:exported="false" />
69-->[com.google.android.gms:play-services-cast-framework:21.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/AndroidManifest.xml:25:13-37
70
71        <activity
71-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
72            android:name="com.google.android.gms.common.api.GoogleApiActivity"
72-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
73            android:exported="false"
73-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
74            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
74-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
75
76        <meta-data
76-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
77            android:name="com.google.android.gms.version"
77-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
78            android:value="@integer/google_play_services_version" />
78-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
79
80        <provider
80-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
81            android:name="androidx.startup.InitializationProvider"
81-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
82            android:authorities="com.webvideocaster.androidx-startup"
82-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
83            android:exported="false" >
83-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
84            <meta-data
84-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
85                android:name="androidx.emoji2.text.EmojiCompatInitializer"
85-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
86                android:value="androidx.startup" />
86-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
88                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
88-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
89                android:value="androidx.startup" />
89-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
91                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
92                android:value="androidx.startup" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
93        </provider>
94
95        <receiver
95-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
96            android:name="androidx.profileinstaller.ProfileInstallReceiver"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
97            android:directBootAware="false"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
98            android:enabled="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
99            android:exported="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
100            android:permission="android.permission.DUMP" >
100-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
102                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
102-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
105                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
105-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
108                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
108-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
111                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
111-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
112            </intent-filter>
113        </receiver>
114
115        <service
115-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:29:9-35:19
116            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
116-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:30:13-103
117            android:exported="false" >
117-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:31:13-37
118            <meta-data
118-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:32:13-34:39
119                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
119-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:33:17-94
120                android:value="cct" />
120-->[com.google.android.datatransport:transport-backend-cct:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/AndroidManifest.xml:34:17-36
121        </service>
122        <service
122-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:26:9-30:19
123            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
123-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:27:13-117
124            android:exported="false"
124-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:28:13-37
125            android:permission="android.permission.BIND_JOB_SERVICE" >
125-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:29:13-69
126        </service>
127
128        <receiver
128-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:32:9-34:40
129            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
129-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:33:13-132
130            android:exported="false" />
130-->[com.google.android.datatransport:transport-runtime:3.1.3] /Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/AndroidManifest.xml:34:13-37
131    </application>
132
133</manifest>
