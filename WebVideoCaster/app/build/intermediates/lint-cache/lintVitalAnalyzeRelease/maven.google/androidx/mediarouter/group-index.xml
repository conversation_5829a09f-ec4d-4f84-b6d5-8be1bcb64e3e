<?xml version='1.0' encoding='UTF-8'?>
<androidx.mediarouter>
  <mediarouter versions="1.0.0-alpha1,1.0.0-alpha3,1.0.0-alpha4,1.0.0-alpha5,1.0.0-beta01,1.0.0,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.2.0-alpha01,1.2.0-alpha02,1.2.0-beta01,1.2.0-rc01,1.2.0-rc02,1.2.0,1.2.1,1.2.2,1.2.3,1.2.4,1.2.5,1.2.6,1.3.0-alpha01,1.3.0-beta01,1.3.0-rc01,1.3.0,1.3.1,1.4.0-alpha01,1.4.0-beta01,1.4.0-beta02,1.4.0-rc01,1.4.0,1.6.0-alpha01,1.6.0-alpha02,1.6.0-alpha03,1.6.0-alpha04,1.6.0-alpha05,1.6.0-beta01,1.6.0-rc01,1.6.0,1.7.0-alpha01,1.7.0-alpha02,1.7.0-beta01,1.7.0-rc01,1.7.0,1.8.0-alpha01,1.8.0-alpha02,1.8.0-alpha03,1.8.0-alpha04,1.8.0-beta01,1.8.0-rc01,1.8.0,1.8.1"/>
  <mediarouter-testing versions="1.4.0-rc01,1.4.0,1.6.0-alpha02,1.6.0-alpha03,1.6.0-alpha04,1.6.0-alpha05,1.6.0-beta01,1.6.0-rc01,1.6.0,1.7.0-alpha01,1.7.0-alpha02,1.7.0-beta01,1.7.0-rc01,1.7.0,1.8.0-alpha01,1.8.0-alpha02,1.8.0-alpha03,1.8.0-alpha04,1.8.0-beta01,1.8.0-rc01,1.8.0,1.8.1"/>
</androidx.mediarouter>
