<libraries>
  <library
      name="androidx.compose.material3:material3:1.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2/jars/classes.jar"
      resolved="androidx.compose.material3:material3:1.1.2"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/a9485240ad3094923adc59f8e124fb8c/transformed/jetified-material3-1.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.11.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0/jars/classes.jar"
      resolved="com.google.android.material:material:1.11.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/bc51b2a0f09a6273d016eeda0ead4c1b/transformed/material-1.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/62abe575c8f057bb95141f0909ff91a3/transformed/constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/39a7638de6424e0475ab2d111ccaff1d/transformed/appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cast-framework:21.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-cast-framework:21.4.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/aafde464ec31a125363796efddc3f83b/transformed/jetified-play-services-cast-framework-21.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/e8201db57eb291eace6fd6b21d50e77d/transformed/jetified-viewpager2-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/e8201db57eb291eace6fd6b21d50e77d/transformed/jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cast:21.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-cast:21.4.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/e43225359ed5e5b5696868642e4e3cfc/transformed/jetified-play-services-cast-21.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-flags:18.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/33b6447230393c3ff0aa742f0d5b466d/transformed/jetified-play-services-flags-18.0.1/jars/classes.jar"
      resolved="com.google.android.gms:play-services-flags:18.0.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/33b6447230393c3ff0aa742f0d5b466d/transformed/jetified-play-services-flags-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1/jars/classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/f3c21bb6fd838878aaf4c92b1aaec7c6/transformed/jetified-play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1/jars/classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/dcb76a73710230c2d8067a5dae119ed5/transformed/jetified-play-services-tasks-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/34ef5b478d82dc1354c92c80c1d62a90/transformed/jetified-play-services-basement-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/ee222b22060dc5f571f40e86de14708c/transformed/fragment-1.3.6/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/ee222b22060dc5f571f40e86de14708c/transformed/fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/a390b088c20f6f3307060a2ecee33876/transformed/jetified-activity-ktx-1.8.2/jars/classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/a390b088c20f6f3307060a2ecee33876/transformed/jetified-activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/8a49a4e061d95551802fa45cfd8e969f/transformed/jetified-activity-1.8.2/jars/classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/8a49a4e061d95551802fa45cfd8e969f/transformed/jetified-activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/47eb938e0da75f94241bf45d90144e98/transformed/jetified-activity-compose-1.8.2/jars/classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/47eb938e0da75f94241bf45d90144e98/transformed/jetified-activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/1ce987e011530297eaf1d0148a62b77e/transformed/jetified-ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/aa4244aebcdf839b597c598a4ca3cd99/transformed/jetified-material-ripple-1.4.1/jars/classes.jar"
      resolved="androidx.compose.material:material-ripple:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/aa4244aebcdf839b597c598a4ca3cd99/transformed/jetified-material-ripple-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/331372e0f38533ce2cfabf9e1d9e1d84/transformed/jetified-material-icons-core-1.4.1/jars/classes.jar"
      resolved="androidx.compose.material:material-icons-core:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/331372e0f38533ce2cfabf9e1d9e1d84/transformed/jetified-material-icons-core-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/75d97b3dc8f545dbbc97571e1ca8e2f9/transformed/jetified-ui-unit-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/75d97b3dc8f545dbbc97571e1ca8e2f9/transformed/jetified-ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/afdb439afded8443d9ae353dfb774cab/transformed/jetified-foundation-layout-release/jars/classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/afdb439afded8443d9ae353dfb774cab/transformed/jetified-foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/64608746c3ff9624fa99ecb6cb1429ed/transformed/jetified-foundation-release/jars/classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/64608746c3ff9624fa99ecb6cb1429ed/transformed/jetified-foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/dc325bc5472c38e8522b454fce0cf047/transformed/jetified-animation-core-release/jars/classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/dc325bc5472c38e8522b454fce0cf047/transformed/jetified-animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/73124378ade76c11d2afb77656920763/transformed/jetified-animation-release/jars/classes.jar"
      resolved="androidx.compose.animation:animation-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/73124378ade76c11d2afb77656920763/transformed/jetified-animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/d533644e31a146bc7e6c213c86da85ec/transformed/jetified-ui-geometry-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/d533644e31a146bc7e6c213c86da85ec/transformed/jetified-ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/5b874d36cc701e5225188d0c3350d2a1/transformed/jetified-ui-tooling-data-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/5b874d36cc701e5225188d0c3350d2a1/transformed/jetified-ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/ff2ac332600eeb8104dea25c838af606/transformed/jetified-ui-text-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/ff2ac332600eeb8104dea25c838af606/transformed/jetified-ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/f95af31094110e4b212b44f472b6902f/transformed/jetified-ui-graphics-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/f95af31094110e4b212b44f472b6902f/transformed/jetified-ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/3dfaed9901eb1f2d4a71c401555caaf7/transformed/jetified-ui-tooling-preview-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/3dfaed9901eb1f2d4a71c401555caaf7/transformed/jetified-ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/a6f4f3bec854fa30b29ad960ecb0728c/transformed/jetified-ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/da7a8ba5b6cf7beb26c2ce521ee4b417/transformed/jetified-appcompat-resources-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/da7a8ba5b6cf7beb26c2ce521ee4b417/transformed/jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/b81511212cfc9f48175ec95256712d0c/transformed/drawerlayout-1.1.1/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/b81511212cfc9f48175ec95256712d0c/transformed/drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/672faa678a9f1e8cd9802c4af4d6cbf2/transformed/coordinatorlayout-1.1.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/672faa678a9f1e8cd9802c4af4d6cbf2/transformed/coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/944a51849acf6b8a33e4c46439086f48/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/944a51849acf6b8a33e4c46439086f48/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/636156b63378b8385d6295ddbcd917d0/transformed/recyclerview-1.1.0/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/636156b63378b8385d6295ddbcd917d0/transformed/recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/ebfcf924deaba69ee263b8cb9247b341/transformed/transition-1.2.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/ebfcf924deaba69ee263b8cb9247b341/transformed/transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/96e80ff796fca669540e59e2b777bfeb/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/96e80ff796fca669540e59e2b777bfeb/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/4a3c24bd55ae2fab52c04b7fd3bf197b/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/4a3c24bd55ae2fab52c04b7fd3bf197b/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.mediarouter:mediarouter:1.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0/jars/classes.jar"
      resolved="androidx.mediarouter:mediarouter:1.6.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/183ceaded57a2ef1647610dbed3bf727/transformed/mediarouter-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/696d7ec42970f7c47e825a10ccca99ca/transformed/media-1.6.0/jars/classes.jar"
      resolved="androidx.media:media:1.6.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/696d7ec42970f7c47e825a10ccca99ca/transformed/media-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/c4271e0011cd633a4cfed4aefcbe8da9/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/c4271e0011cd633a4cfed4aefcbe8da9/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/b49c4ce08ce2b8d95f30ec8ab1d1b1bb/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/b49c4ce08ce2b8d95f30ec8ab1d1b1bb/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/6e373910180ecc499c4cba2cc7958c36/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/6e373910180ecc499c4cba2cc7958c36/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/379e10c6a2ab255f09c739262032f7f3/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/379e10c6a2ab255f09c739262032f7f3/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.12.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0/jars/classes.jar"
      resolved="androidx.core:core:1.12.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/14079aeea39e6e39594aaa0c2f9c5dd5/transformed/core-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/f55d00b0d0d0c1786d8af86fdf7ad602/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/f55d00b0d0d0c1786d8af86fdf7ad602/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/d79e2440f0340e360148a94b90906480/transformed/lifecycle-livedata-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/d79e2440f0340e360148a94b90906480/transformed/lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/4b95a194827e7f73a23c8574950f6b3e/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/4b95a194827e7f73a23c8574950f6b3e/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.7.0/85334205d65cca70ed0109c3acbd29e22a2d9cb1/lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/23138fc38fa20c8c93e58f9de952ab88/transformed/lifecycle-livedata-core-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/23138fc38fa20c8c93e58f9de952ab88/transformed/lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/b1c55e20baaf2b63e5935b6f6e461b1c/transformed/lifecycle-viewmodel-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/b1c55e20baaf2b63e5935b6f6e461b1c/transformed/lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/275eb027f39dfb89e7ad38cb505cb864/transformed/lifecycle-runtime-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/275eb027f39dfb89e7ad38cb505cb864/transformed/lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/099eff3b50696528943bd87848ddf446/transformed/jetified-lifecycle-runtime-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/099eff3b50696528943bd87848ddf446/transformed/jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/b82337f599ebf0a0b3361e83396cc6e1/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/b82337f599ebf0a0b3361e83396cc6e1/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.12.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/c4d635458670a9b028ffcf3be9bff09d/transformed/jetified-core-ktx-1.12.0/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.12.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/c4d635458670a9b028ffcf3be9bff09d/transformed/jetified-core-ktx-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/b0bc7b3992b71085dfaa8e12e93ea0c6/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/b0bc7b3992b71085dfaa8e12e93ea0c6/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/e4deb428621c5224ae59132906665ef7/transformed/jetified-savedstate-ktx-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/e4deb428621c5224ae59132906665ef7/transformed/jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/c3d51f584632b9a7666f8b6ed78a6331/transformed/jetified-savedstate-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/c3d51f584632b9a7666f8b6ed78a6331/transformed/jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/33e6c0dc262202b399de8d22d4841987/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/33e6c0dc262202b399de8d22d4841987/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/0dc324cbeb6deb4bdfd75773ff50f9c7/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.1.0/50b7fb98350d5f42a4e49704b03278542293ba48/concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.3"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/820f2b351678df566258b394c2444b33/transformed/jetified-transport-backend-cct-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.3@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.3"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/a3fe6d832125d8ca16164ae0c9c7e216/transformed/jetified-transport-runtime-3.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/9e704535f3897deba48f602c32a5b71b/transformed/jetified-transport-api-3.0.0/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/9e704535f3897deba48f602c32a5b71b/transformed/jetified-transport-api-3.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/d180a73b7464ea9b85cb84019998d00f/transformed/core-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/d180a73b7464ea9b85cb84019998d00f/transformed/core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/c65d2c4606ef016bc8e3e56ad27eb943/transformed/jetified-runtime-saveable-release/jars/classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/c65d2c4606ef016bc8e3e56ad27eb943/transformed/jetified-runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/1671d930e985436090841b427d11d32f/transformed/jetified-runtime-release/jars/classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/1671d930e985436090841b427d11d32f/transformed/jetified-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/5ca5fc8016562829660423478fa6f454/transformed/jetified-firebase-encoders-json-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/5ca5fc8016562829660423478fa6f454/transformed/jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-proto/16.0.0/a42d5fd83b96ae7b73a8617d29c94703e18c9992/firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders/17.0.0/26f52dc549c42575b155f8c720e84059ee600a85/firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/e58b6f65eff4ddf626c2737ff41a39bd/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/e58b6f65eff4ddf626c2737ff41a39bd/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/e65010cfb503f4435d66ab95983ad30c/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/e65010cfb503f4435d66ab95983ad30c/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/68146a0a5c17126926b9da7c81135d7f/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/68146a0a5c17126926b9da7c81135d7f/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/4c31d476d55a656da9a23569fdd589c1/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/4c31d476d55a656da9a23569fdd589c1/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.6.0/a7257339a052df0f91433cf9651231bbb802b502/annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/9ae346bbf2a17bbcdc3895522114d875/transformed/jetified-annotation-experimental-1.3.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/9ae346bbf2a17bbcdc3895522114d875/transformed/jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.1/63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4/kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.7.1/c2d86b569f10b7fc7e28d3f50c0eed97897d77a7/kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.22/b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1/kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.8.22/4dabb8248310d833bb6a8b516024a91fd3d275c/kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.20/e58b4816ac517e9cc5df1db051120c63d4cde669/kotlin-stdlib-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.20"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.resourceinspection/resourceinspection-annotation/1.0.1/8c21f8ff5d96d5d52c948707f7e4d6ca6773feef/resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.1/30988fe2d77f3fe3bf7551bb8a8b795fad7e7226/constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="org.jupnp:org.jupnp:2.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jupnp/org.jupnp/2.7.1/8682a7f6021f6ce63b0a6f303ba46372d309bf38/org.jupnp-2.7.1.jar"
      resolved="org.jupnp:org.jupnp:2.7.1"/>
  <library
      name="org.jupnp:org.jupnp.support:2.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jupnp/org.jupnp.support/2.7.1/edd17e54b9eb3d49fccc8b191eea241db9c673ed/org.jupnp.support-2.7.1.jar"
      resolved="org.jupnp:org.jupnp.support:2.7.1"/>
  <library
      name="org.nanohttpd:nanohttpd:2.3.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.nanohttpd/nanohttpd/2.3.1/a8d54d1ca554a77f377eff6bf9e16ca8383c8f6c/nanohttpd-2.3.1.jar"
      resolved="org.nanohttpd:nanohttpd:2.3.1"/>
  <library
      name="androidx.compose.material:material:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/876152d5eaa536439b38d32e0c8990fc/transformed/jetified-material-1.4.1/jars/classes.jar"
      resolved="androidx.compose.material:material:1.4.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/876152d5eaa536439b38d32e0c8990fc/transformed/jetified-material-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.5.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/293bb6b66f46252c40050a333d68edea/transformed/jetified-ui-util-release/jars/classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.5.4"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/293bb6b66f46252c40050a333d68edea/transformed/jetified-ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.palette:palette:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/c6c8a149bec494813ab0a6933800fe3b/transformed/palette-1.0.0/jars/classes.jar"
      resolved="androidx.palette:palette:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/c6c8a149bec494813ab0a6933800fe3b/transformed/palette-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/e20b22f26a336ba1aade959c4c7258be/transformed/jetified-autofill-1.0.0/jars/classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/e20b22f26a336ba1aade959c4c7258be/transformed/jetified-autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/5d2ab0f604ac0e2d0cdecc2f6e5534e0/transformed/jetified-emoji2-views-helper-1.4.0/jars/classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.4.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/5d2ab0f604ac0e2d0cdecc2f6e5534e0/transformed/jetified-emoji2-views-helper-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/jars/classes.jar:/Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0/jars/libs/repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/3a68890a9990e34f5efdbad7008a4e24/transformed/jetified-emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/2ec050ae5721703a7581349791e293bf/transformed/jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-java8/2.7.0/2ad14aed781c4a73ed4dbb421966d408a0a06686/lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/a3f1d2edbe65934dfc46afc0cea315bd/transformed/jetified-customview-poolingcontainer-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/a3f1d2edbe65934dfc46afc0cea315bd/transformed/jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0/jars/classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/496960ee04507f142721f5b0245613bf/transformed/jetified-profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/ee86c7481efcf66b90895d4d41d8ad9c/transformed/jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.9/transforms/a0b64b9874b1daa4c457675b104702b8/transformed/jetified-tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.9/transforms/a0b64b9874b1daa4c457675b104702b8/transformed/jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.15.0/38c8485a652f808c8c149150da4e5c2b0bd17f9a/error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
</libraries>
