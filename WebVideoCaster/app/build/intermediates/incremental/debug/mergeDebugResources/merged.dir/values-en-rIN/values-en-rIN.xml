<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <string msgid="5976598919945601918" name="abc_action_bar_home_description">"Navigate home"</string>
    <string msgid="8388173803310557296" name="abc_action_bar_up_description">"Navigate up"</string>
    <string msgid="3937310113216875497" name="abc_action_menu_overflow_description">"More options"</string>
    <string msgid="4692188335987374352" name="abc_action_mode_done">"Done"</string>
    <string msgid="1189761859438369441" name="abc_activity_chooser_view_see_all">"See all"</string>
    <string msgid="2165779757652331008" name="abc_activitychooserview_choose_application">"Choose an app"</string>
    <string msgid="4215997306490295099" name="abc_capital_off">"OFF"</string>
    <string msgid="884982626291842264" name="abc_capital_on">"ON"</string>
    <string msgid="8833365367933412986" name="abc_menu_alt_shortcut_label">"Alt+"</string>
    <string msgid="2223301931652355242" name="abc_menu_ctrl_shortcut_label">"Ctrl+"</string>
    <string msgid="838001238306846836" name="abc_menu_delete_shortcut_label">"delete"</string>
    <string msgid="7986526966204849475" name="abc_menu_enter_shortcut_label">"enter"</string>
    <string msgid="375214403600139847" name="abc_menu_function_shortcut_label">"Function+"</string>
    <string msgid="4192209724446364286" name="abc_menu_meta_shortcut_label">"Meta+"</string>
    <string msgid="4741552369836443843" name="abc_menu_shift_shortcut_label">"Shift+"</string>
    <string msgid="5473865519181928982" name="abc_menu_space_shortcut_label">"space"</string>
    <string msgid="6180552449598693998" name="abc_menu_sym_shortcut_label">"Sym+"</string>
    <string msgid="5520303668377388990" name="abc_prepend_shortcut_label">"Menu+"</string>
    <string msgid="7208076849092622260" name="abc_search_hint">"Search…"</string>
    <string msgid="3741173234950517107" name="abc_searchview_description_clear">"Clear query"</string>
    <string msgid="693312494995508443" name="abc_searchview_description_query">"Search query"</string>
    <string msgid="3417662926640357176" name="abc_searchview_description_search">"Search"</string>
    <string msgid="1486535517437947103" name="abc_searchview_description_submit">"Submit query"</string>
    <string msgid="2293578557972875415" name="abc_searchview_description_voice">"Voice search"</string>
    <string msgid="8875138169939072951" name="abc_shareactionprovider_share_with">"Share with"</string>
    <string msgid="9055268688411532828" name="abc_shareactionprovider_share_with_application">"Share with <ns1:g id="APPLICATION_NAME">%s</ns1:g>"</string>
    <string msgid="1656852541809559762" name="abc_toolbar_collapse_description">"Collapse"</string>
    <string msgid="6128938260108474660" name="bottom_sheet_collapse_description">"Collapse bottom sheet"</string>
    <string msgid="1918297411568599192" name="bottom_sheet_dismiss_description">"Dismiss bottom sheet"</string>
    <string msgid="7772321844937772780" name="bottom_sheet_drag_handle_description">"Drag handle"</string>
    <string msgid="4324434199045499117" name="bottom_sheet_expand_description">"Expand bottom sheet"</string>
    <string msgid="881409763997275156" name="call_notification_answer_action">"Answer"</string>
    <string msgid="8793775615905189152" name="call_notification_answer_video_action">"Video"</string>
    <string msgid="3229508546291798546" name="call_notification_decline_action">"Decline"</string>
    <string msgid="2659457946726154263" name="call_notification_hang_up_action">"Hang up"</string>
    <string msgid="6107532579223922871" name="call_notification_incoming_text">"Incoming call"</string>
    <string msgid="8623827134497363134" name="call_notification_ongoing_text">"On-going call"</string>
    <string msgid="59049573811482460" name="call_notification_screening_text">"Screening an incoming call"</string>
    <string msgid="406453423630273620" name="close_drawer">"Close navigation menu"</string>
    <string msgid="7573152094250666567" name="close_sheet">"Close sheet"</string>
    <string msgid="5389587048670450460" name="collapsed">"Collapsed"</string>
    <string msgid="3499643850558715142" name="date_input_headline">"Entered date"</string>
    <string msgid="8562356184193964298" name="date_input_headline_description">"Entered date: %1$s"</string>
    <string msgid="5281836720766682161" name="date_input_invalid_for_pattern">"Date does not match expected pattern: %1$s"</string>
    <string msgid="6114792992433444995" name="date_input_invalid_not_allowed">"Date not allowed: %1$s"</string>
    <string msgid="8434112129235255568" name="date_input_invalid_year_range">"Date out of expected year range %1$s – %2$s"</string>
    <string msgid="5194825853981987218" name="date_input_label">"Date"</string>
    <string msgid="5722931102250207748" name="date_input_no_input_description">"None"</string>
    <string msgid="3010396677286327048" name="date_input_title">"Select date"</string>
    <string msgid="2846784065735639969" name="date_picker_headline">"Selected date"</string>
    <string msgid="4627306862713137085" name="date_picker_headline_description">"Current selection: %1$s"</string>
    <string msgid="5152441868029453612" name="date_picker_navigate_to_year_description">"Navigate to year %1$s"</string>
    <string msgid="5724377114289981899" name="date_picker_no_selection_description">"None"</string>
    <string msgid="8911933542023210271" name="date_picker_scroll_to_earlier_years">"Scroll to show earlier years"</string>
    <string msgid="3226341140390493774" name="date_picker_scroll_to_later_years">"Scroll to show later years"</string>
    <string msgid="9029369254443419167" name="date_picker_switch_to_calendar_mode">"Switch to calendar input mode"</string>
    <string msgid="145089358343568971" name="date_picker_switch_to_day_selection">"Swipe to select a year, or tap to switch back to selecting a day"</string>
    <string msgid="1496750567914156598" name="date_picker_switch_to_input_mode">"Switch to text input mode"</string>
    <string msgid="8313783187901412102" name="date_picker_switch_to_next_month">"Change to next month"</string>
    <string msgid="7596294429748914881" name="date_picker_switch_to_previous_month">"Change to previous month"</string>
    <string msgid="3412370019845183965" name="date_picker_switch_to_year_selection">"Switch to selecting a year"</string>
    <string msgid="9208721003668059792" name="date_picker_title">"Select date"</string>
    <string msgid="4775802721403526937" name="date_picker_today_description">"Today"</string>
    <string msgid="8140324713311804736" name="date_picker_year_picker_pane_title">"Year picker visible"</string>
    <string msgid="1891592555781755601" name="date_range_input_invalid_range_input">"Invalid date range input"</string>
    <string msgid="2366412111888449406" name="date_range_input_title">"Enter dates"</string>
    <string msgid="9048690781645835833" name="date_range_picker_day_in_range">"In range"</string>
    <string msgid="4766270708882012148" name="date_range_picker_end_headline">"End date"</string>
    <string msgid="51495506931835470" name="date_range_picker_scroll_to_next_month">"Scroll to show the next month"</string>
    <string msgid="4371570854614540700" name="date_range_picker_scroll_to_previous_month">"Scroll to show the previous month"</string>
    <string msgid="5759491386723090559" name="date_range_picker_start_headline">"Start date"</string>
    <string msgid="690080476639943577" name="date_range_picker_title">"Select dates"</string>
    <string msgid="8038256446254964252" name="default_error_message">"Invalid input"</string>
    <string msgid="6312721426453364202" name="default_popup_window_title">"Pop-up window"</string>
    <string msgid="4057925834421392736" name="dialog">"Dialogue"</string>
    <string msgid="1890207353314751437" name="dropdown_menu">"Drop-down menu"</string>
    <string msgid="5974471714631304645" name="expanded">"Expanded"</string>
    <string msgid="9119039724000326934" name="ic_media_route_learn_more_accessibility">"Learn how to cast"</string>
    <string msgid="6827826412747255547" name="in_progress">"In progress"</string>
    <string msgid="7933458017204019916" name="indeterminate">"Partially ticked"</string>
    <string msgid="2939063992730535343" name="mr_button_content_description">"Cast"</string>
    <string msgid="6073720094880410356" name="mr_cast_button_connected">"Cast. Connected"</string>
    <string msgid="6629927151350192407" name="mr_cast_button_connecting">"Cast. Connecting"</string>
    <string msgid="8071109333469380363" name="mr_cast_button_disconnected">"Cast. Disconnected"</string>
    <string msgid="2175930138959078155" name="mr_cast_dialog_title_view_placeholder">"No info available"</string>
    <string msgid="4257319068277776035" name="mr_chooser_looking_for_devices">"Looking for devices..."</string>
    <string msgid="6114250663023140921" name="mr_chooser_searching">"Finding devices"</string>
    <string msgid="1419936397646839840" name="mr_chooser_title">"Cast to"</string>
    <string msgid="3799500840179081429" name="mr_chooser_wifi_learn_more"><a href="https://support.google.com/chromecast/?p=trouble-finding-devices">"Learn more"</a></string>
    <string msgid="2998902945608081567" name="mr_chooser_wifi_warning_description_car">"Make sure that the other device is on the same Wi-Fi network as this car"</string>
    <string msgid="2555886884770958244" name="mr_chooser_wifi_warning_description_phone">"Make sure that the other device is on the same Wi-Fi network as this phone"</string>
    <string msgid="6038748488793588164" name="mr_chooser_wifi_warning_description_tablet">"Make sure that the other device is on the same Wi-Fi network as this tablet"</string>
    <string msgid="5845921667085074878" name="mr_chooser_wifi_warning_description_tv">"Make sure that the other device is on the same Wi-Fi network as this TV"</string>
    <string msgid="3459891599800041449" name="mr_chooser_wifi_warning_description_unknown">"Make sure that the other device is on the same Wi-Fi network as this device"</string>
    <string msgid="5255021372884233706" name="mr_chooser_wifi_warning_description_watch">"Make sure that the other device is on the same Wi-Fi network as this watch"</string>
    <string msgid="5213435473397442608" name="mr_chooser_zero_routes_found_title">"No devices available"</string>
    <string msgid="3330502667672708728" name="mr_controller_album_art">"Album art"</string>
    <string msgid="9171231064758955152" name="mr_controller_casting_screen">"Casting screen"</string>
    <string msgid="5684434439232634509" name="mr_controller_close_description">"Close"</string>
    <string msgid="2585048604188129749" name="mr_controller_collapse_group">"Collapse"</string>
    <string msgid="7812275474138309497" name="mr_controller_disconnect">"Disconnect"</string>
    <string msgid="4521419834052044261" name="mr_controller_expand_group">"Expand"</string>
    <string msgid="855271725131981086" name="mr_controller_no_info_available">"No info available"</string>
    <string msgid="5495452265246139458" name="mr_controller_no_media_selected">"No media selected"</string>
    <string msgid="747801650871398383" name="mr_controller_pause">"Pause"</string>
    <string msgid="1253345086594430054" name="mr_controller_play">"Play"</string>
    <string msgid="5497722768305745508" name="mr_controller_stop">"Stop"</string>
    <string msgid="804210341192624074" name="mr_controller_stop_casting">"Stop casting"</string>
    <string msgid="2955862765169128170" name="mr_controller_volume_slider">"Volume slider"</string>
    <string msgid="4115858704575247342" name="mr_dialog_default_group_name">"Group"</string>
    <string msgid="4307018456678388936" name="mr_dialog_groupable_header">"Add a device"</string>
    <string msgid="6068257520605505468" name="mr_dialog_transferable_header">"Play on a group"</string>
    <string msgid="7449553026175453403" name="mr_system_route_name">"System"</string>
    <string msgid="4088331695424166162" name="mr_user_route_category_name">"Devices"</string>
    <string msgid="542007171693138492" name="navigation_menu">"Navigation menu"</string>
    <string msgid="6610465462668679431" name="not_selected">"Not selected"</string>
    <string msgid="875452955155264703" name="off">"Off"</string>
    <string msgid="8655164131929253426" name="on">"On"</string>
    <string msgid="5941395253238309765" name="range_end">"Range end"</string>
    <string msgid="7097486360902471446" name="range_start">"Range start"</string>
    <string msgid="6420018528474762666" name="search_bar_search">"Search"</string>
    <string msgid="6264217191555673260" name="search_menu_title">"Search"</string>
    <string msgid="6043586758067023" name="selected">"Selected"</string>
    <string msgid="3962933905051144957" name="snackbar_dismiss">"Dismiss"</string>
    <string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string>
    <string msgid="7189888345201419934" name="suggestions_available">"Suggestions below"</string>
    <string msgid="2561197295334830845" name="switch_role">"Switch"</string>
    <string msgid="1672349317127674378" name="tab">"Tab"</string>
    <string msgid="5946805113151406391" name="template_percent">"<ns1:g id="PERCENTAGE">%1$d</ns1:g> per cent."</string>
    <string msgid="5096144640014509074" name="time_picker_am">"AM"</string>
    <string msgid="7241191970823415723" name="time_picker_hour">"Hour"</string>
    <string msgid="4149641012513526783" name="time_picker_hour_24h_suffix">"%1$d hours"</string>
    <string msgid="6081676287789101196" name="time_picker_hour_selection">"Select hour"</string>
    <string msgid="6952032626122080528" name="time_picker_hour_suffix">"%1$d o\'clock"</string>
    <string msgid="5298761125390275834" name="time_picker_hour_text_field">"for hour"</string>
    <string msgid="6116528647594005945" name="time_picker_minute">"Minute"</string>
    <string msgid="8494777394375441602" name="time_picker_minute_selection">"Select minutes"</string>
    <string msgid="3206486707779478173" name="time_picker_minute_suffix">"%1$d minutes"</string>
    <string msgid="994099543833979061" name="time_picker_minute_text_field">"for minutes"</string>
    <string msgid="7352665290700284516" name="time_picker_period_toggle_description">"Select a.m. or p.m."</string>
    <string msgid="2232702812657998674" name="time_picker_pm">"PM"</string>
    <string msgid="2732804537909054941" name="tooltip_long_press_label">"Show tooltip"</string>
    <string msgid="8191239805703103845" name="tooltip_pane_description">"Tooltip"</string>
</resources>