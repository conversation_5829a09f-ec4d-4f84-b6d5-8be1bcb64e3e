<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="background">#FFFAFAFA</color>
    <color name="black">#FF000000</color>
    <color name="cast_connected">#FF4CAF50</color>
    <color name="cast_disconnected">#FF9E9E9E</color>
    <color name="error">#FFF44336</color>
    <color name="primary_color">#FF1976D2</color>
    <color name="primary_variant">#FF1565C0</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_color">#FF03DAC6</color>
    <color name="secondary_variant">#FF018786</color>
    <color name="success">#FF4CAF50</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="surface_variant">#FFF5F5F5</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#FFBDBDBD</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    <color name="warning">#FFFF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="all_videos">All</string>
    <string name="app_description">Cast web videos and local files to your TV with ease</string>
    <string name="app_name">WebVideoCaster</string>
    <string name="back">Back</string>
    <string name="browse_web_videos">Browse Web Videos</string>
    <string name="cancel">Cancel</string>
    <string name="cast">Cast</string>
    <string name="cast_connected">Cast connected</string>
    <string name="cast_failed">Cast failed</string>
    <string name="cast_settings">Cast Settings</string>
    <string name="cast_video">Cast Video</string>
    <string name="casting_to">Casting to %1$s</string>
    <string name="connected_to_device">Connected to %1$s</string>
    <string name="disconnect">Disconnect</string>
    <string name="enter_url">Enter URL</string>
    <string name="error_cast_failed">Failed to cast video</string>
    <string name="error_file_not_found">File not found</string>
    <string name="error_invalid_url">Invalid URL</string>
    <string name="error_network">Network error</string>
    <string name="go">Go</string>
    <string name="large_files">Large Files</string>
    <string name="loading_videos">Loading videos…</string>
    <string name="local_videos">Local Videos</string>
    <string name="no_cast_devices">No cast devices found</string>
    <string name="no_video_found">No video found on this page</string>
    <string name="no_videos_description">We couldn\'t find any video files on your device. Make sure you have videos in your gallery or download folder.</string>
    <string name="no_videos_found">No videos found</string>
    <string name="ok">OK</string>
    <string name="permission_denied">Permission denied</string>
    <string name="permission_granted">Permission granted</string>
    <string name="permission_storage_message">This app needs storage permission to access your video files.</string>
    <string name="permission_storage_title">Storage Permission Required</string>
    <string name="ready_to_cast">Ready to cast</string>
    <string name="recent">Recent</string>
    <string name="retry">Retry</string>
    <string name="scanning_videos">Scanning for videos…</string>
    <string name="search_videos">Search videos…</string>
    <string name="searching_devices">Searching for devices…</string>
    <string name="select_cast_device">Select Cast Device</string>
    <string name="settings">Settings</string>
    <string name="video_count">%1$d videos found</string>
    <string name="video_found">Video found on page</string>
    <string name="video_thumbnail">Video thumbnail</string>
    <style name="Theme.WebVideoCaster" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>