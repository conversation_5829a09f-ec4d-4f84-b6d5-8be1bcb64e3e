<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.LocalVideoActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        app:title="@string/local_videos"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@android:color/white" />

    <!-- Content Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Video List -->
        <LinearLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Search Bar -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:startIconDrawable="@drawable/ic_search"
                app:endIconMode="clear_text"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/search_videos"
                    android:inputType="text"
                    android:imeOptions="actionSearch" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Filter Chips -->
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="8dp">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filters"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:selectionRequired="false">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/all_videos"
                        android:checked="true"
                        style="@style/Widget.Material3.Chip.Filter" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_recent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/recent"
                        style="@style/Widget.Material3.Chip.Filter" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_large"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/large_files"
                        style="@style/Widget.Material3.Chip.Filter" />

                </com.google.android.material.chip.ChipGroup>

            </HorizontalScrollView>

            <!-- Video Count -->
            <TextView
                android:id="@+id/tv_video_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="8dp"
                android:text="@string/loading_videos"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

            <!-- RecyclerView for Videos -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_videos"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingHorizontal="8dp"
                android:clipToPadding="false"
                tools:listitem="@layout/item_video" />

        </LinearLayout>

        <!-- Loading State -->
        <LinearLayout
            android:id="@+id/loading_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="visible">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/scanning_videos"
                android:textSize="16sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/empty_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:src="@drawable/ic_video_library"
                android:layout_marginBottom="24dp"
                android:alpha="0.6"
                android:contentDescription="@string/no_videos_found" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_videos_found"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_videos_description"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:textAlignment="center"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

        <!-- Cast FAB -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_cast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="16dp"
            android:src="@drawable/ic_cast"
            android:contentDescription="@string/cast_video"
            android:visibility="gone" />

    </FrameLayout>

</LinearLayout>
