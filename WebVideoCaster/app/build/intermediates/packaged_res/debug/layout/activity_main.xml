<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    tools:context=".ui.MainActivity">

    <!-- App Logo/Icon -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_cast"
        android:contentDescription="@string/app_name"
        android:scaleType="centerInside" />

    <!-- App Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/primary_color"
        android:layout_marginBottom="16dp" />

    <!-- App Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_description"
        android:textSize="16sp"
        android:textAlignment="center"
        android:textColor="@color/text_secondary"
        android:layout_marginBottom="48dp"
        android:lineSpacingExtra="4dp" />

    <!-- Main Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="32dp">

        <!-- Browse Web Videos Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_browse_web"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/browse_web_videos"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            app:icon="@drawable/ic_web"
            app:iconGravity="start"
            style="@style/Widget.Material3.Button" />

        <!-- Local Videos Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_local_videos"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/local_videos"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            app:icon="@drawable/ic_folder"
            app:iconGravity="start"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- Cast Settings Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cast_settings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/cast_settings"
            android:textSize="16sp"
            app:icon="@drawable/ic_settings"
            app:iconGravity="start"
            style="@style/Widget.Material3.Button.TextButton" />

    </LinearLayout>

    <!-- Cast Status Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cast_status_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardElevation="4dp"
        app:cardCornerRadius="12dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_cast_connected"
                android:layout_marginEnd="12dp"
                android:contentDescription="@string/cast_connected" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_cast_device_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/connected_to_device"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_cast_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ready_to_cast"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_disconnect"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="@string/disconnect"
                android:textSize="12sp"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
