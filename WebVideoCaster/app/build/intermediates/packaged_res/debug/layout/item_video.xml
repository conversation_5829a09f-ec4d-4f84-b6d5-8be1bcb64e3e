<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="12dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Video Thumbnail -->
        <FrameLayout
            android:layout_width="80dp"
            android:layout_height="60dp"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/iv_thumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/surface_variant"
                android:src="@drawable/ic_video_placeholder"
                android:contentDescription="@string/video_thumbnail" />

            <!-- Duration Badge -->
            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="4dp"
                android:background="@drawable/bg_duration_badge"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="00:00"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                tools:text="12:34" />

        </FrameLayout>

        <!-- Video Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Video Title -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Video Title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="4dp"
                tools:text="Sample Video File Name.mp4" />

            <!-- Video Details -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 MB"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    tools:text="125.6 MB" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" • "
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/tv_format"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="MP4"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    tools:text="MP4" />

            </LinearLayout>

            <!-- Video Path -->
            <TextView
                android:id="@+id/tv_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="/storage/emulated/0/Movies/video.mp4"
                android:textSize="11sp"
                android:textColor="@color/text_secondary"
                android:singleLine="true"
                android:ellipsize="start"
                tools:text="/storage/emulated/0/Movies/sample_video.mp4" />

        </LinearLayout>

        <!-- Action Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cast"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginStart="8dp"
            android:layout_gravity="center_vertical"
            android:text="@string/cast"
            android:textSize="12sp"
            app:icon="@drawable/ic_cast"
            app:iconSize="16dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
