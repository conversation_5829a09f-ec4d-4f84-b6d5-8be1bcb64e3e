/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:7: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

/Users/<USER>/Github/WebVideoCaster/app/build.gradle:11: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

/Users/<USER>/Github/WebVideoCaster/app/build.gradle:39: Warning: A newer version of org.jetbrains.kotlin:kotlin-stdlib than 1.9.20 is available: 1.9.22 [GradleDependency]
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:40: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core-ktx:1.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:41: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.appcompat:appcompat:1.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:42: Warning: A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0 [GradleDependency]
    implementation 'com.google.android.material:material:1.11.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:43: Warning: A newer version of androidx.compose.ui:ui than 1.5.4 is available: 1.8.3 [GradleDependency]
    implementation "androidx.compose.ui:ui:1.5.4"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:44: Warning: A newer version of androidx.compose.material3:material3 than 1.1.2 is available: 1.3.2 [GradleDependency]
    implementation "androidx.compose.material3:material3:1.1.2"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:45: Warning: A newer version of androidx.compose.ui:ui-tooling-preview than 1.5.4 is available: 1.8.3 [GradleDependency]
    implementation "androidx.compose.ui:ui-tooling-preview:1.5.4"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:46: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:47: Warning: A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1 [GradleDependency]
    implementation 'androidx.activity:activity-compose:1.8.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:50: Warning: A newer version of com.google.android.gms:play-services-cast-framework than 21.4.0 is available: 22.1.0 [GradleDependency]
    implementation 'com.google.android.gms:play-services-cast-framework:21.4.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:51: Warning: A newer version of androidx.mediarouter:mediarouter than 1.6.0 is available: 1.8.1 [GradleDependency]
    implementation "androidx.mediarouter:mediarouter:1.6.0"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:61: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:62: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:63: Warning: A newer version of androidx.compose.ui:ui-test-junit4 than 1.5.4 is available: 1.8.3 [GradleDependency]
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:1.5.4"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/build.gradle:64: Warning: A newer version of androidx.compose.ui:ui-tooling than 1.5.4 is available: 1.8.3 [GradleDependency]
    debugImplementation "androidx.compose.ui:ui-tooling:1.5.4"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

/Users/<USER>/Github/WebVideoCaster/app/src/main/res/values/themes.xml:13: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
                                            ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

/Users/<USER>/Github/WebVideoCaster/app/src/main/res/values/colors.xml:2: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

/Users/<USER>/Github/WebVideoCaster/app/src/main/AndroidManifest.xml:11: Warning: Should explicitly set android:icon, there is no default [MissingApplicationIcon]
    <application
     ~~~~~~~~~~~

   Explanation for issues of type "MissingApplicationIcon":
   You should set an icon for the application as whole because there is no
   default. This attribute must be set as a reference to a drawable resource
   containing the image (for example @drawable/icon).

   https://developer.android.com/studio/publish/preparing#publishing-configure

/Users/<USER>/Github/WebVideoCaster/app/src/main/res/layout/activity_browser.xml:14: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

/Users/<USER>/Github/WebVideoCaster/app/src/main/res/layout/activity_browser.xml:19: Warning: Hardcoded string "Enter URL", should use @string resource [HardcodedText]
            android:hint="Enter URL"
            ~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/src/main/res/layout/activity_browser.xml:26: Warning: Hardcoded string "Go", should use @string resource [HardcodedText]
            android:text="Go" />
            ~~~~~~~~~~~~~~~~~
/Users/<USER>/Github/WebVideoCaster/app/src/main/res/layout/activity_browser.xml:33: Warning: Hardcoded string "Cast", should use @string resource [HardcodedText]
            android:contentDescription="Cast" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

0 errors, 24 warnings
