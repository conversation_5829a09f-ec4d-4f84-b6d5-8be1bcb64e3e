<variant
    name="release"
    package="com.webvideocaster"
    minSdkVersion="21"
    targetSdkVersion="34"
    mergedManifest="build/intermediates/merged_manifest/release/AndroidManifest.xml"
    manifestMergeReport="build/outputs/logs/manifest-merger-release-report.txt"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.2.2:proguard-rules.pro"
    partialResultsDir="build/intermediates/lint_vital_partial_results/release/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/release/classes:build/tmp/kotlin-classes/release:build/kotlinToolingMetadata:build/intermediates/compile_and_runtime_not_namespaced_r_class_jar/release/R.jar"
      type="MAIN"
      applicationId="com.webvideocaster"
      generatedSourceFolders="build/generated/ap_generated_sources/release/out"
      generatedResourceFolders="build/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/8.9/transforms/49b0c8fd1bee4587aca23aa6beaf3237/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
