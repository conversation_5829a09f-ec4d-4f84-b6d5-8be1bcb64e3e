<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.2" type="incidents">

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="25"
            startOffset="301"
            endLine="9"
            endColumn="929"
            endOffset="1205"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_browser.xml"
            line="14"
            column="10"
            startOffset="480"
            endLine="14"
            endColumn="18"
            endOffset="488"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_browser.xml"
            line="19"
            column="13"
            startOffset="671"
            endLine="19"
            endColumn="37"
            endOffset="695"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Go&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_browser.xml"
            line="26"
            column="13"
            startOffset="905"
            endLine="26"
            endColumn="30"
            endOffset="922"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cast&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_browser.xml"
            line="33"
            column="13"
            startOffset="1157"
            endLine="33"
            endColumn="46"
            endOffset="1190"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="45"
            column="17"
            startOffset="1803"
            endLine="45"
            endColumn="37"
            endOffset="1823"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="47"
            column="17"
            startOffset="1897"
            endLine="47"
            endColumn="40"
            endOffset="1920"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Video Title&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="65"
            column="17"
            startOffset="2465"
            endLine="65"
            endColumn="43"
            endOffset="2491"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 MB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="84"
            column="21"
            startOffset="3232"
            endLine="84"
            endColumn="40"
            endOffset="3251"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot; • &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="92"
            column="21"
            startOffset="3563"
            endLine="92"
            endColumn="39"
            endOffset="3581"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;MP4&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="100"
            column="21"
            startOffset="3899"
            endLine="100"
            endColumn="39"
            endOffset="3917"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;/storage/emulated/0/Movies/video.mp4&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml"
            line="112"
            column="17"
            startOffset="4311"
            endLine="112"
            endColumn="68"
            endOffset="4362"/>
    </incident>

    <incident
        id="PluralsCandidate"
        severity="warning"
        message="Formatting %d followed by words (&quot;videos&quot;): This should probably be a plural rather than a string">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="5"
            startOffset="1512"
            endLine="31"
            endColumn="58"
            endOffset="1565"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/webvideocaster/ui/VideoAdapter.kt"
            line="22"
            column="9"
            startOffset="675"
            endLine="22"
            endColumn="31"
            endOffset="697"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="11"
            column="9"
            startOffset="187"
            endLine="11"
            endColumn="21"
            endOffset="199"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin:kotlin-stdlib than 1.9.20 is available: 1.9.22">
        <location
            file="${:app*projectDir}/build.gradle"
            line="39"
            column="20"
            startOffset="890"
            endLine="39"
            endColumn="72"
            endOffset="942"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="40"
            column="20"
            startOffset="962"
            endLine="40"
            endColumn="51"
            endOffset="993"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="41"
            column="20"
            startOffset="1013"
            endLine="41"
            endColumn="56"
            endOffset="1049"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.11.0"
            replacement="1.12.0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="42"
            column="20"
            startOffset="1069"
            endLine="42"
            endColumn="65"
            endOffset="1114"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.ui:ui than 1.5.4 is available: 1.8.3">
        <fix-replace
            description="Change to 1.8.3"
            family="Update versions"
            oldString="1.5.4"
            replacement="1.8.3"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="43"
            column="20"
            startOffset="1134"
            endLine="43"
            endColumn="50"
            endOffset="1164"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.material3:material3 than 1.1.2 is available: 1.3.2">
        <fix-replace
            description="Change to 1.3.2"
            family="Update versions"
            oldString="1.1.2"
            replacement="1.3.2"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="44"
            column="20"
            startOffset="1184"
            endLine="44"
            endColumn="64"
            endOffset="1228"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.ui:ui-tooling-preview than 1.5.4 is available: 1.8.3">
        <fix-replace
            description="Change to 1.8.3"
            family="Update versions"
            oldString="1.5.4"
            replacement="1.8.3"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="45"
            column="20"
            startOffset="1248"
            endLine="45"
            endColumn="66"
            endOffset="1294"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="46"
            column="20"
            startOffset="1314"
            endLine="46"
            endColumn="68"
            endOffset="1362"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="47"
            column="20"
            startOffset="1382"
            endLine="47"
            endColumn="62"
            endOffset="1424"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.gms:play-services-cast-framework than 21.4.0 is available: 22.1.0">
        <fix-replace
            description="Change to 22.1.0"
            family="Update versions"
            oldString="21.4.0"
            replacement="22.1.0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="50"
            column="20"
            startOffset="1463"
            endLine="50"
            endColumn="80"
            endOffset="1523"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.mediarouter:mediarouter than 1.6.0 is available: 1.8.1">
        <fix-replace
            description="Change to 1.8.1"
            family="Update versions"
            oldString="1.6.0"
            replacement="1.8.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="51"
            column="20"
            startOffset="1543"
            endLine="51"
            endColumn="60"
            endOffset="1583"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="61"
            column="31"
            startOffset="1885"
            endLine="61"
            endColumn="62"
            endOffset="1916"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="62"
            column="31"
            startOffset="1947"
            endLine="62"
            endColumn="75"
            endOffset="1991"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.ui:ui-test-junit4 than 1.5.4 is available: 1.8.3">
        <fix-replace
            description="Change to 1.8.3"
            family="Update versions"
            oldString="1.5.4"
            replacement="1.8.3"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="63"
            column="31"
            startOffset="2022"
            endLine="63"
            endColumn="73"
            endOffset="2064"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.ui:ui-tooling than 1.5.4 is available: 1.8.3">
        <fix-replace
            description="Change to 1.8.3"
            family="Update versions"
            oldString="1.5.4"
            replacement="1.8.3"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="64"
            column="25"
            startOffset="2089"
            endLine="64"
            endColumn="63"
            endOffset="2127"/>
    </incident>

</incidents>
