http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_browser.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,+color:purple_200,0,V"#FFBB86FC";black,0,V"#FF000000";teal_200,0,V"#FF03DAC5";white,0,V"#FFFFFFFF";teal_700,0,V"#FF018786";purple_700,0,V"#FF3700B3";purple_500,0,V"#FF6200EE";+id:cast_button,1,F;url_edit_text,1,F;go_button,1,F;web_view,1,F;+layout:activity_browser,1,F;+string:app_name,2,V"WebVideoCaster";+style:Theme.WebVideoCaster,3,VDTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;