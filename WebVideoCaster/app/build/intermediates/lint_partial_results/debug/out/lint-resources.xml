http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_folder.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cast.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_library.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_web.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_placeholder.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_duration_badge.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cast_connected.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_local_video.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_browser.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,+color:purple_200,0,V"#FFBB86FC";surface,0,V"#FFFFFFFF";text_primary,0,V"#FF212121";secondary_variant,0,V"#FF018786";black,0,V"#FF000000";primary_color,0,V"#FF1976D2";primary_variant,0,V"#FF1565C0";error,0,V"#FFF44336";teal_200,0,V"#FF03DAC5";white,0,V"#FFFFFFFF";cast_disconnected,0,V"#FF9E9E9E";teal_700,0,V"#FF018786";background,0,V"#FFFAFAFA";success,0,V"#FF4CAF50";warning,0,V"#FFFF9800";secondary_color,0,V"#FF03DAC6";surface_variant,0,V"#FFF5F5F5";purple_700,0,V"#FF3700B3";cast_connected,0,V"#FF4CAF50";text_hint,0,V"#FFBDBDBD";purple_500,0,V"#FF6200EE";text_secondary,0,V"#FF757575";+drawable:ic_folder,1,F;ic_cast,2,F;ic_video_library,3,F;ic_web,4,F;ic_video_placeholder,5,F;ic_arrow_back,6,F;bg_duration_badge,7,F;ic_settings,8,F;ic_cast_connected,9,F;ic_search,10,F;+id:tv_size,11,F;tv_title,11,F;tv_format,11,F;btn_disconnect,12,F;et_search,13,F;tv_cast_device_name,12,F;iv_thumbnail,11,F;chip_large,13,F;tv_video_count,13,F;chip_all,13,F;btn_local_videos,12,F;btn_browse_web,12,F;go_button,14,F;tv_duration,11,F;content_container,13,F;btn_cast_settings,12,F;web_view,14,F;tv_path,11,F;chip_group_filters,13,F;empty_container,13,F;btn_cast,11,F;tv_cast_status,12,F;cast_button,14,F;fab_cast,13,F;cast_status_card,12,F;toolbar,13,F;url_edit_text,14,F;rv_videos,13,F;loading_container,13,F;chip_recent,13,F;+layout:activity_browser,14,F;item_video,11,F;activity_local_video,13,F;activity_main,12,F;+string:cancel,15,V"Cancel";disconnect,15,V"Disconnect";all_videos,15,V"All";permission_storage_title,15,V"Storage Permission Required";cast_settings,15,V"Cast Settings";back,15,V"Back";select_cast_device,15,V"Select Cast Device";casting_to,15,V"Casting to %1$s";search_videos,15,V"Search videos…";error_network,15,V"Network error";cast,15,V"Cast";large_files,15,V"Large Files";scanning_videos,15,V"Scanning for videos…";loading_videos,15,V"Loading videos…";connected_to_device,15,V"Connected to %1$s";browse_web_videos,15,V"Browse Web Videos";cast_connected,15,V"Cast connected";ok,15,V"OK";local_videos,15,V"Local Videos";recent,15,V"Recent";searching_devices,15,V"Searching for devices…";retry,15,V"Retry";cast_video,15,V"Cast Video";permission_granted,15,V"Permission granted";settings,15,V"Settings";video_found,15,V"Video found on page";error_invalid_url,15,V"Invalid URL";no_video_found,15,V"No video found on this page";permission_denied,15,V"Permission denied";error_file_not_found,15,V"File not found";enter_url,15,V"Enter URL";go,15,V"Go";cast_failed,15,V"Cast failed";no_videos_description,15,V"We couldn't find any video files on your device. Make sure you have videos in your gallery or download folder.";no_cast_devices,15,V"No cast devices found";video_thumbnail,15,V"Video thumbnail";app_name,15,V"WebVideoCaster";ready_to_cast,15,V"Ready to cast";permission_storage_message,15,V"This app needs storage permission to access your video files.";error_cast_failed,15,V"Failed to cast video";no_videos_found,15,V"No videos found";video_count,15,V"%1$d videos found";app_description,15,V"Cast web videos and local files to your TV with ease";+style:Theme.WebVideoCaster,16,VDTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;