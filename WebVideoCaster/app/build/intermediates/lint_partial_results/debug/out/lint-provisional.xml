<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.2" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="36"
            startOffset="367"
            endLine="7"
            endColumn="76"
            endOffset="407"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cast.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cast.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cast_connected.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="40"
            endOffset="220"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cast_connected.xml"
            line="8"
            column="26"
            startOffset="256"
            endLine="8"
            endColumn="46"
            endOffset="276"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_folder.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_folder.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_library.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_library.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_placeholder.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_placeholder.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_web.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_web.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="714"
                endOffset="733"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="13"
            column="45"
            startOffset="714"
            endLine="13"
            endColumn="64"
            endOffset="733"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/webvideocaster/debug/NetworkDebugHelper.kt"
            line="34"
            column="43"
            startOffset="983"
            endLine="34"
            endColumn="56"
            endOffset="996"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.net.ConnectivityManager#getActiveNetwork`"/>
        </map>
    </incident>

</incidents>
