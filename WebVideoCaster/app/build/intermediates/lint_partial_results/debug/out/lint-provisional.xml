<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.2" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="36"
            startOffset="367"
            endLine="7"
            endColumn="76"
            endOffset="407"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="714"
                endOffset="733"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="13"
            column="45"
            startOffset="714"
            endLine="13"
            endColumn="64"
            endOffset="733"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

</incidents>
