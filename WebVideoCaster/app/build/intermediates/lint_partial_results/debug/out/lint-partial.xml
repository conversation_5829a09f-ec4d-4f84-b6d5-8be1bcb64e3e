<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.2" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.webvideocaster.ui.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.CAST_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/webvideocaster/cast/CastManager.kt"
                            line="132"
                            column="26"
                            startOffset="3965"
                            endLine="132"
                            endColumn="66"
                            endOffset="4005"/>
                    </map>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.text_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="467"
            endLine="14"
            endColumn="31"
            endOffset="486"/>
        <location id="R.string.settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="2739"
            endLine="59"
            endColumn="28"
            endOffset="2754"/>
        <location id="R.string.searching_devices"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="1913"
            endLine="41"
            endColumn="37"
            endOffset="1937"/>
        <location id="R.string.permission_storage_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2008"
            endLine="44"
            endColumn="44"
            endOffset="2039"/>
        <location id="R.string.retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="2699"
            endLine="58"
            endColumn="25"
            endOffset="2711"/>
        <location id="R.color.background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="394"
            endLine="11"
            endColumn="29"
            endOffset="411"/>
        <location id="R.color.secondary_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="163"
            endLine="5"
            endColumn="34"
            endOffset="185"/>
        <location id="R.string.error_invalid_url"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2544"
            endLine="53"
            endColumn="37"
            endOffset="2568"/>
        <location id="R.color.success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="34"
            column="12"
            startOffset="1201"
            endLine="34"
            endColumn="26"
            endOffset="1215"/>
        <location id="R.string.permission_granted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2206"
            endLine="46"
            endColumn="38"
            endOffset="2231"/>
        <location id="R.color.cast_disconnected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1033"
            endLine="29"
            endColumn="36"
            endOffset="1057"/>
        <location id="R.string.ok"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="2623"
            endLine="56"
            endColumn="22"
            endOffset="2632"/>
        <location id="R.color.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="32"
            column="12"
            startOffset="1115"
            endLine="32"
            endColumn="24"
            endOffset="1127"/>
        <entry
            name="model"
            string="attr[actionBarSize(R),colorOnSurface(R),selectableItemBackground(R),colorPrimaryVariant(R)],color[purple_200(D),surface(D),text_primary(D),secondary_variant(D),black(U),primary_color(U),primary_variant(D),error(D),teal_200(U),white(U),cast_disconnected(D),teal_700(U),background(D),success(D),warning(D),cast_connected(U),surface_variant(U),secondary_color(D),purple_700(U),text_hint(D),purple_500(U),text_secondary(U)],drawable[ic_folder(U),ic_cast(U),ic_video_library(U),ic_web(U),ic_video_placeholder(U),ic_arrow_back(U),bg_duration_badge(U),ic_settings(U),ic_cast_connected(U),ic_search(U)],id[tv_size(U),tv_title(U),tv_format(U),btn_disconnect(U),et_search(U),tv_cast_device_name(U),iv_thumbnail(U),chip_large(U),tv_video_count(U),chip_all(D),btn_local_videos(U),btn_browse_web(U),go_button(U),tv_duration(U),content_container(U),btn_cast_settings(U),web_view(U),tv_path(U),chip_group_filters(U),empty_container(U),btn_cast(U),tv_cast_status(U),cast_button(U),fab_cast(U),cast_status_card(U),toolbar(U),url_edit_text(U),rv_videos(U),loading_container(U),chip_recent(U)],layout[activity_browser(U),item_video(U),activity_local_video(U),activity_main(U)],string[cancel(U),disconnect(U),all_videos(U),permission_storage_title(D),cast_settings(U),back(D),select_cast_device(U),casting_to(U),search_videos(U),error_network(D),large_files(U),scanning_videos(U),cast(U),loading_videos(U),connected_to_device(U),browse_web_videos(U),cast_connected(U),ok(D),local_videos(U),recent(U),searching_devices(D),retry(D),cast_video(U),permission_granted(D),settings(D),video_found(U),error_invalid_url(D),no_video_found(U),permission_denied(U),error_file_not_found(D),enter_url(D),go(D),cast_failed(U),no_videos_description(U),no_cast_devices(U),video_thumbnail(U),app_name(U),ready_to_cast(U),permission_storage_message(D),error_cast_failed(D),no_videos_found(U),video_count(U),app_description(U)],style[Theme_WebVideoCaster(U),Widget_Material3_Button(R),Widget_Material3_Button_TextButton(R),Widget_Material3_TextInputLayout_OutlinedBox(R),Widget_Material3_Button_OutlinedButton(R),Widget_Material3_Chip_Filter(R),Theme_MaterialComponents_DayNight_DarkActionBar(R)];1a^1,1b^1,1c^1,1d^1,1e^1,1f^1,21^1,22^13,23^1,43^2^14^69^1e^20^19^52^1b^75,44^9^0^1f^58^23^74^4e^48^76^59^50^53^19^43^51^6e^1c^67^5c^1b,45^6a^1b^9^70^19^55^1d^72^58^1a^75^4a^21^73^56^22^54^6b^47,71^77^18^16^d^c^f^8^3;;;"/>
        <location id="R.string.error_file_not_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2413"
            endLine="51"
            endColumn="40"
            endOffset="2440"/>
        <location id="R.color.primary_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="111"
            endLine="4"
            endColumn="34"
            endOffset="133"/>
        <location id="R.string.go"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="626"
            endLine="15"
            endColumn="22"
            endOffset="635"/>
        <location id="R.string.error_network"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="2357"
            endLine="50"
            endColumn="33"
            endOffset="2377"/>
        <location id="R.color.surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="298"
            endLine="9"
            endColumn="26"
            endOffset="312"/>
        <location id="R.string.enter_url"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="578"
            endLine="14"
            endColumn="29"
            endOffset="594"/>
        <location id="R.color.warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="33"
            column="12"
            startOffset="1157"
            endLine="33"
            endColumn="26"
            endOffset="1171"/>
        <location id="R.string.back"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="2785"
            endLine="60"
            endColumn="24"
            endOffset="2796"/>
        <location id="R.color.text_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="567"
            endLine="16"
            endColumn="28"
            endOffset="583"/>
        <location id="R.string.permission_storage_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2089"
            endLine="45"
            endColumn="46"
            endOffset="2122"/>
        <location id="R.color.secondary_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="215"
            endLine="6"
            endColumn="36"
            endOffset="239"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="641"
            endLine="19"
            endColumn="29"
            endOffset="658"/>
        <location id="R.string.error_cast_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="2477"
            endLine="52"
            endColumn="37"
            endOffset="2501"/>
    </map>

</incidents>
