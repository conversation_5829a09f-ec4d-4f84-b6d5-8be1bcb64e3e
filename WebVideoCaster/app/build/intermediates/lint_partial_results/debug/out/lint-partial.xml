<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.2" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.webvideocaster.ui.BrowserActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.CAST_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/webvideocaster/cast/CastManager.kt"
                            line="132"
                            column="26"
                            startOffset="3965"
                            endLine="132"
                            endColumn="66"
                            endOffset="4005"/>
                    </map>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="2"
            column="12"
            startOffset="23"
            endLine="2"
            endColumn="29"
            endOffset="40"/>
        <entry
            name="model"
            string="attr[colorPrimaryVariant(R)],color[purple_200(D),black(U),teal_200(U),white(U),teal_700(U),purple_700(U),purple_500(U)],id[cast_button(U),url_edit_text(U),go_button(U),web_view(U)],layout[activity_browser(U)],string[app_name(U)],style[Theme_WebVideoCaster(U),Theme_MaterialComponents_DayNight_DarkActionBar(R)];e^f^7^6^4^3^5^2^0;;;"/>
    </map>

</incidents>
