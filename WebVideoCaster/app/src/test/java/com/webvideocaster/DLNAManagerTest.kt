package com.webvideocaster

import android.content.Context
import com.webvideocaster.cast.DLNAManager
import com.webvideocaster.model.DeviceType
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@RunWith(RobolectricTestRunner::class)
class DLNAManagerTest {

    @Mock
    private lateinit var mockContext: Context
    
    private lateinit var dlnaManager: DLNAManager

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        mockContext = RuntimeEnvironment.getApplication()
        dlnaManager = DLNAManager(mockContext)
    }

    @Test
    fun testDLNAManagerInitialization() {
        // Test that DLNA manager can be initialized
        dlnaManager.bindUpnpService()
        
        // Wait a bit for device discovery
        runBlocking {
            delay(1000)
        }
        
        // Should have at least the mock device
        val devices = dlnaManager.getAvailableDevices()
        assert(devices.isNotEmpty())
        assert(devices.any { it.type == DeviceType.DLNA })
    }

    @Test
    fun testDeviceDiscovery() {
        var devicesFound = false
        
        dlnaManager.setOnDevicesUpdatedListener { devices ->
            if (devices.isNotEmpty()) {
                devicesFound = true
            }
        }
        
        dlnaManager.bindUpnpService()
        
        // Wait for discovery
        runBlocking {
            delay(2000)
        }
        
        assert(devicesFound)
    }

    @Test
    fun testCasting() {
        dlnaManager.bindUpnpService()
        
        runBlocking {
            delay(1000)
        }
        
        val devices = dlnaManager.getAvailableDevices()
        if (devices.isNotEmpty()) {
            // Test casting to first device
            dlnaManager.cast("http://example.com/video.mp4", devices.first().id)
            // If no exception thrown, test passes
        }
    }
}
