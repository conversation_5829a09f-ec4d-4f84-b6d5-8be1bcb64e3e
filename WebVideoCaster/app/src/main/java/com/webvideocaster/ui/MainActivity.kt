package com.webvideocaster.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.webvideocaster.R
import com.webvideocaster.cast.CastManager

class MainActivity : AppCompatActivity() {

    private lateinit var btnBrowseWeb: MaterialButton
    private lateinit var btnLocalVideos: MaterialButton
    private lateinit var btnCastSettings: MaterialButton
    private lateinit var castStatusCard: MaterialCardView
    private lateinit var tvCastDeviceName: TextView
    private lateinit var tvCastStatus: TextView
    private lateinit var btnDisconnect: MaterialButton

    private lateinit var castManager: CastManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        initCastManager()
        setupListeners()
        updateCastStatus()
    }

    private fun initViews() {
        btnBrowseWeb = findViewById(R.id.btn_browse_web)
        btnLocalVideos = findViewById(R.id.btn_local_videos)
        btnCastSettings = findViewById(R.id.btn_cast_settings)
        castStatusCard = findViewById(R.id.cast_status_card)
        tvCastDeviceName = findViewById(R.id.tv_cast_device_name)
        tvCastStatus = findViewById(R.id.tv_cast_status)
        btnDisconnect = findViewById(R.id.btn_disconnect)
    }

    private fun initCastManager() {
        castManager = CastManager(this)
        castManager.initialize()
    }

    private fun setupListeners() {
        btnBrowseWeb.setOnClickListener { startActivity(Intent(this, BrowserActivity::class.java)) }

        btnLocalVideos.setOnClickListener {
            startActivity(Intent(this, LocalVideoActivity::class.java))
        }

        btnCastSettings.setOnClickListener {
            // TODO: Implement cast settings
            // For now, just show available devices
            showCastDevices()
        }

        btnDisconnect.setOnClickListener {
            castManager.stopCasting()
            updateCastStatus()
        }
    }

    private fun showCastDevices() {
        val devices = castManager.getAvailableDevices()
        if (devices.isEmpty()) {
            // Show no devices message
        } else {
            // Show device selection dialog
        }
    }

    private fun updateCastStatus() {
        val isConnected = castManager.isConnected()
        castStatusCard.visibility = if (isConnected) View.VISIBLE else View.GONE

        if (isConnected) {
            tvCastDeviceName.text = getString(R.string.connected_to_device, "Cast Device")
            tvCastStatus.text = getString(R.string.ready_to_cast)
        }
    }

    override fun onResume() {
        super.onResume()
        updateCastStatus()
    }

    override fun onDestroy() {
        super.onDestroy()
        castManager.cleanup()
    }
}
