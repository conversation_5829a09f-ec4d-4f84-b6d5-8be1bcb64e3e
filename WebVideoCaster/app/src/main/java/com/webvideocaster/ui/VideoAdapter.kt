package com.webvideocaster.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.webvideocaster.R
import com.webvideocaster.media.VideoFile
import com.webvideocaster.media.VideoScanner

class VideoAdapter(
    private val onCastClick: (VideoFile) -> Unit
) : RecyclerView.Adapter<VideoAdapter.VideoViewHolder>() {
    
    private var videos = listOf<VideoFile>()
    
    fun updateVideos(newVideos: List<VideoFile>) {
        videos = newVideos
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_video, parent, false)
        return VideoViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: VideoViewHolder, position: Int) {
        holder.bind(videos[position])
    }
    
    override fun getItemCount(): Int = videos.size
    
    inner class VideoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val thumbnailImageView: ImageView = itemView.findViewById(R.id.iv_thumbnail)
        private val titleTextView: TextView = itemView.findViewById(R.id.tv_title)
        private val durationTextView: TextView = itemView.findViewById(R.id.tv_duration)
        private val sizeTextView: TextView = itemView.findViewById(R.id.tv_size)
        private val formatTextView: TextView = itemView.findViewById(R.id.tv_format)
        private val pathTextView: TextView = itemView.findViewById(R.id.tv_path)
        private val castButton: MaterialButton = itemView.findViewById(R.id.btn_cast)
        
        fun bind(videoFile: VideoFile) {
            titleTextView.text = videoFile.title.ifEmpty { videoFile.displayName }
            durationTextView.text = VideoScanner(itemView.context).formatDuration(videoFile.duration)
            sizeTextView.text = VideoScanner(itemView.context).formatFileSize(videoFile.size)
            formatTextView.text = getFileExtension(videoFile.displayName).uppercase()
            pathTextView.text = videoFile.path
            
            // Set thumbnail placeholder
            thumbnailImageView.setImageResource(R.drawable.ic_video_placeholder)
            
            castButton.setOnClickListener {
                onCastClick(videoFile)
            }
            
            itemView.setOnClickListener {
                onCastClick(videoFile)
            }
        }
        
        private fun getFileExtension(fileName: String): String {
            return fileName.substringAfterLast('.', "")
        }
    }
}
