package com.webvideocaster.ui

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.chip.ChipGroup
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.textfield.TextInputEditText
import com.webvideocaster.R
import com.webvideocaster.cast.CastManager
import com.webvideocaster.media.VideoFile
import com.webvideocaster.media.VideoScanner
import com.webvideocaster.network.LocalHttpServer
import kotlinx.coroutines.*

class LocalVideoActivity : AppCompatActivity() {

    companion object {
        private const val STORAGE_PERMISSION_REQUEST = 100
    }

    private lateinit var toolbar: MaterialToolbar
    private lateinit var searchEditText: TextInputEditText
    private lateinit var chipGroupFilters: ChipGroup
    private lateinit var videoCountText: TextView
    private lateinit var recyclerView: RecyclerView
    private lateinit var loadingContainer: LinearLayout
    private lateinit var emptyContainer: LinearLayout
    private lateinit var contentContainer: LinearLayout
    private lateinit var castFab: FloatingActionButton

    private lateinit var videoScanner: VideoScanner
    private lateinit var castManager: CastManager
    private lateinit var httpServer: LocalHttpServer
    private lateinit var videoAdapter: VideoAdapter

    private var allVideos = listOf<VideoFile>()
    private var filteredVideos = listOf<VideoFile>()
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_local_video)

        initViews()
        initManagers()
        setupRecyclerView()
        setupListeners()

        if (checkStoragePermission()) {
            loadVideos()
        } else {
            requestStoragePermission()
        }
    }

    private fun initViews() {
        toolbar = findViewById(R.id.toolbar)
        searchEditText = findViewById(R.id.et_search)
        chipGroupFilters = findViewById(R.id.chip_group_filters)
        videoCountText = findViewById(R.id.tv_video_count)
        recyclerView = findViewById(R.id.rv_videos)
        loadingContainer = findViewById(R.id.loading_container)
        emptyContainer = findViewById(R.id.empty_container)
        contentContainer = findViewById(R.id.content_container)
        castFab = findViewById(R.id.fab_cast)

        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    private fun initManagers() {
        videoScanner = VideoScanner(this)
        castManager = CastManager(this)
        httpServer = LocalHttpServer()

        castManager.initialize()
    }

    private fun setupRecyclerView() {
        videoAdapter = VideoAdapter { videoFile -> castVideo(videoFile) }
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = videoAdapter
    }

    private fun setupListeners() {
        toolbar.setNavigationOnClickListener { finish() }

        searchEditText.addTextChangedListener(
                object : TextWatcher {
                    override fun beforeTextChanged(
                            s: CharSequence?,
                            start: Int,
                            count: Int,
                            after: Int
                    ) {}
                    override fun onTextChanged(
                            s: CharSequence?,
                            start: Int,
                            before: Int,
                            count: Int
                    ) {}
                    override fun afterTextChanged(s: Editable?) {
                        filterVideos()
                    }
                }
        )

        chipGroupFilters.setOnCheckedStateChangeListener { _, checkedIds -> filterVideos() }

        castFab.setOnClickListener {
            // Show cast device selection
            Toast.makeText(this, "Select a video to cast", Toast.LENGTH_SHORT).show()
        }
    }

    private fun checkStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) ==
                PackageManager.PERMISSION_GRANTED
    }

    private fun requestStoragePermission() {
        ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                STORAGE_PERMISSION_REQUEST
        )
    }

    override fun onRequestPermissionsResult(
            requestCode: Int,
            permissions: Array<out String>,
            grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == STORAGE_PERMISSION_REQUEST) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                loadVideos()
            } else {
                Toast.makeText(this, getString(R.string.permission_denied), Toast.LENGTH_LONG)
                        .show()
                finish()
            }
        }
    }

    private fun loadVideos() {
        showLoading(true)

        scope.launch {
            try {
                val videos = withContext(Dispatchers.IO) { videoScanner.scanForLocalVideos() }

                allVideos = videos
                filteredVideos = videos

                updateUI()
            } catch (e: Exception) {
                Toast.makeText(
                                this@LocalVideoActivity,
                                "Error loading videos: ${e.message}",
                                Toast.LENGTH_LONG
                        )
                        .show()
                showEmpty(true)
            }
        }
    }

    private fun filterVideos() {
        val query = searchEditText.text.toString().lowercase()
        val checkedChipId = chipGroupFilters.checkedChipId

        filteredVideos =
                allVideos.filter { video ->
                    // Text filter
                    val matchesQuery =
                            query.isEmpty() ||
                                    video.title.lowercase().contains(query) ||
                                    video.displayName.lowercase().contains(query)

                    // Chip filter
                    val matchesFilter =
                            when (checkedChipId) {
                                R.id.chip_recent -> {
                                    // Recent videos (last 30 days)
                                    val thirtyDaysAgo =
                                            System.currentTimeMillis() / 1000 - (30 * 24 * 60 * 60)
                                    video.dateModified > thirtyDaysAgo
                                }
                                R.id.chip_large -> {
                                    // Large files (> 100MB)
                                    video.size > 100 * 1024 * 1024
                                }
                                else -> true // All videos
                            }

                    matchesQuery && matchesFilter
                }

        updateUI()
    }

    private fun updateUI() {
        showLoading(false)

        if (filteredVideos.isEmpty()) {
            showEmpty(true)
        } else {
            showEmpty(false)
            videoAdapter.updateVideos(filteredVideos)
            videoCountText.text = getString(R.string.video_count, filteredVideos.size)
        }
    }

    private fun showLoading(show: Boolean) {
        loadingContainer.visibility = if (show) View.VISIBLE else View.GONE
        contentContainer.visibility = if (show) View.GONE else View.VISIBLE
    }

    private fun showEmpty(show: Boolean) {
        emptyContainer.visibility = if (show) View.VISIBLE else View.GONE
        contentContainer.visibility = if (show) View.GONE else View.VISIBLE
    }

    private fun castVideo(videoFile: VideoFile) {
        scope.launch {
            try {
                // Start HTTP server if not running
                if (!httpServer.isRunning()) {
                    withContext(Dispatchers.IO) { httpServer.start() }
                }

                // Get URL for the video file
                val videoUrl = httpServer.getUrlForFile(videoFile.path)
                if (videoUrl.isEmpty()) {
                    Toast.makeText(
                                    this@LocalVideoActivity,
                                    "Failed to serve video file",
                                    Toast.LENGTH_SHORT
                            )
                            .show()
                    return@launch
                }

                // Cast the video
                castManager.cast(videoUrl)
                Toast.makeText(
                                this@LocalVideoActivity,
                                getString(R.string.casting_to, "device"),
                                Toast.LENGTH_SHORT
                        )
                        .show()
            } catch (e: Exception) {
                Toast.makeText(
                                this@LocalVideoActivity,
                                getString(R.string.cast_failed),
                                Toast.LENGTH_LONG
                        )
                        .show()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
        castManager.cleanup()

        // Stop HTTP server
        try {
            httpServer.stop()
        } catch (e: Exception) {
            // Ignore
        }
    }
}
