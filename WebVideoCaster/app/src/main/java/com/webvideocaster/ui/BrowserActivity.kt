package com.webvideocaster.ui

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Bundle
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.webvideocaster.R
import com.webvideocaster.cast.CastManager
import com.webvideocaster.cast.ChromecastManager
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType

class BrowserActivity : AppCompatActivity() {

    private lateinit var webView: WebView
    private lateinit var urlEditText: EditText
    private lateinit var goButton: Button
    private lateinit var castButton: ImageButton
    private lateinit var castManager: CastManager
    private lateinit var chromecastManager: ChromecastManager

    private val detectedVideoUrls = mutableSetOf<String>()
    private var availableDevices: List<Device> = listOf()

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_browser)

        webView = findViewById(R.id.web_view)
        urlEditText = findViewById(R.id.url_edit_text)
        goButton = findViewById(R.id.go_button)
        castButton = findViewById(R.id.cast_button)

        // Initialize Cast Manager
        castManager = CastManager(this)
        chromecastManager = ChromecastManager(this)
        chromecastManager.setOnDevicesUpdatedListener { devices ->
            availableDevices = devices
            // Maybe update a cast icon state here
        }

        val settings = webView.settings
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.databaseEnabled = true
        settings.setSupportZoom(true)
        settings.useWideViewPort = true
        settings.loadWithOverviewMode = true
        settings.userAgentString =
                "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"

        webView.webViewClient =
                object : WebViewClient() {
                    override fun shouldInterceptRequest(
                            view: WebView?,
                            request: WebResourceRequest?
                    ): WebResourceResponse? {
                        val url = request?.url.toString()
                        if (isVideoUrl(url) && detectedVideoUrls.add(url)) {
                            // New video detected, maybe show a toast or update an icon
                            // For now, we just add it to the list.
                            // The user will initiate casting via a UI element.
                        }
                        return super.shouldInterceptRequest(view, request)
                    }
                }

        goButton.setOnClickListener {
            var url = urlEditText.text.toString()
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                url = "https://$url"
            }
            webView.loadUrl(url)
        }

        // Load a default page
        webView.loadUrl("https://www.google.com")

        castButton.setOnClickListener { showVideoSelectionDialog() }
    }

    override fun onResume() {
        super.onResume()
        chromecastManager.startDeviceDiscovery()
    }

    override fun onPause() {
        super.onPause()
        chromecastManager.stopDeviceDiscovery()
    }

    private fun isVideoUrl(url: String): Boolean {
        return url.endsWith(".mp4", true) ||
                url.endsWith(".m3u8", true) ||
                url.endsWith(".webm", true) ||
                url.endsWith(".mkv", true) ||
                url.endsWith(".avi", true)
    }

    private fun showVideoSelectionDialog() {
        val videoArray = detectedVideoUrls.toTypedArray()

        AlertDialog.Builder(this)
                .setTitle("选择一个视频投屏")
                .setItems(
                        videoArray,
                        DialogInterface.OnClickListener { _, which ->
                            val selectedVideoUrl = videoArray[which]
                            showDeviceSelectionDialog(selectedVideoUrl)
                        }
                )
                .setNegativeButton("取消", null)
                .show()
    }

    private fun showDeviceSelectionDialog(videoUrl: String) {
        val deviceNames = availableDevices.map { it.name }.toTypedArray()

        AlertDialog.Builder(this)
                .setTitle("选择一个设备")
                .setItems(
                        deviceNames,
                        DialogInterface.OnClickListener { _, which ->
                            val selectedDevice = availableDevices[which]
                            // For now, we only handle Chromecast
                            if (selectedDevice.type == DeviceType.CHROMECAST) {
                                chromecastManager.cast(videoUrl, selectedDevice.id)
                            }
                        }
                )
                .setNegativeButton("取消", null)
                .show()
    }
}
