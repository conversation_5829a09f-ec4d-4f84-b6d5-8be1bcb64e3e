package com.webvideocaster.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.webvideocaster.R
import com.webvideocaster.cast.CastManager
import com.webvideocaster.cast.ChromecastManager
import com.webvideocaster.media.VideoParser
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType

class BrowserActivity : AppCompatActivity() {

    private lateinit var webView: WebView
    private lateinit var urlEditText: EditText
    private lateinit var goButton: Button
    private lateinit var castButton: ImageButton
    private lateinit var castManager: CastManager
    private lateinit var chromecastManager: ChromecastManager
    private lateinit var videoParser: VideoParser

    private val detectedVideoUrls = mutableSetOf<String>()
    private var availableDevices: List<Device> = listOf()
    private var currentPageUrl: String = ""

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_browser)

        webView = findViewById(R.id.web_view)
        urlEditText = findViewById(R.id.url_edit_text)
        goButton = findViewById(R.id.go_button)
        castButton = findViewById(R.id.cast_button)

        // Initialize Cast Manager and Video Parser
        castManager = CastManager(this)
        chromecastManager = ChromecastManager(this)
        videoParser = VideoParser()
        chromecastManager.setOnDevicesUpdatedListener { devices ->
            // 更新可用设备列表（包含所有类型的设备）
            availableDevices = castManager.getAvailableDevices()
            updateCastButtonState()
        }

        val settings = webView.settings
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.databaseEnabled = true
        settings.setSupportZoom(true)
        settings.useWideViewPort = true
        settings.loadWithOverviewMode = true
        settings.userAgentString =
                "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"

        webView.webViewClient =
                object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        url?.let {
                            currentPageUrl = it
                            // Parse the page content for video URLs
                            view?.evaluateJavascript("document.documentElement.outerHTML") { html ->
                                if (html != null && html != "null") {
                                    val cleanHtml =
                                            html.replace("\\\"", "\"").removeSurrounding("\"")
                                    videoParser.parseVideoUrl(cleanHtml) { videoUrl ->
                                        runOnUiThread {
                                            if (detectedVideoUrls.add(videoUrl)) {
                                                updateCastButtonState()
                                                // 检测到新视频时自动弹出投屏选择对话框
                                                showVideoDetectedDialog()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    override fun shouldInterceptRequest(
                            view: WebView?,
                            request: WebResourceRequest?
                    ): WebResourceResponse? {
                        val url = request?.url.toString()
                        if (isVideoUrl(url) && detectedVideoUrls.add(url)) {
                            runOnUiThread { updateCastButtonState() }
                        }
                        return super.shouldInterceptRequest(view, request)
                    }
                }

        goButton.setOnClickListener {
            var url = urlEditText.text.toString()
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                url = "https://$url"
            }
            webView.loadUrl(url)
        }

        // Load a default page
        webView.loadUrl("https://www.google.com")

        castButton.setOnClickListener { showVideoSelectionDialog() }

        // Initialize cast button state
        updateCastButtonState()
    }

    override fun onResume() {
        super.onResume()
        chromecastManager.startDeviceDiscovery()
    }

    override fun onPause() {
        super.onPause()
        chromecastManager.stopDeviceDiscovery()
    }

    private fun updateCastButtonState() {
        castButton.isEnabled = detectedVideoUrls.isNotEmpty() && availableDevices.isNotEmpty()
        castButton.alpha = if (castButton.isEnabled) 1.0f else 0.5f
    }

    private fun isVideoUrl(url: String): Boolean {
        return url.endsWith(".mp4", true) ||
                url.endsWith(".m3u8", true) ||
                url.endsWith(".webm", true) ||
                url.endsWith(".mkv", true) ||
                url.endsWith(".avi", true) ||
                url.endsWith(".mov", true) ||
                url.endsWith(".flv", true) ||
                url.contains("youtube.com") ||
                url.contains("youtu.be") ||
                url.contains("vimeo.com")
    }

    private fun showVideoDetectedDialog() {
        if (detectedVideoUrls.isEmpty()) return

        val message =
                if (detectedVideoUrls.size == 1) {
                    "检测到1个视频，是否立即投屏？"
                } else {
                    "检测到${detectedVideoUrls.size}个视频，是否选择投屏？"
                }

        AlertDialog.Builder(this)
                .setTitle("发现视频")
                .setMessage(message)
                .setPositiveButton("投屏") { _, _ -> showVideoSelectionDialog() }
                .setNegativeButton("稍后") { dialog, _ ->
                    dialog.dismiss()
                    Toast.makeText(this, "可点击投屏按钮随时投屏", Toast.LENGTH_SHORT).show()
                }
                .setCancelable(true)
                .show()
    }

    private fun showVideoSelectionDialog() {
        if (detectedVideoUrls.isEmpty()) {
            Toast.makeText(this, getString(R.string.no_video_found), Toast.LENGTH_SHORT).show()
            return
        }

        if (availableDevices.isEmpty()) {
            Toast.makeText(this, getString(R.string.no_cast_devices), Toast.LENGTH_SHORT).show()
            return
        }

        val videoArray = detectedVideoUrls.toTypedArray()
        val displayNames =
                videoArray
                        .map { url ->
                            // Extract filename or use last part of URL for display
                            url.substringAfterLast('/').substringBefore('?').takeIf {
                                it.isNotEmpty()
                            }
                                    ?: url
                        }
                        .toTypedArray()

        AlertDialog.Builder(this)
                .setTitle(getString(R.string.select_cast_device))
                .setItems(displayNames) { _, which ->
                    val selectedVideoUrl = videoArray[which]
                    showDeviceSelectionDialog(selectedVideoUrl)
                }
                .setNegativeButton(getString(R.string.cancel), null)
                .show()
    }

    private fun showDeviceSelectionDialog(videoUrl: String) {
        // 获取所有可用设备
        val allDevices = castManager.getAvailableDevices()

        if (allDevices.isEmpty()) {
            Toast.makeText(this, "正在搜索投屏设备...", Toast.LENGTH_SHORT).show()
            return
        }

        val deviceNames = allDevices.map { "${it.name} (${it.type.name})" }.toTypedArray()

        AlertDialog.Builder(this)
                .setTitle("选择投屏设备")
                .setItems(deviceNames) { _, which ->
                    val selectedDevice = allDevices[which]
                    castVideoToDevice(videoUrl, selectedDevice)
                }
                .setNegativeButton(getString(R.string.cancel), null)
                .show()
    }

    private fun castVideoToDevice(videoUrl: String, device: Device) {
        try {
            // 使用统一的CastManager进行投屏
            castManager.cast(videoUrl, device)

            val deviceTypeName =
                    when (device.type) {
                        DeviceType.CHROMECAST -> "Chromecast"
                        DeviceType.DLNA -> "DLNA"
                        DeviceType.MIRACAST -> "Miracast"
                    }

            Toast.makeText(this, "正在通过 $deviceTypeName 投屏到 ${device.name}", Toast.LENGTH_SHORT)
                    .show()
        } catch (e: Exception) {
            Log.e("BrowserActivity", "Cast failed", e)
            Toast.makeText(this, "投屏失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
}
