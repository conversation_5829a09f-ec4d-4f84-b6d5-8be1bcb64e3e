package com.webvideocaster.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.webvideocaster.R
import com.webvideocaster.cast.CastManager
import com.webvideocaster.cast.ChromecastManager
import com.webvideocaster.media.VideoParser
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType

class BrowserActivity : AppCompatActivity() {

    private lateinit var webView: WebView
    private lateinit var urlEditText: EditText
    private lateinit var goButton: Button
    private lateinit var castButton: ImageButton
    private lateinit var castManager: CastManager
    private lateinit var chromecastManager: ChromecastManager
    private lateinit var videoParser: VideoParser

    private val detectedVideoUrls = mutableSetOf<String>()
    private var availableDevices: List<Device> = listOf()
    private var currentPageUrl: String = ""

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_browser)

        webView = findViewById(R.id.web_view)
        urlEditText = findViewById(R.id.url_edit_text)
        goButton = findViewById(R.id.go_button)
        castButton = findViewById(R.id.cast_button)

        // Initialize Cast Manager and Video Parser
        castManager = CastManager(this)
        chromecastManager = ChromecastManager(this)
        videoParser = VideoParser()
        chromecastManager.setOnDevicesUpdatedListener { devices ->
            availableDevices = devices
            updateCastButtonState()
        }

        val settings = webView.settings
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.databaseEnabled = true
        settings.setSupportZoom(true)
        settings.useWideViewPort = true
        settings.loadWithOverviewMode = true
        settings.userAgentString =
                "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"

        webView.webViewClient =
                object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        url?.let {
                            currentPageUrl = it
                            // Parse the page content for video URLs
                            view?.evaluateJavascript("document.documentElement.outerHTML") { html ->
                                if (html != null && html != "null") {
                                    val cleanHtml =
                                            html.replace("\\\"", "\"").removeSurrounding("\"")
                                    videoParser.parseVideoUrl(cleanHtml) { videoUrl ->
                                        runOnUiThread {
                                            if (detectedVideoUrls.add(videoUrl)) {
                                                updateCastButtonState()
                                                Toast.makeText(
                                                                this@BrowserActivity,
                                                                getString(R.string.video_found),
                                                                Toast.LENGTH_SHORT
                                                        )
                                                        .show()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    override fun shouldInterceptRequest(
                            view: WebView?,
                            request: WebResourceRequest?
                    ): WebResourceResponse? {
                        val url = request?.url.toString()
                        if (isVideoUrl(url) && detectedVideoUrls.add(url)) {
                            runOnUiThread { updateCastButtonState() }
                        }
                        return super.shouldInterceptRequest(view, request)
                    }
                }

        goButton.setOnClickListener {
            var url = urlEditText.text.toString()
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                url = "https://$url"
            }
            webView.loadUrl(url)
        }

        // Load a default page
        webView.loadUrl("https://www.google.com")

        castButton.setOnClickListener { showVideoSelectionDialog() }

        // Initialize cast button state
        updateCastButtonState()
    }

    override fun onResume() {
        super.onResume()
        chromecastManager.startDeviceDiscovery()
    }

    override fun onPause() {
        super.onPause()
        chromecastManager.stopDeviceDiscovery()
    }

    private fun updateCastButtonState() {
        castButton.isEnabled = detectedVideoUrls.isNotEmpty() && availableDevices.isNotEmpty()
        castButton.alpha = if (castButton.isEnabled) 1.0f else 0.5f
    }

    private fun isVideoUrl(url: String): Boolean {
        return url.endsWith(".mp4", true) ||
                url.endsWith(".m3u8", true) ||
                url.endsWith(".webm", true) ||
                url.endsWith(".mkv", true) ||
                url.endsWith(".avi", true) ||
                url.endsWith(".mov", true) ||
                url.endsWith(".flv", true) ||
                url.contains("youtube.com") ||
                url.contains("youtu.be") ||
                url.contains("vimeo.com")
    }

    private fun showVideoSelectionDialog() {
        if (detectedVideoUrls.isEmpty()) {
            Toast.makeText(this, getString(R.string.no_video_found), Toast.LENGTH_SHORT).show()
            return
        }

        if (availableDevices.isEmpty()) {
            Toast.makeText(this, getString(R.string.no_cast_devices), Toast.LENGTH_SHORT).show()
            return
        }

        val videoArray = detectedVideoUrls.toTypedArray()
        val displayNames =
                videoArray
                        .map { url ->
                            // Extract filename or use last part of URL for display
                            url.substringAfterLast('/').substringBefore('?').takeIf {
                                it.isNotEmpty()
                            }
                                    ?: url
                        }
                        .toTypedArray()

        AlertDialog.Builder(this)
                .setTitle(getString(R.string.select_cast_device))
                .setItems(displayNames) { _, which ->
                    val selectedVideoUrl = videoArray[which]
                    showDeviceSelectionDialog(selectedVideoUrl)
                }
                .setNegativeButton(getString(R.string.cancel), null)
                .show()
    }

    private fun showDeviceSelectionDialog(videoUrl: String) {
        val deviceNames = availableDevices.map { "${it.name} (${it.type.name})" }.toTypedArray()

        AlertDialog.Builder(this)
                .setTitle(getString(R.string.select_cast_device))
                .setItems(deviceNames) { _, which ->
                    val selectedDevice = availableDevices[which]
                    castVideoToDevice(videoUrl, selectedDevice)
                }
                .setNegativeButton(getString(R.string.cancel), null)
                .show()
    }

    private fun castVideoToDevice(videoUrl: String, device: Device) {
        try {
            when (device.type) {
                DeviceType.CHROMECAST -> {
                    chromecastManager.castVideo(videoUrl, "Web Video", "Cast from $currentPageUrl")
                    Toast.makeText(
                                    this,
                                    getString(R.string.casting_to, device.name),
                                    Toast.LENGTH_SHORT
                            )
                            .show()
                }
                DeviceType.DLNA -> {
                    // TODO: Implement DLNA casting
                    Toast.makeText(this, "DLNA casting not yet implemented", Toast.LENGTH_SHORT)
                            .show()
                }
                DeviceType.MIRACAST -> {
                    // TODO: Implement Miracast
                    Toast.makeText(this, "Miracast not yet implemented", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            Toast.makeText(this, getString(R.string.cast_failed), Toast.LENGTH_LONG).show()
        }
    }
}
