package com.webvideocaster.cast

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder

// Simplified UPnP service for future Jupiter UPnP implementation

class UpnpService : Service() {

    private val binder = LocalBinder()

    inner class LocalBinder : Binder() {
        val service: UpnpService
            get() = this@UpnpService
    }

    override fun onBind(intent: Intent?): IBinder? {
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        // TODO: Initialize Jupiter UPnP service
    }

    override fun onDestroy() {
        super.onDestroy()
        // TODO: Shutdown Jupiter UPnP service
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }
}
