package com.webvideocaster.cast

import android.content.Context
import android.util.Log
import androidx.mediarouter.media.MediaRouteSelector
import androidx.mediarouter.media.MediaRouter
import com.google.android.gms.cast.CastMediaControlIntent
import com.google.android.gms.cast.MediaInfo
import com.google.android.gms.cast.MediaLoadRequestData
import com.google.android.gms.cast.MediaMetadata
import com.google.android.gms.cast.framework.CastContext
import com.google.android.gms.cast.framework.CastSession
import com.google.android.gms.cast.framework.SessionManagerListener
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType

class ChromecastManager(private val context: Context) {

    private val mediaRouter = MediaRouter.getInstance(context)
    private val castContext by lazy { CastContext.getSharedInstance(context) }
    private var castSession: CastSession? = null
    private val sessionManagerListener: SessionManagerListener<CastSession> =
            MySessionManagerListener()

    private val mediaRouteSelector =
            MediaRouteSelector.Builder()
                    .addControlCategory(CastMediaControlIntent.categoryForCast("CC1AD845"))
                    .build()

    private val availableDevices = mutableListOf<Device>()

    private val mediaRouterCallback =
            object : MediaRouter.Callback() {
                override fun onRouteAdded(router: MediaRouter, route: MediaRouter.RouteInfo) {
                    val device = Device(route.id, route.name, DeviceType.CHROMECAST)
                    if (!availableDevices.any { it.id == device.id }) {
                        availableDevices.add(device)
                        onDevicesUpdatedListener?.invoke(availableDevices.toList())
                    }
                }

                override fun onRouteRemoved(router: MediaRouter, route: MediaRouter.RouteInfo) {
                    availableDevices.removeAll { it.id == route.id }
                    onDevicesUpdatedListener?.invoke(availableDevices.toList())
                }
            }

    private var onDevicesUpdatedListener: ((List<Device>) -> Unit)? = null

    fun setOnDevicesUpdatedListener(listener: (List<Device>) -> Unit) {
        this.onDevicesUpdatedListener = listener
    }

    fun startDeviceDiscovery() {
        castContext.sessionManager.addSessionManagerListener(
                sessionManagerListener,
                CastSession::class.java
        )
        mediaRouter.addCallback(
                mediaRouteSelector,
                mediaRouterCallback,
                MediaRouter.CALLBACK_FLAG_PERFORM_ACTIVE_SCAN
        )
    }

    fun stopDeviceDiscovery() {
        castContext.sessionManager.removeSessionManagerListener(
                sessionManagerListener,
                CastSession::class.java
        )
        mediaRouter.removeCallback(mediaRouterCallback)
    }

    fun cast(url: String, deviceId: String) {
        Log.d("ChromecastManager", "Attempting to cast $url to $deviceId")
        val route = mediaRouter.routes.find { it.id == deviceId }
        if (route != null) {
            mediaRouter.selectRoute(route)
            // The actual casting will be handled by the session listener once a session is
            // established.
            // We can store the URL to be casted.
            castUrlAfterSessionStart = url
        } else {
            Log.e("ChromecastManager", "Device not found: $deviceId")
        }
    }

    private var castUrlAfterSessionStart: String? = null

    private inner class MySessionManagerListener : SessionManagerListener<CastSession> {
        override fun onSessionStarted(session: CastSession, sessionId: String) {
            Log.d("ChromecastManager", "Session started")
            castSession = session
            castUrlAfterSessionStart?.let { url ->
                loadMedia(url)
                castUrlAfterSessionStart = null
            }
        }

        override fun onSessionResumed(session: CastSession, wasSuspended: Boolean) {
            Log.d("ChromecastManager", "Session resumed")
            castSession = session
        }

        override fun onSessionEnded(session: CastSession, error: Int) {
            Log.d("ChromecastManager", "Session ended")
            castSession = null
        }

        override fun onSessionSuspended(session: CastSession, reason: Int) {
            Log.d("ChromecastManager", "Session suspended")
            castSession = null
        }

        override fun onSessionEnding(session: CastSession) {
            Log.d("ChromecastManager", "Session ending")
            castSession = null
        }

        // Other session callbacks...
        override fun onSessionStarting(session: CastSession) {}
        override fun onSessionStartFailed(session: CastSession, error: Int) {}
        override fun onSessionResuming(session: CastSession, sessionId: String) {}
        override fun onSessionResumeFailed(session: CastSession, error: Int) {}
    }

    private fun loadMedia(mediaUrl: String) {
        val remoteMediaClient =
                castSession?.remoteMediaClient
                        ?: run {
                            Log.e("ChromecastManager", "RemoteMediaClient is not available.")
                            return
                        }

        val mediaMetadata = MediaMetadata(MediaMetadata.MEDIA_TYPE_MOVIE)
        mediaMetadata.putString(MediaMetadata.KEY_TITLE, "Casting Video")

        val mediaInfo =
                MediaInfo.Builder(mediaUrl)
                        .setStreamType(MediaInfo.STREAM_TYPE_BUFFERED)
                        .setContentType("video/mp4") // You might need to determine this dynamically
                        .setMetadata(mediaMetadata)
                        .build()

        val mediaLoadRequestData =
                MediaLoadRequestData.Builder().setMediaInfo(mediaInfo).setAutoplay(true).build()

        remoteMediaClient.load(mediaLoadRequestData)
    }

    // Methods required by CastManager
    fun initialize() {
        Log.d("ChromecastManager", "Initializing ChromecastManager")
        startDeviceDiscovery()
    }

    fun cleanup() {
        Log.d("ChromecastManager", "Cleaning up ChromecastManager")
        stopDeviceDiscovery()
        castSession = null
    }

    fun getAvailableDevices(): List<Device> {
        return availableDevices.toList()
    }

    fun isAvailable(): Boolean {
        return availableDevices.isNotEmpty()
    }

    fun castVideo(url: String, title: String, description: String) {
        Log.d("ChromecastManager", "Casting video: $url")

        val remoteMediaClient = castSession?.remoteMediaClient
        if (remoteMediaClient != null) {
            // Already have a session, cast directly
            loadMediaWithDetails(url, title, description)
        } else {
            // Need to establish session first
            castUrlAfterSessionStart = url
            // Try to connect to the first available device
            if (availableDevices.isNotEmpty()) {
                cast(url, availableDevices.first().id)
            } else {
                Log.e("ChromecastManager", "No Chromecast devices available")
            }
        }
    }

    fun stopCasting() {
        Log.d("ChromecastManager", "Stopping casting")
        castSession?.remoteMediaClient?.stop()
        castContext.sessionManager.endCurrentSession(true)
    }

    fun isConnected(): Boolean {
        return castSession?.isConnected == true
    }

    private fun loadMediaWithDetails(mediaUrl: String, title: String, description: String) {
        val remoteMediaClient =
                castSession?.remoteMediaClient
                        ?: run {
                            Log.e("ChromecastManager", "RemoteMediaClient is not available.")
                            return
                        }

        val mediaMetadata = MediaMetadata(MediaMetadata.MEDIA_TYPE_MOVIE)
        mediaMetadata.putString(MediaMetadata.KEY_TITLE, title)
        mediaMetadata.putString(MediaMetadata.KEY_SUBTITLE, description)

        val mediaInfo =
                MediaInfo.Builder(mediaUrl)
                        .setStreamType(MediaInfo.STREAM_TYPE_BUFFERED)
                        .setContentType("video/mp4") // You might need to determine this dynamically
                        .setMetadata(mediaMetadata)
                        .build()

        val mediaLoadRequestData =
                MediaLoadRequestData.Builder().setMediaInfo(mediaInfo).setAutoplay(true).build()

        remoteMediaClient.load(mediaLoadRequestData)
    }
}
