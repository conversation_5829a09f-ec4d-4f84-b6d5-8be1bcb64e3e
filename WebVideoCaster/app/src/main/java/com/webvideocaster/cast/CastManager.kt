package com.webvideocaster.cast

import android.content.Context
import android.content.Intent
import android.media.MediaRouter
import android.util.Log
import android.widget.Toast
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType

class CastManager(private val context: Context) {

    companion object {
        private const val TAG = "CastManager"
    }

    private var chromecastManager: ChromecastManager? = null
    private var dlnaManager: DLNAManager? = null
    private var selectedDevice: Device? = null

    fun initialize() {
        Log.d(TAG, "Initializing CastManager")

        // Initialize Chromecast
        try {
            chromecastManager = ChromecastManager(context)
            chromecastManager?.initialize()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Chromecast", e)
        }

        // Initialize DLNA
        try {
            dlnaManager = DLNAManager(context)
            dlnaManager?.bindUpnpService()
            dlnaManager?.setOnDevicesUpdatedListener { devices ->
                Log.d(TAG, "DLNA devices updated: ${devices.size} devices")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize DLNA", e)
        }
    }

    fun cleanup() {
        Log.d(TAG, "Cleaning up CastManager")

        chromecastManager?.cleanup()
        dlnaManager?.unbindUpnpService()

        chromecastManager = null
        dlnaManager = null
        selectedDevice = null
    }

    fun cast(url: String, device: Device? = null) {
        Log.d(TAG, "Casting URL: $url")

        if (url.isBlank()) {
            showError("Invalid URL")
            return
        }

        val targetDevice = device ?: selectedDevice

        if (targetDevice == null) {
            // Auto-select best available device
            when {
                isChromecastAvailable() -> castViaChromecast(url)
                isDLNAAvailable() -> castViaDLNA(url)
                isMiracastAvailable() -> startSystemMiracast()
                else -> showError("No casting devices available")
            }
        } else {
            // Cast to specific device
            when (targetDevice.type) {
                DeviceType.CHROMECAST -> castViaChromecast(url)
                DeviceType.DLNA -> castViaDLNA(url, targetDevice)
                DeviceType.MIRACAST -> startSystemMiracast()
            }
        }
    }

    fun setSelectedDevice(device: Device?) {
        selectedDevice = device
        Log.d(TAG, "Selected device: ${device?.name ?: "None"}")
    }

    fun getAvailableDevices(): List<Device> {
        val devices = mutableListOf<Device>()

        // Add Chromecast devices
        chromecastManager?.getAvailableDevices()?.let { devices.addAll(it) }

        // Add DLNA devices
        dlnaManager?.getAvailableDevices()?.let { devices.addAll(it) }

        return devices
    }

    private fun isChromecastAvailable(): Boolean {
        return chromecastManager?.isAvailable() == true
    }

    private fun isMiracastAvailable(): Boolean {
        // Check if system supports Miracast/Wireless Display
        val mediaRouter = context.getSystemService(Context.MEDIA_ROUTER_SERVICE) as? MediaRouter
        return mediaRouter?.getRouteCount() ?: 0 > 1
    }

    private fun isDLNAAvailable(): Boolean {
        return dlnaManager?.isAvailable() == true
    }

    private fun castViaChromecast(url: String) {
        Log.d(TAG, "Casting via Chromecast: $url")

        chromecastManager?.let { manager ->
            try {
                manager.castVideo(url, "Video", "Cast from WebVideoCaster")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to cast via Chromecast", e)
                showError("Failed to cast via Chromecast: ${e.message}")
            }
        }
                ?: showError("Chromecast not available")
    }

    private fun startSystemMiracast() {
        Log.d(TAG, "Starting system Miracast")

        try {
            // Open system wireless display settings
            val intent = Intent("android.settings.CAST_SETTINGS")
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start Miracast", e)
            showError("Miracast not supported on this device")
        }
    }

    private fun castViaDLNA(url: String, device: Device? = null) {
        Log.d(TAG, "Casting via DLNA: $url")

        dlnaManager?.let { manager ->
            try {
                val targetDevice = device ?: manager.getAvailableDevices().firstOrNull()
                if (targetDevice != null) {
                    manager.cast(url, targetDevice.id)
                    Log.d(TAG, "DLNA cast initiated to ${targetDevice.name}")
                } else {
                    showError("No DLNA devices available")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to cast via DLNA", e)
                showError("Failed to cast via DLNA: ${e.message}")
            }
        }
                ?: showError("DLNA not available")
    }

    private fun showError(message: String) {
        Log.e(TAG, "Error: $message")
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    fun stopCasting() {
        Log.d(TAG, "Stopping casting")

        chromecastManager?.stopCasting()
        dlnaManager?.stopCasting()
    }

    fun isConnected(): Boolean {
        return chromecastManager?.isConnected() == true || dlnaManager?.isConnected() == true
    }
}
