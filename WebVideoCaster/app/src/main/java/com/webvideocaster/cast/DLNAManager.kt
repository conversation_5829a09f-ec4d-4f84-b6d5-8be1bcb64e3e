package com.webvideocaster.cast

import android.content.Context
import android.util.Log
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType

// Note: Jupiter UPnP has different API structure
// For now, we'll implement a simplified version that can be extended later

class DLNAManager(private val context: Context) {

    private val availableDevices = mutableListOf<Device>()
    private var onDevicesUpdatedListener: ((List<Device>) -> Unit)? = null
    private var isServiceBound = false

    fun bindUpnpService() {
        // TODO: Implement Jupiter UPnP service binding
        Log.d(TAG, "DLNA service binding - to be implemented with Jupiter UPnP")
        isServiceBound = true

        // For now, add a mock device for testing
        val mockDevice = Device("dlna-mock-1", "Mock DLNA Device", DeviceType.DLNA)
        availableDevices.add(mockDevice)
        onDevicesUpdatedListener?.invoke(availableDevices.toList())
    }

    fun unbindUpnpService() {
        if (isServiceBound) {
            Log.d(TAG, "DLNA service unbinding")
            isServiceBound = false
            availableDevices.clear()
            onDevicesUpdatedListener?.invoke(availableDevices.toList())
        }
    }

    fun setOnDevicesUpdatedListener(listener: (List<Device>) -> Unit) {
        this.onDevicesUpdatedListener = listener
    }

    fun cast(url: String, deviceId: String) {
        // TODO: Implement DLNA casting
        Log.d(TAG, "Casting $url to $deviceId (DLNA)")
    }

    companion object {
        private const val TAG = "DLNAManager"
    }
}
