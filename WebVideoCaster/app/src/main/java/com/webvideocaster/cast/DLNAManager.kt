package com.webvideocaster.cast

import android.content.Context
import android.util.Log
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType
import java.net.*
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.*

// DLNA Manager with basic UPnP device discovery
// This is a simplified implementation that can be extended with Jupiter UPnP later

class DLNAManager(private val context: Context) {

    private val availableDevices = ConcurrentHashMap<String, Device>()
    private var onDevicesUpdatedListener: ((List<Device>) -> Unit)? = null
    private var isServiceBound = false
    private var discoveryJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    fun bindUpnpService() {
        Log.d(TAG, "Starting DLNA service and device discovery")
        isServiceBound = true
        startDeviceDiscovery()
    }

    fun unbindUpnpService() {
        if (isServiceBound) {
            Log.d(TAG, "DLNA service unbinding")
            isServiceBound = false
            stopDeviceDiscovery()
            availableDevices.clear()
            notifyDevicesUpdated()
        }
    }

    private fun startDeviceDiscovery() {
        discoveryJob?.cancel()
        discoveryJob =
                scope.launch {
                    try {
                        discoverUPnPDevices()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during device discovery", e)
                    }
                }
    }

    private fun stopDeviceDiscovery() {
        discoveryJob?.cancel()
        discoveryJob = null
    }

    private suspend fun discoverUPnPDevices() {
        Log.d(TAG, "Starting UPnP device discovery")

        try {
            // UPnP SSDP multicast discovery
            val multicastAddress = InetAddress.getByName("***************")
            val port = 1900

            val socket = MulticastSocket()
            socket.joinGroup(multicastAddress)
            socket.soTimeout = 5000 // 5 second timeout

            // Send M-SEARCH request
            val searchMessage = buildMSearchMessage()
            val searchPacket =
                    DatagramPacket(
                            searchMessage.toByteArray(),
                            searchMessage.length,
                            multicastAddress,
                            port
                    )

            socket.send(searchPacket)
            Log.d(TAG, "Sent UPnP M-SEARCH request")

            // Listen for responses
            val buffer = ByteArray(1024)
            val startTime = System.currentTimeMillis()

            while (System.currentTimeMillis() - startTime < 10000) { // 10 seconds discovery
                try {
                    val packet = DatagramPacket(buffer, buffer.size)
                    socket.receive(packet)

                    val response = String(packet.data, 0, packet.length)
                    parseUPnPResponse(response, packet.address)
                } catch (e: SocketTimeoutException) {
                    // Continue listening
                } catch (e: Exception) {
                    Log.e(TAG, "Error receiving UPnP response", e)
                }
            }

            socket.leaveGroup(multicastAddress)
            socket.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error in UPnP discovery", e)
        }

        // Add a mock device for testing when no real devices found
        if (availableDevices.isEmpty()) {
            val mockDevice = Device("dlna-mock-1", "Mock DLNA Device", DeviceType.DLNA)
            availableDevices[mockDevice.id] = mockDevice
            notifyDevicesUpdated()
        }
    }

    private fun buildMSearchMessage(): String {
        return "M-SEARCH * HTTP/1.1\r\n" +
                "HOST: ***************:1900\r\n" +
                "MAN: \"ssdp:discover\"\r\n" +
                "ST: upnp:rootdevice\r\n" +
                "MX: 3\r\n\r\n"
    }

    private fun parseUPnPResponse(response: String, address: InetAddress) {
        if (response.contains("HTTP/1.1 200 OK") &&
                        (response.contains("MediaRenderer") || response.contains("MediaServer"))
        ) {

            val deviceId = "dlna-${address.hostAddress}"
            val deviceName = extractDeviceName(response) ?: "DLNA Device (${address.hostAddress})"

            val device = Device(deviceId, deviceName, DeviceType.DLNA)

            if (!availableDevices.containsKey(deviceId)) {
                availableDevices[deviceId] = device
                Log.d(TAG, "Found DLNA device: $deviceName at ${address.hostAddress}")
                notifyDevicesUpdated()
            }
        }
    }

    private fun extractDeviceName(response: String): String? {
        // Try to extract device name from UPnP response
        // This is a simplified extraction - real implementation would parse XML
        val lines = response.split("\r\n")
        for (line in lines) {
            if (line.startsWith("SERVER:") || line.startsWith("User-Agent:")) {
                return line.substringAfter(":").trim()
            }
        }
        return null
    }

    private fun notifyDevicesUpdated() {
        scope.launch(Dispatchers.Main) {
            onDevicesUpdatedListener?.invoke(availableDevices.values.toList())
        }
    }

    fun setOnDevicesUpdatedListener(listener: (List<Device>) -> Unit) {
        this.onDevicesUpdatedListener = listener
    }

    fun getAvailableDevices(): List<Device> {
        return availableDevices.values.toList()
    }

    fun isAvailable(): Boolean {
        return availableDevices.isNotEmpty()
    }

    fun cast(url: String, deviceId: String) {
        Log.d(TAG, "Casting $url to $deviceId (DLNA)")

        scope.launch {
            try {
                // Simplified DLNA casting - in real implementation this would use UPnP control
                // points
                Log.d(TAG, "Starting DLNA cast to device: $deviceId")
                Log.d(TAG, "Video URL: $url")

                // For now, just log the casting attempt
                // Real implementation would:
                // 1. Get device description XML
                // 2. Find MediaRenderer service
                // 3. Use SetAVTransportURI and Play actions

                withContext(Dispatchers.Main) {
                    // Notify success (mock)
                    Log.d(TAG, "DLNA cast initiated successfully")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error casting via DLNA", e)
            }
        }
    }

    fun stopCasting() {
        Log.d(TAG, "Stopping DLNA casting")
        // TODO: Implement stop casting
    }

    fun isConnected(): Boolean {
        // TODO: Implement connection status
        return false
    }

    companion object {
        private const val TAG = "DLNAManager"
    }
}
