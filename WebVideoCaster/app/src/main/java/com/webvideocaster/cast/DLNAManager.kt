package com.webvideocaster.cast

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import com.webvideocaster.model.Device
import com.webvideocaster.model.DeviceType
import java.net.*
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.*

// DLNA Manager with basic UPnP device discovery
// This is a simplified implementation that can be extended with Jupiter UPnP later

class DLNAManager(private val context: Context) {

    private val availableDevices = ConcurrentHashMap<String, Device>()
    private var onDevicesUpdatedListener: ((List<Device>) -> Unit)? = null
    private var isServiceBound = false
    private var discoveryJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var multicastLock: WifiManager.MulticastLock? = null
    // Network debug helper will be created inline to avoid import issues

    fun bindUpnpService() {
        Log.d(TAG, "Starting DLNA service and device discovery")
        isServiceBound = true
        startDeviceDiscovery()
    }

    fun unbindUpnpService() {
        if (isServiceBound) {
            Log.d(TAG, "DLNA service unbinding")
            isServiceBound = false
            stopDeviceDiscovery()
            availableDevices.clear()
            notifyDevicesUpdated()
        }
    }

    private fun startDeviceDiscovery() {
        discoveryJob?.cancel()
        discoveryJob =
                scope.launch {
                    try {
                        discoverUPnPDevices()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during device discovery", e)
                    }
                }
    }

    private fun stopDeviceDiscovery() {
        discoveryJob?.cancel()
        discoveryJob = null
    }

    private suspend fun discoverUPnPDevices() {
        Log.d(TAG, "Starting UPnP device discovery")

        // Log network debug info
        logNetworkInfo()

        // Acquire multicast lock
        acquireMulticastLock()

        try {
            // UPnP SSDP multicast discovery
            val multicastAddress = InetAddress.getByName("***************")
            val port = 1900

            val socket = MulticastSocket(port)
            socket.joinGroup(multicastAddress)
            socket.soTimeout = 3000 // 3 second timeout per request

            // Send multiple M-SEARCH requests for different device types
            val searchMessages = buildMSearchMessages()

            for (searchMessage in searchMessages) {
                val searchPacket =
                        DatagramPacket(
                                searchMessage.toByteArray(),
                                searchMessage.length,
                                multicastAddress,
                                port
                        )

                socket.send(searchPacket)
                Log.d(TAG, "Sent UPnP M-SEARCH request: ${searchMessage.lines().first()}")

                // Wait a bit between requests
                delay(500)
            }

            // Listen for responses for a longer period
            val buffer = ByteArray(2048) // Increased buffer size
            val startTime = System.currentTimeMillis()

            while (System.currentTimeMillis() - startTime < 15000) { // 15 seconds discovery
                try {
                    val packet = DatagramPacket(buffer, buffer.size)
                    socket.receive(packet)

                    val response = String(packet.data, 0, packet.length)
                    Log.d(TAG, "Received UPnP response from ${packet.address.hostAddress}")
                    Log.v(TAG, "Response content: $response")

                    parseUPnPResponse(response, packet.address)
                } catch (e: SocketTimeoutException) {
                    // Continue listening - this is normal
                } catch (e: Exception) {
                    Log.e(TAG, "Error receiving UPnP response", e)
                }
            }

            socket.leaveGroup(multicastAddress)
            socket.close()

            Log.d(TAG, "UPnP discovery completed. Found ${availableDevices.size} devices")
        } catch (e: Exception) {
            Log.e(TAG, "Error in UPnP discovery", e)
        } finally {
            // Release multicast lock
            releaseMulticastLock()
        }

        // Add a mock device for testing when no real devices found
        if (availableDevices.isEmpty()) {
            Log.d(TAG, "No real devices found, adding mock device for testing")
            val mockDevice = Device("dlna-mock-1", "Mock DLNA Device (小米电视模拟)", DeviceType.DLNA)
            availableDevices[mockDevice.id] = mockDevice
            notifyDevicesUpdated()
        }
    }

    private fun buildMSearchMessages(): List<String> {
        return listOf(
                // Search for all UPnP devices
                "M-SEARCH * HTTP/1.1\r\n" +
                        "HOST: ***************:1900\r\n" +
                        "MAN: \"ssdp:discover\"\r\n" +
                        "ST: upnp:rootdevice\r\n" +
                        "MX: 3\r\n\r\n",

                // Search specifically for MediaRenderer devices (DLNA players)
                "M-SEARCH * HTTP/1.1\r\n" +
                        "HOST: ***************:1900\r\n" +
                        "MAN: \"ssdp:discover\"\r\n" +
                        "ST: urn:schemas-upnp-org:device:MediaRenderer:1\r\n" +
                        "MX: 3\r\n\r\n",

                // Search for MediaServer devices (might also support rendering)
                "M-SEARCH * HTTP/1.1\r\n" +
                        "HOST: ***************:1900\r\n" +
                        "MAN: \"ssdp:discover\"\r\n" +
                        "ST: urn:schemas-upnp-org:device:MediaServer:1\r\n" +
                        "MX: 3\r\n\r\n",

                // Search for all SSDP devices
                "M-SEARCH * HTTP/1.1\r\n" +
                        "HOST: ***************:1900\r\n" +
                        "MAN: \"ssdp:discover\"\r\n" +
                        "ST: ssdp:all\r\n" +
                        "MX: 3\r\n\r\n"
        )
    }

    private fun parseUPnPResponse(response: String, address: InetAddress) {
        if (response.contains("HTTP/1.1 200 OK")) {
            // Check if this is a media device
            val isMediaDevice =
                    response.contains("MediaRenderer", ignoreCase = true) ||
                            response.contains("MediaServer", ignoreCase = true) ||
                            response.contains(
                                    "urn:schemas-upnp-org:device:MediaRenderer",
                                    ignoreCase = true
                            ) ||
                            response.contains(
                                    "urn:schemas-upnp-org:device:MediaServer",
                                    ignoreCase = true
                            ) ||
                            // Check for common TV/media device indicators
                            response.contains("TV", ignoreCase = true) ||
                            response.contains("Television", ignoreCase = true) ||
                            response.contains("SmartTV", ignoreCase = true) ||
                            response.contains("Android", ignoreCase = true) ||
                            response.contains("DLNA", ignoreCase = true) ||
                            // Xiaomi TV specific indicators
                            response.contains("Xiaomi", ignoreCase = true) ||
                            response.contains("Mi TV", ignoreCase = true) ||
                            response.contains("MIUI", ignoreCase = true)

            if (isMediaDevice) {
                val deviceId = "dlna-${address.hostAddress}"
                val deviceName =
                        extractDeviceName(response) ?: "DLNA Device (${address.hostAddress})"

                val device = Device(deviceId, deviceName, DeviceType.DLNA)

                if (!availableDevices.containsKey(deviceId)) {
                    availableDevices[deviceId] = device
                    Log.d(TAG, "Found DLNA device: $deviceName at ${address.hostAddress}")
                    notifyDevicesUpdated()
                }
            }
        }
    }

    private fun extractDeviceName(response: String): String? {
        // Try to extract device name from UPnP response
        val lines = response.split("\r\n")
        for (line in lines) {
            val upperLine = line.uppercase()
            when {
                upperLine.startsWith("SERVER:") -> {
                    val serverInfo = line.substringAfter(":").trim()
                    // Extract meaningful device name from server string
                    return parseServerString(serverInfo)
                }
                upperLine.startsWith("USER-AGENT:") -> {
                    val userAgent = line.substringAfter(":").trim()
                    return parseServerString(userAgent)
                }
                upperLine.startsWith("LOCATION:") -> {
                    // Could extract hostname from location URL
                    val location = line.substringAfter(":").trim()
                    val hostname = extractHostnameFromUrl(location)
                    if (hostname != null &&
                                    hostname != "localhost" &&
                                    !hostname.matches(Regex("\\d+\\.\\d+\\.\\d+\\.\\d+"))
                    ) {
                        return hostname
                    }
                }
            }
        }
        return null
    }

    private fun parseServerString(serverString: String): String? {
        // Parse server string to extract device name
        return when {
            serverString.contains("Xiaomi", ignoreCase = true) -> "小米电视"
            serverString.contains("Mi TV", ignoreCase = true) -> "小米电视"
            serverString.contains("Android", ignoreCase = true) &&
                    serverString.contains("TV", ignoreCase = true) -> "Android TV"
            serverString.contains("Samsung", ignoreCase = true) -> "三星电视"
            serverString.contains("LG", ignoreCase = true) -> "LG电视"
            serverString.contains("Sony", ignoreCase = true) -> "索尼电视"
            serverString.contains("TCL", ignoreCase = true) -> "TCL电视"
            serverString.contains("Hisense", ignoreCase = true) -> "海信电视"
            serverString.contains("TV", ignoreCase = true) -> "智能电视"
            serverString.contains("DLNA", ignoreCase = true) -> "DLNA设备"
            else -> serverString.take(30) // Limit length
        }
    }

    private fun extractHostnameFromUrl(url: String): String? {
        return try {
            val uri = java.net.URI(url)
            uri.host
        } catch (e: Exception) {
            null
        }
    }

    private fun notifyDevicesUpdated() {
        scope.launch(Dispatchers.Main) {
            onDevicesUpdatedListener?.invoke(availableDevices.values.toList())
        }
    }

    fun setOnDevicesUpdatedListener(listener: (List<Device>) -> Unit) {
        this.onDevicesUpdatedListener = listener
    }

    fun getAvailableDevices(): List<Device> {
        return availableDevices.values.toList()
    }

    fun isAvailable(): Boolean {
        return availableDevices.isNotEmpty()
    }

    fun cast(url: String, deviceId: String) {
        Log.d(TAG, "Casting $url to $deviceId (DLNA)")

        scope.launch {
            try {
                // Simplified DLNA casting - in real implementation this would use UPnP control
                // points
                Log.d(TAG, "Starting DLNA cast to device: $deviceId")
                Log.d(TAG, "Video URL: $url")

                // For now, just log the casting attempt
                // Real implementation would:
                // 1. Get device description XML
                // 2. Find MediaRenderer service
                // 3. Use SetAVTransportURI and Play actions

                withContext(Dispatchers.Main) {
                    // Notify success (mock)
                    Log.d(TAG, "DLNA cast initiated successfully")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error casting via DLNA", e)
            }
        }
    }

    fun stopCasting() {
        Log.d(TAG, "Stopping DLNA casting")
        // TODO: Implement stop casting
    }

    fun isConnected(): Boolean {
        // TODO: Implement connection status
        return false
    }

    private fun acquireMulticastLock() {
        try {
            val wifiManager =
                    context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            multicastLock = wifiManager.createMulticastLock("DLNA_Discovery")
            multicastLock?.acquire()
            Log.d(TAG, "Multicast lock acquired")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to acquire multicast lock", e)
        }
    }

    private fun releaseMulticastLock() {
        try {
            multicastLock?.let { lock ->
                if (lock.isHeld) {
                    lock.release()
                    Log.d(TAG, "Multicast lock released")
                }
            }
            multicastLock = null
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release multicast lock", e)
        }
    }

    private fun logNetworkInfo() {
        Log.d(TAG, "=== Network Debug Info ===")
        try {
            val wifiManager =
                    context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val wifiInfo = wifiManager.connectionInfo
            Log.d(TAG, "WiFi SSID: ${wifiInfo.ssid}")
            Log.d(TAG, "WiFi IP: ${formatIpAddress(wifiInfo.ipAddress)}")
            Log.d(TAG, "WiFi enabled: ${wifiManager.isWifiEnabled}")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging network info", e)
        }
        Log.d(TAG, "=== End Network Debug Info ===")
    }

    private fun formatIpAddress(ip: Int): String {
        return String.format(
                "%d.%d.%d.%d",
                ip and 0xff,
                ip shr 8 and 0xff,
                ip shr 16 and 0xff,
                ip shr 24 and 0xff
        )
    }

    companion object {
        private const val TAG = "DLNAManager"
    }
}
