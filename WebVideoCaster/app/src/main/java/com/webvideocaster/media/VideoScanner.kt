package com.webvideocaster.media

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.provider.MediaStore
import android.util.Log
import java.io.File

data class VideoFile(
        val id: Long,
        val title: String,
        val displayName: String,
        val path: String,
        val size: Long,
        val duration: Long,
        val mimeType: String,
        val dateAdded: Long,
        val dateModified: Long
)

class VideoScanner(private val context: Context) {

    companion object {
        private const val TAG = "VideoScanner"

        private val VIDEO_PROJECTION =
                arrayOf(
                        MediaStore.Video.Media._ID,
                        MediaStore.Video.Media.TITLE,
                        MediaStore.Video.Media.DISPLAY_NAME,
                        MediaStore.Video.Media.DATA,
                        MediaStore.Video.Media.SIZE,
                        MediaStore.Video.Media.DURATION,
                        MediaStore.Video.Media.MIME_TYPE,
                        MediaStore.Video.Media.DATE_ADDED,
                        MediaStore.Video.Media.DATE_MODIFIED
                )
    }

    fun scanForLocalVideos(): List<VideoFile> {
        Log.d(TAG, "Scanning for local video files...")

        val videoFiles = mutableListOf<VideoFile>()
        val contentResolver: ContentResolver = context.contentResolver

        try {
            val cursor: Cursor? =
                    contentResolver.query(
                            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                            VIDEO_PROJECTION,
                            null,
                            null,
                            "${MediaStore.Video.Media.DATE_MODIFIED} DESC"
                    )

            cursor?.use { c ->
                val idColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
                val titleColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.TITLE)
                val displayNameColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME)
                val dataColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
                val sizeColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE)
                val durationColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION)
                val mimeTypeColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.MIME_TYPE)
                val dateAddedColumn = c.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED)
                val dateModifiedColumn =
                        c.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_MODIFIED)

                while (c.moveToNext()) {
                    val id = c.getLong(idColumn)
                    val title = c.getString(titleColumn) ?: ""
                    val displayName = c.getString(displayNameColumn) ?: ""
                    val path = c.getString(dataColumn) ?: ""
                    val size = c.getLong(sizeColumn)
                    val duration = c.getLong(durationColumn)
                    val mimeType = c.getString(mimeTypeColumn) ?: ""
                    val dateAdded = c.getLong(dateAddedColumn)
                    val dateModified = c.getLong(dateModifiedColumn)

                    // Verify file exists and is accessible
                    if (path.isNotEmpty() && File(path).exists()) {
                        val videoFile =
                                VideoFile(
                                        id = id,
                                        title = title,
                                        displayName = displayName,
                                        path = path,
                                        size = size,
                                        duration = duration,
                                        mimeType = mimeType,
                                        dateAdded = dateAdded,
                                        dateModified = dateModified
                                )
                        videoFiles.add(videoFile)
                        Log.d(TAG, "Found video: $displayName ($path)")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning for video files", e)
        }

        Log.d(TAG, "Found ${videoFiles.size} video files")
        return videoFiles
    }

    fun scanDirectoryForVideos(directoryPath: String): List<VideoFile> {
        Log.d(TAG, "Scanning directory for videos: $directoryPath")

        val videoFiles = mutableListOf<VideoFile>()
        val directory = File(directoryPath)

        if (!directory.exists() || !directory.isDirectory) {
            Log.w(TAG, "Directory does not exist or is not a directory: $directoryPath")
            return videoFiles
        }

        try {
            val files = directory.listFiles { file -> file.isFile && isVideoFile(file.name) }

            files?.forEach { file ->
                val videoFile =
                        VideoFile(
                                id = file.hashCode().toLong(),
                                title = file.nameWithoutExtension,
                                displayName = file.name,
                                path = file.absolutePath,
                                size = file.length(),
                                duration = 0, // Cannot determine duration without
                                // MediaMetadataRetriever
                                mimeType = getMimeTypeFromExtension(file.extension),
                                dateAdded = file.lastModified() / 1000,
                                dateModified = file.lastModified() / 1000
                        )
                videoFiles.add(videoFile)
                Log.d(TAG, "Found video in directory: ${file.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning directory: $directoryPath", e)
        }

        Log.d(TAG, "Found ${videoFiles.size} video files in directory")
        return videoFiles
    }

    private fun isVideoFile(fileName: String): Boolean {
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return extension in
                arrayOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ogv")
    }

    private fun getMimeTypeFromExtension(extension: String): String {
        return when (extension.lowercase()) {
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "wmv" -> "video/x-ms-wmv"
            "flv" -> "video/x-flv"
            "webm" -> "video/webm"
            "m4v" -> "video/x-m4v"
            "3gp" -> "video/3gpp"
            "ogv" -> "video/ogg"
            else -> "video/*"
        }
    }

    fun formatFileSize(bytes: Long): String {
        val kb = 1024
        val mb = kb * 1024
        val gb = mb * 1024

        return when {
            bytes >= gb -> String.format("%.1f GB", bytes.toDouble() / gb)
            bytes >= mb -> String.format("%.1f MB", bytes.toDouble() / mb)
            bytes >= kb -> String.format("%.1f KB", bytes.toDouble() / kb)
            else -> "$bytes B"
        }
    }

    fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60

        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            else -> String.format("%d:%02d", minutes, seconds % 60)
        }
    }
}
