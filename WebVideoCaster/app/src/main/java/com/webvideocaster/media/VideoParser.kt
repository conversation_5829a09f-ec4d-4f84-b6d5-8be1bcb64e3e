package com.webvideocaster.media

import android.util.Log
import java.util.regex.Pattern

class VideoParser {

    companion object {
        private const val TAG = "VideoParser"

        // Common video file extensions
        private val VIDEO_EXTENSIONS =
                arrayOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ogv")

        // Video URL patterns
        private val VIDEO_URL_PATTERNS =
                arrayOf(
                        // Direct video file URLs
                        "https?://[^\\s]+\\.(?:${VIDEO_EXTENSIONS.joinToString("|")})(?:\\?[^\\s]*)?",

                        // YouTube patterns
                        "https?://(?:www\\.)?youtube\\.com/watch\\?v=([a-zA-Z0-9_-]+)",
                        "https?://youtu\\.be/([a-zA-Z0-9_-]+)",

                        // Vimeo patterns
                        "https?://(?:www\\.)?vimeo\\.com/(\\d+)",

                        // Dailymotion patterns
                        "https?://(?:www\\.)?dailymotion\\.com/video/([a-zA-Z0-9]+)",

                        // Generic video streaming patterns
                        "https?://[^\\s]+/[^\\s]*\\.m3u8(?:\\?[^\\s]*)?", // HLS streams
                        "https?://[^\\s]+/[^\\s]*\\.mpd(?:\\?[^\\s]*)?", // DASH streams
                )
    }

    fun parseVideoUrl(html: String, onVideoUrlFound: (String) -> Unit) {
        Log.d(TAG, "Parsing HTML for video URLs...")

        try {
            // Parse video URLs from HTML
            val foundUrls = mutableSetOf<String>()

            // Check for video tags
            parseVideoTags(html, foundUrls)

            // Check for source tags
            parseSourceTags(html, foundUrls)

            // Check for iframe embeds
            parseIframeEmbeds(html, foundUrls)

            // Check for direct video URLs in text
            parseDirectVideoUrls(html, foundUrls)

            // Check for JavaScript video URLs
            parseJavaScriptVideoUrls(html, foundUrls)

            Log.d(TAG, "Found ${foundUrls.size} video URLs")

            // Return found URLs
            foundUrls.forEach { url ->
                Log.d(TAG, "Found video URL: $url")
                onVideoUrlFound(url)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing video URLs", e)
        }
    }

    private fun parseVideoTags(html: String, foundUrls: MutableSet<String>) {
        val videoTagPattern =
                Pattern.compile(
                        "<video[^>]*>.*?</video>",
                        Pattern.CASE_INSENSITIVE or Pattern.DOTALL
                )
        val srcPattern =
                Pattern.compile("src\\s*=\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE)

        val videoMatcher = videoTagPattern.matcher(html)
        while (videoMatcher.find()) {
            val videoTag = videoMatcher.group()
            val srcMatcher = srcPattern.matcher(videoTag)
            while (srcMatcher.find()) {
                val url = srcMatcher.group(1)
                if (url != null && isValidVideoUrl(url)) {
                    foundUrls.add(url)
                }
            }
        }
    }

    private fun parseSourceTags(html: String, foundUrls: MutableSet<String>) {
        val sourcePattern =
                Pattern.compile(
                        "<source[^>]*src\\s*=\\s*[\"']([^\"']+)[\"'][^>]*>",
                        Pattern.CASE_INSENSITIVE
                )

        val matcher = sourcePattern.matcher(html)
        while (matcher.find()) {
            val url = matcher.group(1)
            if (url != null && isValidVideoUrl(url)) {
                foundUrls.add(url)
            }
        }
    }

    private fun parseIframeEmbeds(html: String, foundUrls: MutableSet<String>) {
        val iframePattern =
                Pattern.compile(
                        "<iframe[^>]*src\\s*=\\s*[\"']([^\"']+)[\"'][^>]*>",
                        Pattern.CASE_INSENSITIVE
                )

        val matcher = iframePattern.matcher(html)
        while (matcher.find()) {
            val url = matcher.group(1)
            if (url != null && isVideoEmbedUrl(url)) {
                foundUrls.add(url)
            }
        }
    }

    private fun parseDirectVideoUrls(html: String, foundUrls: MutableSet<String>) {
        VIDEO_URL_PATTERNS.forEach { pattern ->
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(html)
            while (matcher.find()) {
                val url = matcher.group()
                if (isValidVideoUrl(url)) {
                    foundUrls.add(url)
                }
            }
        }
    }

    private fun parseJavaScriptVideoUrls(html: String, foundUrls: MutableSet<String>) {
        // Look for video URLs in JavaScript variables and strings
        val jsPatterns =
                arrayOf(
                        "(?:src|url|video)\\s*[:=]\\s*[\"']([^\"']+\\.(?:${VIDEO_EXTENSIONS.joinToString("|")})[^\"']*)[\"']",
                        "(?:src|url|video)\\s*[:=]\\s*[\"']([^\"']+\\.m3u8[^\"']*)[\"']",
                        "(?:src|url|video)\\s*[:=]\\s*[\"']([^\"']+\\.mpd[^\"']*)[\"']"
                )

        jsPatterns.forEach { pattern ->
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(html)
            while (matcher.find()) {
                val url = matcher.group(1)
                if (url != null && isValidVideoUrl(url)) {
                    foundUrls.add(url)
                }
            }
        }
    }

    private fun isValidVideoUrl(url: String): Boolean {
        if (url.isBlank()) return false

        // Check if it's a valid HTTP/HTTPS URL
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            return false
        }

        // Check if it has a video extension or is a streaming URL
        val lowerUrl = url.lowercase()
        return VIDEO_EXTENSIONS.any { ext -> lowerUrl.contains(".$ext") } ||
                lowerUrl.contains(".m3u8") ||
                lowerUrl.contains(".mpd") ||
                isVideoEmbedUrl(url)
    }

    private fun isVideoEmbedUrl(url: String): Boolean {
        val lowerUrl = url.lowercase()
        return lowerUrl.contains("youtube.com") ||
                lowerUrl.contains("youtu.be") ||
                lowerUrl.contains("vimeo.com") ||
                lowerUrl.contains("dailymotion.com") ||
                lowerUrl.contains("twitch.tv") ||
                lowerUrl.contains("facebook.com/watch") ||
                lowerUrl.contains("instagram.com")
    }
}
