package com.webvideocaster.network

import android.util.Log
import fi.iki.elonen.NanoHTTPD
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.net.NetworkInterface
import java.util.*

class LocalHttpServer(private val port: Int = 8080) : NanoHTTPD(port) {

    companion object {
        private const val TAG = "LocalHttpServer"
    }

    private var isServerRunning = false
    private val servedFiles = mutableMapOf<String, String>() // path -> file path mapping

    override fun start() {
        try {
            super.start()
            isServerRunning = true
            Log.d(TAG, "HTTP Server started on port $port")
            Log.d(TAG, "Server URL: http://${getLocalIpAddress()}:$port")
        } catch (e: IOException) {
            Log.e(TAG, "Failed to start HTTP server", e)
            throw e
        }
    }

    override fun stop() {
        if (isServerRunning) {
            super.stop()
            isServerRunning = false
            servedFiles.clear()
            Log.d(TAG, "HTTP Server stopped")
        }
    }

    fun getUrlForFile(filePath: String): String {
        if (!isServerRunning) {
            Log.w(TAG, "Server is not running")
            return ""
        }

        val file = File(filePath)
        if (!file.exists() || !file.isFile) {
            Log.w(TAG, "File does not exist: $filePath")
            return ""
        }

        // Generate a unique path for the file
        val fileName = file.name
        val uniquePath = "/file/${UUID.randomUUID()}/$fileName"

        // Store the mapping
        servedFiles[uniquePath] = filePath

        val serverUrl = "http://${getLocalIpAddress()}:$port$uniquePath"
        Log.d(TAG, "Generated URL for file $filePath: $serverUrl")

        return serverUrl
    }

    override fun serve(session: IHTTPSession): Response {
        val uri = session.uri
        Log.d(TAG, "Serving request: $uri")

        return try {
            when {
                uri == "/" -> serveIndexPage()
                uri.startsWith("/file/") -> serveFile(uri)
                else ->
                        newFixedLengthResponse(
                                Response.Status.NOT_FOUND,
                                MIME_PLAINTEXT,
                                "Not Found"
                        )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error serving request: $uri", e)
            newFixedLengthResponse(
                    Response.Status.INTERNAL_ERROR,
                    MIME_PLAINTEXT,
                    "Internal Server Error: ${e.message}"
            )
        }
    }

    private fun serveIndexPage(): Response {
        val html =
                """
            <!DOCTYPE html>
            <html>
            <head>
                <title>WebVideoCaster Local Server</title>
                <meta charset="UTF-8">
            </head>
            <body>
                <h1>WebVideoCaster Local HTTP Server</h1>
                <p>Server is running on port $port</p>
                <p>Use the app to cast local video files.</p>
            </body>
            </html>
        """.trimIndent()

        return newFixedLengthResponse(Response.Status.OK, "text/html", html)
    }

    private fun serveFile(uri: String): Response {
        val filePath = servedFiles[uri]
        if (filePath == null) {
            Log.w(TAG, "File not found for URI: $uri")
            return newFixedLengthResponse(
                    Response.Status.NOT_FOUND,
                    MIME_PLAINTEXT,
                    "File not found"
            )
        }

        val file = File(filePath)
        if (!file.exists() || !file.isFile) {
            Log.w(TAG, "Physical file not found: $filePath")
            servedFiles.remove(uri) // Clean up invalid mapping
            return newFixedLengthResponse(
                    Response.Status.NOT_FOUND,
                    MIME_PLAINTEXT,
                    "File not found"
            )
        }

        return try {
            val mimeType = getMimeTypeForFile(file)
            val fileInputStream = FileInputStream(file)

            Log.d(TAG, "Serving file: $filePath (${file.length()} bytes, MIME: $mimeType)")

            val response =
                    newFixedLengthResponse(
                            Response.Status.OK,
                            mimeType,
                            fileInputStream,
                            file.length()
                    )

            // Add headers for video streaming
            response.addHeader("Accept-Ranges", "bytes")
            response.addHeader("Content-Length", file.length().toString())
            response.addHeader("Cache-Control", "no-cache")

            response
        } catch (e: Exception) {
            Log.e(TAG, "Error serving file: $filePath", e)
            newFixedLengthResponse(
                    Response.Status.INTERNAL_ERROR,
                    MIME_PLAINTEXT,
                    "Error serving file: ${e.message}"
            )
        }
    }

    private fun getMimeTypeForFile(file: File): String {
        val fileName = file.name.lowercase()
        return when {
            fileName.endsWith(".mp4") -> "video/mp4"
            fileName.endsWith(".avi") -> "video/x-msvideo"
            fileName.endsWith(".mkv") -> "video/x-matroska"
            fileName.endsWith(".mov") -> "video/quicktime"
            fileName.endsWith(".wmv") -> "video/x-ms-wmv"
            fileName.endsWith(".flv") -> "video/x-flv"
            fileName.endsWith(".webm") -> "video/webm"
            fileName.endsWith(".m4v") -> "video/x-m4v"
            fileName.endsWith(".3gp") -> "video/3gpp"
            fileName.endsWith(".ogv") -> "video/ogg"
            else -> "application/octet-stream"
        }
    }

    private fun getLocalIpAddress(): String {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                if (!networkInterface.isLoopback && networkInterface.isUp) {
                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        if (!address.isLoopbackAddress &&
                                        address.hostAddress?.contains(':') == false
                        ) {
                            return address.hostAddress ?: "localhost"
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting local IP address", e)
        }
        return "localhost"
    }

    fun isRunning(): Boolean = isServerRunning

    fun getServerUrl(): String = "http://${getLocalIpAddress()}:$port"
}
