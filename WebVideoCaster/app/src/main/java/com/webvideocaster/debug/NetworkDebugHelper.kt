package com.webvideocaster.debug

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.util.Log
import java.net.InetAddress
import java.net.NetworkInterface

class NetworkDebugHelper(private val context: Context) {

    companion object {
        private const val TAG = "NetworkDebugHelper"
    }

    fun logNetworkInfo() {
        Log.d(TAG, "=== Network Debug Info ===")

        // Check WiFi connection
        checkWifiConnection()

        // Check network interfaces
        checkNetworkInterfaces()

        // Check multicast support
        checkMulticastSupport()

        Log.d(TAG, "=== End Network Debug Info ===")
    }

    private fun checkWifiConnection() {
        val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)

        if (networkCapabilities != null) {
            val hasWifi = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
            val hasInternet =
                    networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)

            Log.d(TAG, "WiFi connected: $hasWifi")
            Log.d(TAG, "Internet access: $hasInternet")

            if (hasWifi) {
                val wifiManager =
                        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as
                                WifiManager
                val wifiInfo = wifiManager.connectionInfo
                Log.d(TAG, "WiFi SSID: ${wifiInfo.ssid}")
                Log.d(TAG, "WiFi IP: ${formatIpAddress(wifiInfo.ipAddress)}")
            }
        } else {
            Log.w(TAG, "No active network connection")
        }
    }

    private fun checkNetworkInterfaces() {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                if (networkInterface.isUp && !networkInterface.isLoopback) {
                    Log.d(TAG, "Network Interface: ${networkInterface.name}")
                    Log.d(TAG, "  - Display Name: ${networkInterface.displayName}")
                    Log.d(TAG, "  - Supports Multicast: ${networkInterface.supportsMulticast()}")

                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        if (!address.isLoopbackAddress) {
                            Log.d(TAG, "  - Address: ${address.hostAddress}")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking network interfaces", e)
        }
    }

    private fun checkMulticastSupport() {
        val wifiManager =
                context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val multicastLock = wifiManager.createMulticastLock("DLNA_Discovery")

        try {
            multicastLock.acquire()
            Log.d(TAG, "Multicast lock acquired successfully")

            // Test multicast address reachability
            val multicastAddress = InetAddress.getByName("***************")
            Log.d(TAG, "Multicast address reachable: ${multicastAddress.isReachable(5000)}")
        } catch (e: Exception) {
            Log.e(TAG, "Error testing multicast support", e)
        } finally {
            if (multicastLock.isHeld) {
                multicastLock.release()
                Log.d(TAG, "Multicast lock released")
            }
        }
    }

    private fun formatIpAddress(ip: Int): String {
        return String.format(
                "%d.%d.%d.%d",
                ip and 0xff,
                ip shr 8 and 0xff,
                ip shr 16 and 0xff,
                ip shr 24 and 0xff
        )
    }
}
